import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UserAiStore } from '@core/stores';
import { DxTab, DxTabGroup } from '@dx-ui/ui';
import { ROLE } from '@shared/app.constant';
import { IAgentConfig, IAiConfig, IExportConfig } from '@shared/models';
import {
  AiConfigService,
  ExportConfigService,
  SettingsService,
} from '@shared/services';
import { StringUtils } from '@shared/utils';
import { AgentSettingsComponent } from './components/agent-settings/agent-settings.component';
import { AiSettingsComponent } from './components/ai-settings/ai-settings.component';
import { ExportSettingsComponent } from './components/export-settings/export-settings.component';
import { GatherInformationSettingsComponent } from './components/gather-information-settings/gather-information-settings.component';
import { HumanHandoffSettingsComponent } from './components/human-handoff-settings/human-handoff-settings.component';
import { LLMSettingsComponent } from './components/llm-settings/llm-settings.component';
import { SearchSettingsComponent } from './components/search-settings/search-settings.component';
import { UserSettingsComponent } from './components/user-settings/user-settings.component';
import { WidgetSettingsComponent } from './components/widget-settings/widget-settings.component';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [
    CommonModule,
    DxTabGroup,
    DxTab,
    AiSettingsComponent,
    AgentSettingsComponent,
    GatherInformationSettingsComponent,
    HumanHandoffSettingsComponent,
    WidgetSettingsComponent,
    SearchSettingsComponent,
    ExportSettingsComponent,
    UserSettingsComponent,
    LLMSettingsComponent,
  ],
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.css'],
})
export class SettingsComponent implements OnInit {
  selectedTab = signal<number>(0);
  settingData = signal<IAgentConfig>({});
  aiConfigData = signal<IAiConfig | undefined>(undefined);
  exportConfigData = signal<IExportConfig[]>([]);

  readonly ROLE = ROLE;

  userAiStore = inject(UserAiStore);
  private settingsService = inject(SettingsService);
  private aiConfigService = inject(AiConfigService);
  private exportConfigService = inject(ExportConfigService);
  private router = inject(Router);
  private activatedRoute = inject(ActivatedRoute);

  ngOnInit(): void {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      const tabSlug = params.get('tab');
      if (tabSlug) {
        const tabLabels = this.getTabLabels().map((label) =>
          StringUtils.slugify(label)
        );
        const index = tabLabels.indexOf(tabSlug);
        if (index > -1) {
          this.selectedTab.set(index);
        }
      }
    });

    this.settingsService.getDetailSetting().subscribe({
      next: (data: any) => this.settingData.set(data),
    });

    this.aiConfigService
      .getAiConfig({
        ai_id: this.userAiStore.currentAi()?.id,
      })
      .subscribe({
        next: (data: IAiConfig) => {
          if (data && Object.keys(data).length !== 0) {
            this.aiConfigData.set(data);
          } else {
            this.aiConfigData.set({
              ai_id: this.userAiStore.currentAiId() ?? '',
              threshold: 0.25,
              bot_style: '',
              show_source: true,
            });
          }
        },
      });

    this.exportConfigService.getExportConfig().subscribe({
      next: (data: IExportConfig[]) => this.exportConfigData.set(data),
    });
  }

  onTabChange(index: number) {
    this.selectedTab.set(index);
    const tabLabel = this.getTabLabels()[index];
    if (tabLabel) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { tab: StringUtils.slugify(tabLabel) },
        queryParamsHandling: 'merge',
      });
    }
  }

  private getTabLabels(): string[] {
    const labels = [
      'AI settings',
      'Human handoff',
      'Widget',
      'Search',
      'Export',
      'User',
    ];

    if (
      this.userAiStore.currentAi()?.role === ROLE.OWNER ||
      this.userAiStore.currentAi()?.role === ROLE.ADMIN
    ) {
      labels.splice(1, 0, 'Agent settings');
      labels.splice(4, 0, 'LLM');
      labels.splice(5, 0, 'Gather information');
    }

    return labels;
  }
}
