.cs-drawer-wrapper {
  height: 90.9vh;
}

.cs-drawer-wrapper .drawer-header {
  height: 80px;
  padding-left: 1rem;
  padding-right: 1rem;
}

.cs-drawer-wrapper .drawer-header .event-info {
  margin-right: 1rem;
  margin-bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 2rem;
  font-weight: 700;
}

.cs-drawer-wrapper .drawer-header .event-train {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-right: 1rem;
  margin-bottom: 0;
}

.list-event {
  width: 20%;
  min-width: 200px;
  height: 100%;
  overflow-y: auto;
}

.event-item {
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  position: relative;
  margin-bottom: 0;
}

.event-item.selected, .event-item:hover {
  background-color: #f5f5f5;
}

.event-item.activate {
  background-color: rgba(42, 134, 255, 0.05);
}

.border-bottom-gray {
  border-bottom: 1px solid #e5e5e5;
}

.border-light-gray {
  border: 1px solid #e5e5e5;
}

.background-bold {
  background-color: #fff;
  color: #000;
}

.background-light {
  background-color: #f5f5f5;
}

::ng-deep .create-tool-select .mat-mdc-form-field-flex {
  border: 1px solid #e5e5e5 !important;
}

.result-event-test {
  width: 100%;
  height: calc(100vh - 300px);
}

.editor-container {
  height: 100%;
}

ngx-monaco-editor {
  height: 100% !important;
}

@media (max-width: 1023px) {
  .sliding-panel {
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    min-width: 400px;
    height: 100%;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    z-index: 10;
    background-color: #fff;
    visibility: hidden;
    opacity: 0;
    display: none;
  }

  .sliding-panel.visible {
    transform: translateX(0);
    visibility: visible;
    opacity: 1;
    display: block;
  }

  .sliding-panel.hidden {
    display: block;
    animation: slide-out 0.3s ease-in-out forwards;
  }

  @keyframes slide-out {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(100%);
    }
  }
}


.list-events {
  max-height: calc(100dvh - 201px);
}

.list-events::-webkit-scrollbar {
  display: none;
}

