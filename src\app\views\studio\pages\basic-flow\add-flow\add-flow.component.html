<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      {{
        data.isClone ? "Clone flow" : !data.isEdit ? "Create flow" : "Edit flow"
      }}
    </div>
    <div class="flex items-center justify-end space-x-4">
      <app-svg-icon
        type="icClose"
        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></app-svg-icon>
    </div>
  </div>

  <div class="flex-1 overflow-y-auto mt-18 mb-20">
    <div
      [formGroup]="formGroup"
      class="px-6 pt-6 pb-[3px] flex flex-col space-x-4"
    >
      <dx-form-field class="w-full">
        <dx-label class="text-sm">Name</dx-label>
        <input
          dxInput
          formControlName="name"
          [type]="'text'"
          placeholder="Enter name"
        />
        @if (formGroup.get('name')?.errors &&
        formGroup.get('name')?.errors?.['required'] &&
        (formGroup.get('name')?.touched || formGroup.get('name')?.dirty)) {
        <dx-error>Name is required.</dx-error>
        }
      </dx-form-field>
      <dx-form-field class="w-full">
        <dx-label class="text-sm">Description</dx-label>
        <textarea
          dxInput
          rows="3"
          autoResize
          formControlName="description"
          [type]="'text'"
          placeholder="Enter description"
        ></textarea>
        @if (formGroup.get('description')?.errors &&
        formGroup.get('description')?.errors?.['required'] &&
        (formGroup.get('description')?.touched ||
        formGroup.get('description')?.dirty)) {
        <dx-error>Description is required.</dx-error>
        }
      </dx-form-field>
      <dx-form-field class="w-full">
        <dx-label class="text-sm">Type</dx-label>
        <dx-select formControlName="trigger_type">
          @for (type of listTriggerType; track $index) {
          <dx-option [value]="type.value">
            {{ type.label }}
          </dx-option>
          }
        </dx-select>
        @if (formGroup.get('type')?.errors &&
        formGroup.get('type')?.errors?.['required'] &&
        (formGroup.get('type')?.touched || formGroup.get('type')?.dirty)) {
        <dx-error>Type is required.</dx-error>
        }
      </dx-form-field>
    </div>
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Cancel</button>
    <button
      dxLoadingButton="filled"
      [loading]="isSubmitting()"
      [disabled]="formGroup.invalid"
      (click)="onSave(data.isEdit)"
    >
      {{ data.isEdit ? "Update" : data.isClone ? "Save" : "Create" }}
    </button>
  </div>
</div>
