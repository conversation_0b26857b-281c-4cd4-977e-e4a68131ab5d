// @ts-nocheck
import GoTo<PERSON><PERSON>Handle from "@flow-editor/components/flow/GoToBlockHandle";
import InputField from "@flow-editor/components/form/InputField";
import SelectField from "@flow-editor/components/form/SelectField";
import NodeHeader from "@flow-editor/components/nodes/NodeHeader";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor/components/styled";
import NodeTooltip from "@flow-editor/components/tooltip/NodeTooltip";
import { STUDIO_STATUS } from "@flow-editor/constant";
import { useFlowInstance } from "@flow-editor/hook";
import { EventTriggerNodeData, LayoutState } from "@flow-editor/model";
import {
  useBuildFlowState,
  useEventState,
  useFlowInstanceState,
  useLayoutState,
  useStudioState
} from "@flow-editor/store";
import { isValidConnection } from "@flow-editor/utils/flow";
import { hexToRgb } from "@flow-editor/utils/util";
import { yupResolver } from "@hookform/resolvers/yup";
import { Divider, Modal, Typography } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Handle, Position, useReactFlow } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";

const StyledHandleSourceAnchor = styled.div<{ $bgColor, $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => props.$data ? 'white' : `rgba(255, 255, 255, 0.5)`};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => props.$data ? 'normal' : 'italic'};
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const StyledHandleSourceAnchorStatus = styled.div<{ $type? }>`
  margin-top: 8px;
  padding: 0 6px;
  text-align: start;
  color: ${(props) => props.$type === 'success' ? '#66b266' : props.$type === 'fallback' ? '#ff6666' : props.$type === 'error' ? '#fa6505' :'white'};
  font-size: 10px;
  font-weight: 600;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: end;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const eventTriggerNodeDataFormSchema = yup.object().shape({
  event_id: yup.string().required('Event is required'),
});

const EventTriggerNode = ({data}: { data: EventTriggerNodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const {flowInstance} = useFlowInstanceState(state => state);
  const {events} = useEventState<EventState>(state => state);
  const {flow, setDirty} = useBuildFlowState(state => state);
  const {status} = useStudioState(state => state);
  const [selectedItems, setSelectedItems] = useState([]);
  const {getEdges, setEdges} = useReactFlow();
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const {theme} = useLayoutState<LayoutState>(state => state);
  const {
    control,
    setValue,
    handleSubmit,
    reset
  } = useForm({
    resolver: yupResolver(eventTriggerNodeDataFormSchema)
  })

  const resetForm = () => {
    reset()
  }

  const onSubmit = (formValue) => {
    data.event_id = formValue?.event_id;

    resetForm();
    setModalOpen(false);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  }

  useEffect(() => {
    resetForm();
    data.event_id && setValue('event_id', data.event_id);
  }, [JSON.stringify(data), JSON.stringify(events)]);

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    setModalOpen(true);
  }, []);

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $theme={theme}>
      <div className="absolute left-0 text-[10px]" style={{top: -20, color: '#D4D9E2'}}>{data.id}</div>
      {
        status && status === STUDIO_STATUS.DEV && (
          <NodeTooltip data={data} selected={data.selected}/>
        )
      }
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black'
          }}
        />
        <StyledHandleSourceAnchor $bgColor={data.node_color} $data={!!(data.event_id)} onDoubleClick={handleOpenModal}>
          {
            data.event_id ? events.find(event => event.id === data.event_id) ? events.find(event => event.id === data.event_id).name : 'Configure' : 'Configure'
          }
        </StyledHandleSourceAnchor>
        {
          !data.goToBlockSource ? (
            <Handle
              type="source"
              position={Position.Right}
              id={`${data.id}_source#1`}
              style={{
                height: 10,
                width: 10,
                backgroundColor: theme === 'dark' ? 'white' : 'black',
              }}
            />
          ) : (() => {
              const key = `${data.id}_source#1`
              const goToBlock = data.goToBlockSource.find(v => v.sourceHandle === key)
              if (goToBlock) {
                return <GoToBlockHandle
                  data={data}
                  sourceHandle={key}
                  style={{
                    height: 14,
                    width: 84,
                    right: -80,
                    backgroundColor: theme === 'dark' ? 'white' : 'black',
                    fontSize: 10,
                    color: 'black',
                    border: 'none',
                    borderRadius: 10,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onMouseEnter={() => setIsHoverGoToBlock(true)}
                  onMouseLeave={() => setIsHoverGoToBlock(false)}
                />
              }

              return <Handle
                type="source"
                position={Position.Right}
                id={`${data.id}_source#1`}
                style={{
                  height: 10,
                  width: 10,
                  backgroundColor: theme === 'dark' ? 'white' : 'black',
                }}
              />
            }
          )()
        }
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col space-y-4 mt-6">
          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Select Event</Typography.Text>
            <SelectField name={'event_id'} disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                         options={events.map(v => ({
                           ...v,
                           key: v.id,
                           value: v.id,
                           label: v.name
                         }))}/>
          </div>

          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Output key</Typography.Text>
            <InputField
              type={"input"}
              name={`output_key`}
              control={control}
              setValue={setValue}
            />
          </div>

          <Divider className="!border-primary-border dark:!border-dark-primary-border"/>

          <div className="w-full flex justify-end items-center space-x-4">
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() => setModalOpen(false)}
            >
              Cancel
            </StyledButtonModal>
            <StyledButtonModal
              type="submit"
              key="save"
              $bgColor={"#7F75CF"}
              $color={"white"}
              disabled={status && status === STUDIO_STATUS.LIVE}
            >
              Save
            </StyledButtonModal>
          </div>
        </form>
      </Modal>
    </StyledNodeFlow>
  );
};

export default EventTriggerNode;
