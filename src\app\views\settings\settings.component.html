<div class="flex flex-col justify-between">
  <h1
    class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
  >
    Settings
  </h1>

  <dx-tab-group
    class="mt-6"
    [selectedIndex]="selectedTab()"
    (selectedIndexChange)="onTabChange($event)"
  >
    <dx-tab label="AI settings">
      <app-ai-settings class="h-full overflow-y-auto"
        [settings]="settingData()"
        [aiConfig]="aiConfigData()"
      ></app-ai-settings>
    </dx-tab>

    @if ( userAiStore.currentAi()?.role === ROLE.OWNER ||
    userAiStore.currentAi()?.role === ROLE.ADMIN ) {
    <dx-tab label="Agent settings">
      <app-agent-settings [settings]="settingData()"></app-agent-settings>
    </dx-tab>
    }

    <dx-tab label="Human handoff" >
      <app-human-handoff-settings
        [settings]="settingData()"
      ></app-human-handoff-settings>
    </dx-tab>

    <dx-tab label="Widget">
      <app-widget-settings [settings]="settingData()"></app-widget-settings>
    </dx-tab>

    @if ( userAiStore.currentAi()?.role === ROLE.OWNER ||
    userAiStore.currentAi()?.role === ROLE.ADMIN ) {
    <dx-tab label="LLM">
      <app-llm-settings [settings]="settingData()"></app-llm-settings>
    </dx-tab>
    } @if ( userAiStore.currentAi()?.role === ROLE.OWNER ||
    userAiStore.currentAi()?.role === ROLE.ADMIN ) {
    <dx-tab label="Gather information">
      <app-gather-information-settings
        [settings]="settingData()"
      ></app-gather-information-settings>
    </dx-tab>
    }

    <dx-tab label="Search">
      <app-search-settings [settings]="settingData()"></app-search-settings>
    </dx-tab>

    <dx-tab label="Export">
      <app-export-settings
        [exportConfig]="exportConfigData()"
      ></app-export-settings>
    </dx-tab>

    <dx-tab label="User">
      <app-user-settings></app-user-settings>
    </dx-tab>
  </dx-tab-group>
</div>
