import { Platform } from '@angular/cdk/platform';
import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  effect,
  inject,
  input,
  signal,
} from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import {
  DxFormField,
  DxInput,
  DxLabel,
  DxOption,
  DxSelect,
  DxSlider,
  DxSliderThumb,
  DxSnackBar,
  DxTooltip,
} from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { heroInformationCircle } from '@ng-icons/heroicons/outline';
import { SettingsService } from '@shared/services';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  Subject,
  takeUntil,
} from 'rxjs';

@Component({
  selector: 'app-llm-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DxTooltip,
    DxFormField,
    DxLabel,
    DxInput,
    DxSelect,
    DxOption,
    DxSlider,
    DxSliderThumb,
    NgIconsModule,
  ],
  templateUrl: './llm-settings.component.html',
  providers: [provideIcons({ heroInformationCircle })],
})
export class LLMSettingsComponent {
  settings = input<any>();

  listModel = signal([
    { code: 'gpt-4.1', name: 'gpt-4.1' },
    { code: 'gpt-4.1-mini', name: 'gpt-4.1-mini' },
    { code: 'gpt-4.1-nano', name: 'gpt-4.1-nano' },
    { code: 'gpt-4o', name: 'gpt-4o' },
    { code: 'gpt-4o-mini', name: 'gpt-4o-mini' },
    { code: 'gpt-3.5-turbo-0125', name: 'gpt-3.5-turbo-0125' },
    { code: 'gemini-1.5-flash', name: 'gemini-1.5-flash' },
  ]);
  listModelOptions = computed(() =>
    this.listModel().map((model) => ({
      value: model.code,
      label: model.name,
    }))
  );

  private destroy$ = new Subject<void>();

  private platform = inject(Platform);
  private snackBar = inject(DxSnackBar);
  private settingsService = inject(SettingsService);

  // FORM CONTROL
  modelRag = new FormControl<string>('gpt-4o-mini');
  modelCF = new FormControl<string>('gpt-4o-mini');
  modelDI = new FormControl<string>('gpt-4o-mini');
  temperatureRag = new FormControl<number>(0);
  temperatureCF = new FormControl<number>(0);
  temperatureDI = new FormControl<number>(0);
  limit = new FormControl<number>(0);
  threshold = new FormControl<number>(0);

  constructor() {
    effect(() => {
      const currentSettings = this.settings();
      if (currentSettings) {
        this.updateSettingsFormControls(currentSettings);
      }
    });
  }

  ngOnInit(): void {
    this.initializeFormSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  updateTextAreaHeight(id: string) {
    if (this.platform.isBrowser) {
      const textarea = document.getElementById(id);
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 32 + 'px';
      }
    }
  }

  private updateSettingsFormControls(settings: any) {
    this.modelRag.setValue(settings?.settings?.llm_setting?.check_rag?.model, {
      emitEvent: false,
    });
    this.modelCF.setValue(settings?.settings?.llm_setting?.check_fit?.model, {
      emitEvent: false,
    });
    this.modelDI.setValue(
      settings?.settings?.llm_setting?.check_detect_intent?.model,
      { emitEvent: false }
    );
    this.temperatureRag.setValue(
      settings?.settings?.llm_setting?.check_rag?.temperature,
      { emitEvent: false }
    );
    this.temperatureCF.setValue(
      settings?.settings?.llm_setting?.check_fit?.temperature,
      { emitEvent: false }
    );
    this.temperatureDI.setValue(
      settings?.settings?.llm_setting?.check_detect_intent?.temperature,
      { emitEvent: false }
    );
    this.limit.setValue(settings?.settings?.llm_setting?.check_rag?.limit, {
      emitEvent: false,
    });
    this.threshold.setValue(
      settings?.settings?.llm_setting?.check_rag?.threshold,
      { emitEvent: false }
    );
  }

  private subscribeToFormControlWithHandler(
    control: FormControl,
    handler: (value: any) => void,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    let stream = control.valueChanges.pipe(
      takeUntil(this.destroy$),
      distinctUntilChanged(),
      debounceTime(300)
    );

    if (shouldFilter) {
      stream = stream.pipe(filter(shouldFilter));
    }

    stream.subscribe({ next: handler });
  }

  private subscribeToFormControlSettings(
    control: FormControl,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    this.subscribeToFormControlWithHandler(
      control,
      this.buildUpdatedSettings.bind(this),
      shouldFilter
    );
  }

  private initializeFormSubscriptions() {
    this.subscribeToFormControlSettings(this.modelRag);
    this.subscribeToFormControlSettings(this.modelCF);
    this.subscribeToFormControlSettings(this.modelDI);
    this.subscribeToFormControlWithHandler(
      this.temperatureRag,
      this.handleTemperatureRagChange.bind(this),
      (value: number) => value >= 0 && value <= 1
    );
    this.subscribeToFormControlWithHandler(
      this.temperatureCF,
      this.handleTemperatureCFChange.bind(this),
      (value: number) => value >= 0 && value <= 1
    );
    this.subscribeToFormControlWithHandler(
      this.temperatureDI,
      this.handleTemperatureDIChange.bind(this),
      (value: number) => value >= 0 && value <= 1
    );
    this.subscribeToFormControlSettings(
      this.limit,
      (value: number) => value >= 0 && value <= 1
    );
    this.subscribeToFormControlWithHandler(
      this.threshold,
      this.handleThresholdChange.bind(this),
      (value: number) => value >= 0 && value <= 1
    );
  }

  private handleTemperatureRagChange(value: number) {
    this.buildUpdatedSettings();
    this.temperatureRag.setValue(value, { emitEvent: false });
  }

  private handleTemperatureCFChange(value: number) {
    this.buildUpdatedSettings();
    this.temperatureCF.setValue(value, { emitEvent: false });
  }

  private handleTemperatureDIChange(value: number) {
    this.buildUpdatedSettings();
    this.temperatureDI.setValue(value, { emitEvent: false });
  }

  private handleThresholdChange(value: number) {
    this.buildUpdatedSettings();
    this.threshold.setValue(value, { emitEvent: false });
  }

  private buildUpdatedSettings(): void {
    const currentSettings = this.settings()?.settings;
    const updatedSettings = {
      ...currentSettings,
      llm_setting: {
        ...currentSettings?.llm_setting,
        check_rag: {
          ...currentSettings?.llm_setting?.check_rag,
          model: this.modelRag.value,
          temperature: this.temperatureRag.value,
          limit: this.limit.value,
          threshold: this.threshold.value,
        },
        check_fit: {
          ...currentSettings?.llm_setting?.check_fit,
          model: this.modelCF.value,
          temperature: this.temperatureCF.value,
        },
        check_detect_intent: {
          ...currentSettings?.llm_setting?.check_detect_intent,
          model: this.modelDI.value,
          temperature: this.temperatureDI.value,
        },
      },
    };
    this.updateSettings(updatedSettings);
  }

  private updateSettings(data: any) {
    this.settingsService.updateSetting(data).subscribe({
      next: (res) => {
        if (res) {
          this.snackBar.open(res.message, '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        }
      },
      error: (err) => {
        this.snackBar.open(err.error.detail, '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
    });
  }
}
