import { ROLE } from '@shared/app.constant';
import {IAgentSetting, ISearchSetting} from '@shared/models/llm-config.model';
import {IPlanFeature} from '@shared/models/plan.model';

export interface ISettingDetail {
  id: string;
  name: string;
  description: string;
  settings: any;
  created_at: string;
  updated_at: string;
  role: ROL<PERSON>;
  default: boolean;
  plan_of_owner: string;
  plan_features: any[];
}
export interface ISetting {}


export interface IAgentConfig {
  id?: string;
  name?: string;
  description?: string;
  settings?: ISettings;
  created_at?: string;
  updated_at?: string;
  role?: string;
  default?: boolean;
  plan_of_owner?: string;
  plan_features?: IPlanFeature[];
}

export interface ISettings {
  basic?: IBasicSettings;
  languages?: string[];
  human_handoff?: IHumanHandoff;
  widget?: IWidgetSettings;
  llm_setting?: ILlmSetting;
  search_setting?: ISearchSetting;
  agent_setting?: IAgentSetting;
}

export interface IBasicSettings {
  ask_for_email?: IEnablePosition;
  using_emoji?: IEnable;
  use_agent?: IEnable;
}

export interface IEnable {
  enabled?: number;
}

export interface IEnablePosition extends IEnable {
  position?: any;
}

export interface IHumanHandoff {
  topic?: string | null;
  business?: string | null;
  custom_prompt?: string | null;
  enabled?: number;
  email_notifications?: {
    enabled?: number;
    emails?: string[];
  };
  summary?: {
    enabled?: number;
  };
  message?: {
    confirmation?: string;
    outside_working_hours?: string;
    question?: string;
  };
  only_human?: {
    enabled?: number;
  };
  working_hours?: {
    enabled?: number;
    days?: string[];
    hours?: string[];
    timezone?: string;
  };
  reply_length?: number;
  num_sentences_bot_answer_not_topic?: number;
}

export interface IWidgetSettings {
  welcome_message?: string;
  input_placeholder?: string;
  logo_url?: string;
  header?: string;
  whitelist?: string[];
}

export interface ILlmSetting {
  check_detect_intent?: ILLMModel;
  check_rag?: IRagModel;
  check_fit?: ILLMModel;
}

export interface ILLMModel {
  model?: string;
  temperature?: number;
}

export interface IRagModel extends ILLMModel {
  limit?: number;
  threshold?: number;
}

// export interface ISearchSetting {
//   enabled?: boolean;
//   prefer_sites?: string[];
//   exclude_sites?: string[];
//   full_text?: boolean;
//   llm_answer?: ILLMModel;
// }

// export interface IAgentSetting {
//   rag?: {
//     description?: string;
//     role?: string;
//   };
//   gather_information?: {
//     enabled?: boolean;
//     ai_model?: string;
//     question_count?: number;
//     webhook_set_user_data?: string;
//     default_parameters?: IParameter[];
//     parameters?: IParameter[];
//   };
// }

export interface IParameter {
  key?: string;
  type?: string;
  description?: string;
  note?: string;
  is_required?: boolean;
}

/*export interface IPlanFeature {
  id?: number;
  name?: string;
  type?: string;
  value?: string;
}*/
