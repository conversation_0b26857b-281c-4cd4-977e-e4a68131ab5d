import {Component, inject, OnInit, signal} from '@angular/core';
import {ConfirmDialogComponent, DataTableComponent, IColumn} from '@shared/components';
import {DatePipe, Ng<PERSON>lass, Ng<PERSON>tyle, Ng<PERSON><PERSON>, Ng<PERSON>witchCase, NgSwitchDefault} from '@angular/common';
import {NgIcon, provideIcons} from '@ng-icons/core';
import {FormBuilder, FormsModule} from '@angular/forms';
import {DIALOG_DATA, DxButton, DxDialog, DxDialogRef} from '@dx-ui/ui';
import {AiLlmService} from '@shared/services';
import {heroPencilSquare, heroXMark} from '@ng-icons/heroicons/outline';
import {AddOrEditApiKeyComponent} from '@views/admin/pages/list-user/add-or-edit-api-key/add-or-edit-api-key.component';
import {CdkConnectedOverlay, CdkOverlayOrigin} from '@angular/cdk/overlay';
import {ClickOutsideDirective} from '@shared/directives';

@Component({
  selector: 'app-edit-ai-user',
  imports: [
    DataTableComponent,
    NgIcon,
    NgStyle,
    NgSwitchDefault,
    NgSwitch,
    NgSwitchCase,
    DatePipe,
    DxButton,
    FormsModule,
    CdkOverlayOrigin,
    CdkConnectedOverlay,
    ClickOutsideDirective
  ],
  providers: [
    provideIcons({
      heroXMark,
      heroPencilSquare
    })
  ],
  templateUrl: './edit-ai-user.component.html',
  styleUrl: './edit-ai-user.component.css',
  host: {
    class: 'h-full'
  }
})
export class EditAiUserComponent implements OnInit {

  isLoading = signal<boolean>(false);

  dropdownOpen = false;
  listLLM = signal<any[]>([]);
  columnsLLM: IColumn[] = [
    {
      columnDef: 'index',
      headerName: 'No.',
      flex: 0.1,
      minWidth: '40px',
    },
    {
      columnDef: 'LLM_name',
      headerName: 'Name',
      flex: 0.3,
      minWidth: '40px',
    },
    {
      columnDef: 'api_key',
      headerName: 'Api key',
      flex: 0.3,
      minWidth: '40px',
    },
    {
      columnDef: 'created_at',
      headerName: 'Created at',
      flex: 0.3,
      minWidth: '40px',
    },
    {
      columnDef: 'updated_at',
      headerName: 'Updated at',
      flex: 0.3,
      minWidth: '40px',
    },
    {
      columnDef: 'action_edit',
      headerName: 'Action',
      flex: 0.2,
      minWidth: '120px',
      alignHeader: 'center',
      align: 'center',
    },
  ];
  aiChangeLLMId: number = 0;

  fb = inject(FormBuilder);
  dialogRef = inject(DxDialogRef<EditAiUserComponent>);
  data: { aiId: number | string } = inject(DIALOG_DATA);
  dialogService = inject(DxDialog);
  private aiLLMService: AiLlmService = inject(AiLlmService);

  ngOnInit(): void {
    this.getListApiKey();
  }

  addOrEditLLM(llm?: any, ai?: any) {
    this.dialogService.open(AddOrEditApiKeyComponent, {
      data: {
        AIId: ai.aiId,
        llmName: llm.LLM_name ?? '',
        apiKey: llm.api_key ?? '',
        id: llm.id ?? 0,
      },
      width: '30vw',
    }).afterClosed().subscribe(() => {
      this.aiLLMService.getListLLM(this.data.aiId).subscribe((res) => {
        this.listLLM.set(res);
      });
    });
  }

  checkDuplicate(LLM_name: string): boolean {
    if (!this.listLLM()) {
      return false;
    }
    return this.listLLM()?.some((item: any) => item.LLM_name === LLM_name);
  }


  deleteLLM(row: any) {
    this.dialogService.open(ConfirmDialogComponent,
      {
        data: {
          title: 'Delete this LLM',
          content: `The chatBot will not be able to chat using ${row.LLM_name} if this key is deleted. Are you sure you want to delete it?`,
          isDelete: true,
        },
        width: '300px',
      }
    ).afterClosed().subscribe((value: any) => {
      if (value) {
        this.aiLLMService.deleteLLM(row.id).subscribe({
          next: () => {
            this.aiLLMService.getListLLM(this.aiChangeLLMId).subscribe({
              next: (res) => {
                this.listLLM.set(res);
              },
              error: () => {
              }
            });
          },
          error: () => {
          }
        });
      }
    });
  }

  getListApiKey() {
    this.aiLLMService.getListLLM(this.data.aiId).subscribe({
      next: (res) => {
        this.listLLM.set(res);
      },
      error: () => {
      },
      complete: () => {
      }
    });
  }
}
