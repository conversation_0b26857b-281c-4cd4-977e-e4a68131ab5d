import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule, NgOptimizedImage } from '@angular/common';
import {
  Component,
  effect,
  inject,
  OnInit,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroBoltMini,
  heroEllipsisHorizontalMini,
  heroPencilSquareMini,
  heroPlusMini,
  heroTrashMini,
} from '@ng-icons/heroicons/mini';
import {
  ConfirmDialogComponent,
  DataTableComponent,
  IColumn,
} from '@shared/components';

import { STUDIO_STATUS } from '@core/constants';
import { StudioStore } from '@core/stores';
import { DxButton, DxDialog, DxFormField, DxInput, DxPrefix } from '@dx-ui/ui';
import { IAgentDev, IAgentDevFilter } from '@shared/models';
import { IAgent, IAgentFilter } from '@shared/models/agent.model';
import { ParseJsonPipe } from '@shared/pipes';
import { AgentDevService, AgentService } from '@shared/services';
import { AddOrEditAgentComponent } from '@views/studio/pages/agent/add-or-edit-agent/add-or-edit-agent.component';
import { DetailAgentComponent } from '@views/studio/pages/agent/detail-agent/detail-agent.component';

@Component({
  selector: 'app-agent',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DataTableComponent,
    MatInputModule,
    NgIconsModule,
    DragDropModule,
    ParseJsonPipe,
    DxButton,
    DxPrefix,
    DxFormField,
    DxInput,
    NgOptimizedImage,
  ],
  templateUrl: './agent.component.html',
  styleUrl: './agent.component.css',
  providers: [
    provideIcons({
      heroPlusMini,
      heroPencilSquareMini,
      heroTrashMini,
      heroEllipsisHorizontalMini,
      heroBoltMini,
    }),
  ],
})
export class AgentComponent implements OnInit {
  agentDetailsDialog = viewChild<TemplateRef<any> | null>('agentDetailsDialog');

  searchModel: IAgentDevFilter | IAgentFilter = {
    keyword: '',
    page: 1,
    page_size: 10,
    sort_by: 'id',
    direction: 'desc',
  };
  agents = signal<Array<IAgentDev | IAgent>>([]);
  totalAgents = signal<number>(0);
  isSubmitting = signal<boolean>(false);
  aiModels = signal<any[]>([
    { value: 'gpt-4o-mini', type: 'openai', label: 'gpt-4o-mini' },
    { value: 'gpt-4o', type: 'openai', label: 'gpt-4o' },
    { value: 'gpt-4.1-mini', type: 'openai', label: 'gpt-4.1-mini' },
    { value: 'gpt-4.1-nano', type: 'openai', label: 'gpt-4.1-nano' },
    { value: 'gpt-4.1', type: 'openai', label: 'gpt-4.1' },
    { value: 'gemini-1.5-flash', type: 'gemini', label: 'gemini-1.5-flash' },
    { value: 'gemini-2.0-flash', type: 'gemini', label: 'gemini-2.0-flash' },
  ]);
  columns = signal<IColumn[]>([]);

  modelConfig: { name: string; temperature: number } = {
    name: 'openai',
    temperature: 0,
  };
  readonly STUDIO_STATUS = STUDIO_STATUS;
  dialog = inject(DxDialog);
  studioStore = inject(StudioStore);
  private agentDevService = inject(AgentDevService);
  private agentService = inject(AgentService);

  constructor() {
    effect(() => {
      const status = this.studioStore.status();
      const baseColumns: IColumn[] = [
        {
          columnDef: 'name',
          headerName: 'Name',
          flex: 2,
        },
        {
          columnDef: 'description',
          headerName: 'Description',
          flex: 3,
          maxWidth: '200px',
        },
        {
          columnDef: 'llm_type',
          headerName: 'AI Model',
          flex: 2,
        },
      ];

      if (status === this.STUDIO_STATUS.DEV) {
        this.columns.set([
          ...baseColumns,
          {
            columnDef: 'action',
            headerName: 'Actions',
            flex: 1,
            actions: [
              {
                case: 'view',
                name: 'View',
                title: 'View',
              },
              {
                case: 'edit',
                name: 'Edit',
                title: 'Edit',
              },
              {
                case: 'delete',
                name: 'Delete',
                title: 'Delete',
              },
            ],
          },
        ]);
      }

      if (status === this.STUDIO_STATUS.LIVE) {
        this.columns.set([
          ...baseColumns,
          {
            columnDef: 'action',
            headerName: 'Actions',
            flex: 1,
            actions: [
              {
                case: 'view',
                name: 'View',
                title: 'View',
              },
              {
                case: 'delete',
                name: 'Delete',
                title: 'Delete',
              },
            ],
          },
        ]);
      }

      this.getAgents();
    });
  }

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    // Set initial model config name
    this.modelConfig.name = this.aiModels()[0].value;
  }

  getAgents(): void {
    if (this.studioStore.status() === STUDIO_STATUS.DEV) {
      this.agentDevService.findBySearch(this.searchModel).subscribe({
        next: (response) => {
          this.agents.set(response.items);
          this.totalAgents.set(response.total);
        },
      });
    }
    if (this.studioStore.status() === STUDIO_STATUS.LIVE) {
      this.agentService.findBySearch(this.searchModel).subscribe({
        next: (response) => {
          this.agents.set(response.items);
          this.totalAgents.set(response.total);
        },
      });
    }
  }

  onPageChange(event: any): void {
    this.searchModel = {
      ...this.searchModel,
      page: event.pageIndex + 1,
      page_size: event.pageSize,
    };
    this.getAgents();
  }

  doSearch(): void {
    this.searchModel = {
      ...this.searchModel,
      keyword: this.searchModel.keyword,
    };
    this.getAgents();
  }

  onAction(event: any): void {
    const { type, data } = event;
    switch (type) {
      case 'view':
        this.onView(data);
        break;
      case 'edit':
        this.onEdit(data);
        break;
      case 'delete':
        this.onDelete(data);
        break;
      default:
        break;
    }
  }

  openCreateDialog(): void {
    /*
      this.formGroup.patchValue({
        llm_type: this.aiModels()[0].type,
        temperature: 0,
        selectedModel: this.aiModels()[0].value,
      });
      this.modelConfig = {
      name: this.aiModels()[0].value,
      temperature: 0,
    };*/
    this.dialog
      .open(AddOrEditAgentComponent, {
        height: '80dvh',
        width: '50dvw',
        data: { isEdit: false },
      })
      .afterClosed()
      .subscribe(() => {
        this.getAgents();
      });
  }

  onView(agent: IAgentDev | IAgent): void {
    this.dialog.open(DetailAgentComponent, {
      width: '50dvw',
      data: { agent },
    });
  }

  onEdit(agent: IAgentDev): void {
    let temperature = 0;
    let modelConfigName = '';

    // Parse temperature and name from model_config
    try {
      if (agent.model_config) {
        const modelCfg = JSON.parse(agent.model_config);
        this.modelConfig = {
          name: modelCfg.name || this.aiModels()[0].value,
          temperature: modelCfg.temperature || 0,
        };
        temperature = modelCfg.temperature || 0;
        modelConfigName = modelCfg.name || '';
      } else {
        this.modelConfig = {
          name: this.aiModels()[0].value,
          temperature: 0,
        };
      }
    } catch (e) {
      temperature = 0;
      modelConfigName = '';
      this.modelConfig = {
        name: this.aiModels()[0].value,
        temperature: 0,
      };
    }

    // Find the model using model_config.name instead of llm_type
    const modelForName = this.aiModels().find(
      (model) => model.value === modelConfigName
    );
    let selectedValue = modelForName?.value || this.aiModels()[0].value;

    this.dialog.open(AddOrEditAgentComponent, {
      width: '50vw',
      data: {
        id: agent.id,
        ai_id: agent.ai_id,
        name: agent.name,
        description: agent.description,
        llm_type: agent.llm_type,
        instruction: agent.instruction,
        model_config: this.modelConfig,
        rule: agent.rule,
        temperature: temperature,
        selectedModel: selectedValue,
        isEdit: true,
      },
    });
  }

  onDelete(agent: IAgentDev | IAgent): void {
    if (agent.id) {
      const id = agent.id;
      this.dialog
        .open(ConfirmDialogComponent, {
          data: {
            title: 'Delete Agent',
            message: `Are you sure you want to delete agent "${agent.name}"?`,
            confirmText: 'Delete',
            cancelText: 'Cancel',
            isDelete: true,
          },
        })
        .afterClosed()
        .subscribe((result: any) => {
          if (!!result && this.studioStore.status() === STUDIO_STATUS.DEV) {
            this.agentDevService.delete(id).subscribe({
              next: () => {
                this.getAgents();
              },
            });
          }
          if (result && this.studioStore.status() === STUDIO_STATUS.LIVE) {
            this.agentService.delete(id).subscribe({
              next: () => {
                this.getAgents();
              },
            });
          }
        });
    }
  }
}
