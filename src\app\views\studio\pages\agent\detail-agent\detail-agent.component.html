<div class="h-full relative flex flex-col rounded-3xl">
  <!-- Dialog Header -->
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      Agent Details
    </div>
    <div class="flex items-center justify-end space-x-4">
      <ng-icon
        name="heroXMark"
        class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></ng-icon>
    </div>
  </div>

  <!-- Dialog Content -->
  <div class="flex-1 overflow-y-auto mt-18 mb-20">
    <div class="px-6 pt-6 pb-[3px] flex flex-col space-x-4">
      <dx-form-field class="w-full">
        <dx-label class="text-sm">Name</dx-label>
        <input
          dxInput
          [ngModel]="data.agent.name"
          [type]="'text'"
          placeholder="Agent name"
        />
      </dx-form-field>
      <dx-form-field class="w-full">
        <dx-label class="text-sm">Description</dx-label>
        <textarea
          dxInput
          rows="3"
          autoResize
          [ngModel]="data.agent.description"
          [type]="'text'"
          placeholder="Description"
        >
        </textarea>
      </dx-form-field>
      <dx-form-field class="w-full" id="instruction">
        <dx-label class="text-sm">Instruction</dx-label>
        <textarea
          dxInput
          rows="5"
          autoResize
          [ngModel]="data.agent.instruction"
          [type]="'text'"
          placeholder="Instruction"
        >
        </textarea>
      </dx-form-field>
      <dx-form-field class="w-full" id="rule">
        <dx-label class="text-sm">Rule</dx-label>
        <textarea
          dxInput
          rows="8"
          autoResize
          [ngModel]="data.agent.rule"
          [type]="'text'"
          placeholder="Rule"
        >
        </textarea>
      </dx-form-field>
      <dx-form-field class="w-full" id="selectedModel">
        <dx-label class="text-sm">AI Model</dx-label>
        <input
          dxInput
          [ngModel]="modelAi"
          [type]="'text'"
          placeholder="AI Model"
        />
      </dx-form-field>
      <!--<dx-form-field class="w-full" id="temperature">
        <dx-label class="text-sm">Temperature</dx-label>
        <input dxInput [readOnly]="true" [ngModel]="agent.temperature" [type]="'number'" placeholder="Temperature" min="0" max="1" step="0.1">
      </dx-form-field>-->
    </div>
  </div>

  <!-- Dialog Footer -->
  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Cancel</button>
  </div>
</div>
