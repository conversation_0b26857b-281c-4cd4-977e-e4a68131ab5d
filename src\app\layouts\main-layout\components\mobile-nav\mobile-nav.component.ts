import { Component } from '@angular/core';
import { RouterLink, RouterModule } from '@angular/router';
import { DxDrawer, DxDrawerContainer, DxDrawerContent } from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { heroChevronLeft } from '@ng-icons/heroicons/outline';
import { SvgIconComponent } from '@shared/components';

@Component({
  selector: 'app-mobile-nav',
  standalone: true,
  imports: [
    RouterModule,
    RouterLink,
    SvgIconComponent,
    DxDrawer,
    DxDrawerContainer,
    DxDrawerContent,
    NgIconsModule,
  ],
  templateUrl: './mobile-nav.component.html',
  providers: [provideIcons({ heroChevronLeft })],
})
export class MobileNavComponent {
  backdropClick() {
    console.log('hello');
  }
}
