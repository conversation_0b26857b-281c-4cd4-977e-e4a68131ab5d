import {Component, inject, signal} from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  DxButton,
  DxDialogRef,
  DxError,
  DxFormField,
  DxInput,
  DxLabel, DxLoadingButton,
  DxSnackBar,
} from '@dx-ui/ui';
import { NgIcon, provideIcons } from '@ng-icons/core';
import { heroXMark } from '@ng-icons/heroicons/outline';
import { AiService } from '@shared/services';

@Component({
  selector: 'app-create-ai',
  imports: [
    DxButton,
    DxError,
    DxFormField,
    DxInput,
    DxLabel,
    NgIcon,
    ReactiveFormsModule,
    DxLoadingButton,
  ],
  providers: [provideIcons({ heroXMark })],
  templateUrl: './create-ai.component.html',
  styleUrl: './create-ai.component.css',
  host: {
    class: 'h-full',
  },
})
export class CreateAiComponent {
  isCreating = signal(false);
  dialogRef = inject(DxDialogRef<CreateAiComponent>);
  formGroupCreateAi = inject(FormBuilder).group({
    name: [null, [Validators.required, Validators.maxLength(255)]],
    description: [null, [Validators.required, Validators.maxLength(255)]],
  });
  private aiService = inject(AiService);
  private snackbar = inject(DxSnackBar);

  closeDialogCreateAi(): void {
    this.dialogRef.close();
  }

  onCreateNewAi(data: any) {
    this.isCreating.set(true);
    this.aiService.createAI(data).subscribe({
      next: (res: any) => {
        this.dialogRef.close(res.id);
        this.isCreating.set(false);
        if (res) {
          this.snackbar.open('AI created successfully!', '', {
            duration: 2000,
            horizontalPosition: 'right',
            verticalPosition: 'top',
            panelClass: 'dx-snack-bar-success',
          });
        }
      },
      error: (err: any) => {
        this.isCreating.set(false);
        // console.error(err.error.detail);
        this.snackbar.open('AI created failed!', '', {
          duration: 2000,
          horizontalPosition: 'right',
          verticalPosition: 'top',
          panelClass: 'dx-snack-bar-error',
        });
      },
    });
  }
}
