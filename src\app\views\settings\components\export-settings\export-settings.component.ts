import { Platform } from '@angular/cdk/platform';
import { CommonModule } from '@angular/common';
import { Component, effect, inject, input, signal } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { UserAiStore } from '@core/stores';
import {
  DxFormField,
  DxInput,
  DxLabel,
  DxSnackBar,
  DxSuffix,
  DxTab,
  DxTabGroup,
  DxTabLabel,
} from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroEye,
  heroEyeSlash,
  heroInformationCircle,
} from '@ng-icons/heroicons/outline';
import { ExportConfigType, IExportConfig } from '@shared/models';
import { ExportConfigService } from '@shared/services';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  Subject,
  takeUntil,
} from 'rxjs';

@Component({
  selector: 'app-export-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DxFormField,
    DxSuffix,
    DxLabel,
    DxInput,
    DxTabGroup,
    DxTab,
    DxTabLabel,
    NgIconsModule,
  ],
  templateUrl: './export-settings.component.html',
  providers: [provideIcons({ heroInformationCircle, heroEyeSlash, heroEye })],
})
export class ExportSettingsComponent {
  exportConfig = input<IExportConfig[]>();

  selectedTab = signal(0);
  showPassword = signal<boolean>(false);

  private destroy$ = new Subject<void>();

  private platform = inject(Platform);
  private snackBar = inject(DxSnackBar);
  private exportConfigService = inject(ExportConfigService);
  private userAiStore = inject(UserAiStore);

  // FORM CONTROL
  id = new FormControl<any>('');
  remote_path = new FormControl<string>('');

  aws_access_key_id = new FormControl<string>('');
  aws_secret_access_key = new FormControl<string>('');
  region_name = new FormControl<string>('ap-southeast-1');
  bucket_name = new FormControl<string>('dxconnect-bucket');

  server = new FormControl<string>('');
  port = new FormControl<number>(8000);
  username = new FormControl<string>('');
  password = new FormControl<string>('');

  constructor() {
    effect(() => {
      const currentExportConfig = this.exportConfig();
      if (currentExportConfig) {
        this.updateExportConfigFormControls(currentExportConfig);
      }
    });
  }

  ngOnInit(): void {
    this.initializeFormSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  updateTextAreaHeight(id: string) {
    if (this.platform.isBrowser) {
      const textarea = document.getElementById(id);
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 32 + 'px';
      }
    }
  }

  toggleVisibility() {
    this.showPassword.set(!this.showPassword());
  }

  private updateExportConfigFormControls(exportConfig: IExportConfig[]) {
    for (let item of exportConfig) {
      this.id.setValue(item.id, { emitEvent: false });
      const config = item.config ? JSON.parse(item.config) : null;
      if (item.type_config === ExportConfigType.S3 && config) {
        this.aws_access_key_id.setValue(config.aws_access_key_id, {
          emitEvent: false,
        });
        this.aws_secret_access_key.setValue(config.aws_secret_access_key, {
          emitEvent: false,
        });
        this.region_name.setValue(config.region_name ?? 'ap-southeast-1', {
          emitEvent: false,
        });
        this.bucket_name.setValue(config.bucket_name ?? 'dxconnect-bucket', {
          emitEvent: false,
        });
        this.remote_path.setValue(config.remote_path, { emitEvent: false });
      }
      if (item.type_config === ExportConfigType.SFTP && config) {
        this.server.setValue(config.server, { emitEvent: false });
        this.port.setValue(config.port, { emitEvent: false });
        this.username.setValue(config.username, { emitEvent: false });
        this.password.setValue(config.password, { emitEvent: false });
      }
      this.remote_path.setValue(config.remote_path, {
        emitEvent: false,
      });
    }
  }

  private subscribeToFormControlWithHandler(
    control: FormControl,
    handler: (value: any) => void,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    let stream = control.valueChanges.pipe(
      takeUntil(this.destroy$),
      distinctUntilChanged(),
      debounceTime(300)
    );

    if (shouldFilter) {
      stream = stream.pipe(filter(shouldFilter));
    }

    stream.subscribe({ next: handler });
  }

  private initializeFormSubscriptions() {
    this.subscribeToFormControlWithHandler(
      this.aws_access_key_id,
      this.handleAwsAccessKeyIdChange.bind(this),
      (value: string) => !!value
    );
    this.subscribeToFormControlWithHandler(
      this.aws_secret_access_key,
      this.handleAwsSecretAccessKeyChange.bind(this),
      (value: string) => !!value
    );
    this.subscribeToFormControlWithHandler(
      this.region_name,
      this.handleRegionNameChange.bind(this),
      (value: string) => !!value
    );
    this.subscribeToFormControlWithHandler(
      this.bucket_name,
      this.handleBucketNameChange.bind(this)
    );

    this.subscribeToFormControlWithHandler(
      this.server,
      this.handleServerChange.bind(this),
      (value: string) => !!value
    );
    this.subscribeToFormControlWithHandler(
      this.port,
      this.handlePortChange.bind(this),
      (value: number) => value >= 0
    );
    this.subscribeToFormControlWithHandler(
      this.username,
      this.handleUsernameChange.bind(this),
      (value: string) => !!value
    );
    this.subscribeToFormControlWithHandler(
      this.password,
      this.handlePasswordChange.bind(this)
    );

    this.subscribeToFormControlWithHandler(
      this.remote_path,
      this.handleRemotePathChange.bind(this)
    );
  }

  private handleAwsAccessKeyIdChange(value: string) {
    this.updateExportConfig({ aws_access_key_id: value });
  }

  private handleAwsSecretAccessKeyChange(value: string) {
    this.updateExportConfig({ aws_secret_access_key: value });
  }

  private handleRegionNameChange(value: string) {
    this.updateExportConfig({ region_name: value });
  }

  private handleBucketNameChange(value: string) {
    this.updateExportConfig({ bucket_name: value });
  }

  private handleServerChange(value: string) {}
  private handlePortChange(value: number) {}
  private handleUsernameChange(value: string) {}
  private handlePasswordChange(value: string) {}

  private handleRemotePathChange(value: string) {
    this.updateExportConfig({ remote_path: value });
  }

  private updateExportConfig(updates: Partial<IExportConfig>): void {
    const data: IExportConfig = {
      id: this.id.value,
      ai_id: this.userAiStore.currentAi()?.id,
      config: {
        aws_access_key_id: this.aws_access_key_id.value ?? '',
        aws_secret_access_key: this.aws_secret_access_key.value ?? '',
        region_name: this.region_name.value ?? '',
        bucket_name: this.bucket_name.value ?? '',
        server: this.server.value ?? '',
        port: this.port.value ?? NaN,
        username: this.username.value ?? '',
        password: this.password.value ?? '',
        remote_path: this.remote_path.value ?? '',
        ...updates,
      } as any,
      type_config: ExportConfigType.S3,
      type: ExportConfigType.S3,
    };
    this.updateOrCreateConfig(data);
  }

  private updateOrCreateConfig(config: IExportConfig) {
    if (config.id) {
      this.exportConfigService.updateExportConfig(config).subscribe({
        next: (res) => {
          if (res) {
            this.snackBar.open('Updated successfully!', '', {
              panelClass: 'dx-snack-bar-success',
              duration: 5000,
              verticalPosition: 'top',
              horizontalPosition: 'right',
            });
          }
        },
        error: (err) => {
          this.snackBar.open(err.error.detail, '', {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        },
      });
    } else {
      delete config.id;
      delete config.ai_id;
      delete config.type_config;
      this.exportConfigService.createExportConfig(config).subscribe({
        next: (res) => {
          if (res) {
            this.snackBar.open(res.message, '', {
              panelClass: 'dx-snack-bar-success',
              duration: 5000,
              verticalPosition: 'top',
              horizontalPosition: 'right',
            });
          }
        },
        error: (err) => {
          this.snackBar.open(err.error.detail, '', {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        },
      });
    }
  }
}
