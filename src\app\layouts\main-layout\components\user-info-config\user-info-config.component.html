<!-- <div data-dropdown-toggle="dropdown-help" data-dropdown-placement="right" data-dropdown-offset-distance="6"
  data-dropdown-offset-skidding="0"
  class="w-full flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group cursor-pointer">
  <ng-icon name="heroQuestionMarkCircleSolid" class="text-2xl text-gray-800 dark:text-white"></ng-icon>
  <span class="flex-1 ms-3 mt-0.5">Need help</span>
  <ng-icon name="heroEllipsisVerticalMini" class="ml-auto text-xl dark:text-white"></ng-icon>
</div>
<div id="dropdown-help"
  class="z-10 hidden bg-white divide-y divide-gray-200 rounded-lg shadow-lg w-44 dark:bg-gray-700">
  <ul class="py-2 text-sm text-primary" aria-labelledby="dropdownDefaultButton">
    <li>
      <a href="https://docs.dxconnect.lifesup.ai/" target="_blank"
        class="block px-4 py-1.5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Documentation</a>
    </li>
    <li>
      <a href="mailto:<EMAIL>" target="_blank"
        class="block px-4 py-1.5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Email us</a>
    </li>
  </ul>
</div> -->

<div
  (click)="openMenuUserInfo()"
  cdkOverlayOrigin
  #userInfoOrigin
  class="w-full flex items-center space-x-2 p-2 rounded-2xl cursor-pointer text-neutral-content dark:text-dark-neutral-content hover:bg-base-400 dark:hover:bg-dark-base-400 hover:text-base-content dark:hover:text-dark-base-content"
>
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="24" height="24" rx="12" fill="url(#paint0_linear_312_1324)" />
    <defs>
      <linearGradient
        id="paint0_linear_312_1324"
        x1="2.08723"
        y1="-6.05024e-07"
        x2="21.9128"
        y2="24"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#669EDD" />
        <stop offset="1" stop-color="#9DC390" />
      </linearGradient>
    </defs>
  </svg>
  <span
    class="text-[15px] truncate text-neutral-content dark:text-dark-neutral-content hover:text-base-content dark:hover:text-dark-base-content"
    >{{ userAiStore.currentUser()?.email }}</span
  >
  <ng-icon
    name="heroEllipsisHorizontalMini"
    class="ml-auto text-xl dark:text-white"
  ></ng-icon>
</div>
<ng-template
  cdkConnectedOverlay
  [cdkConnectedOverlayOrigin]="userInfoOrigin"
  [cdkConnectedOverlayOpen]="showMenuUserInfo()"
  [cdkConnectedOverlayPositions]="[
    {
      originX: 'start',
      originY: 'top',
      overlayX: 'start',
      overlayY: 'bottom',
      offsetY: 5
    }
  ]"
>
  <ul
    (clickOutside)="showMenuUserInfo.set(false)"
    class="p-2 w-[245px] flex flex-col justify-between shadow-lg rounded-xl border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
  >
    <li
      class="pb-3 flex space-x-[10px] cursor-pointer mb-2 border-b border-primary-border dark:border-dark-primary-border"
    >
      <div
        class="items-center text-center justify-center px-2 py-1 text-[14px] font-semibold border rounded-lg w-full"
        [ngClass]="handleClassesPlan()"
        [dxTooltip]="
          handleTimePlanTooltip('start_date') +
          '\n' +
          handleTimePlanTooltip('end_date')
        "
        [dxTooltipPosition]="'above'"
      >
        {{ this.userAiStore.currentUser()?.plan ?? "None" }}
      </div>
    </li>
    <li
      (click)="changePassword(changePassDialog)"
      class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-100 dark:hover:bg-dark-base-100"
    >
      <div
        class="flex items-center justify-between text-[16px] font-medium !text-base-content dark:!text-dark-base-content"
      >
        Change password
      </div>
    </li>
    <li
      (click)="signOut()"
      class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-100 dark:hover:bg-dark-base-100"
    >
      <div
        class="flex items-center justify-between text-[16px] font-medium !text-base-content dark:!text-dark-base-content"
      >
        Sign out
      </div>
    </li>
  </ul>
</ng-template>

<ng-template #changePassDialog>
  <div class="h-full relative flex flex-col rounded-3xl">
    <div
      class="absolute top-0 left-0 right-0 z-2 flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
    >
      <div
        class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
      >
        Change password
      </div>
      <div class="flex items-center justify-end space-x-4">
        <ng-icon
          name="heroXMarkMini"
          class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          (click)="closeDialogChangePass()"
        ></ng-icon>
      </div>
    </div>

    <div
      class="flex-1 mt-18 mb-20 overflow-y-auto flex flex-col space-y-4 p-6 bg-base-200 dark:bg-dark-base-200"
      [formGroup]="formGroupChangePass"
    >
      <dx-form-field id="oldPassword">
        <ng-icon
          dxPrefix
          name="heroLockClosed"
          class="text-2xl !text-neutral-content dark:!text-dark-neutral-content ml-3"
        ></ng-icon>
        <dx-label class="text-sm">Old Password</dx-label>
        <input
          dxInput
          formControlName="oldPassword"
          [type]="showOldPassword() ? 'text' : 'password'"
          placeholder="Old Password"
        />
        <ng-icon
          dxSuffix
          [name]="showOldPassword() ? 'heroEyeSlash' : 'heroEye'"
          class="text-xl !text-neutral-content dark:!text-dark-neutral-content m-3 cursor-pointer"
          (click)="toggleVisibility('oldPassword')"
        ></ng-icon>
      </dx-form-field>

      <div>
        <dx-form-field id="newPassword" [subscriptHidden]="true">
          <ng-icon
            dxPrefix
            name="heroLockClosed"
            class="text-2xl !text-neutral-content dark:!text-dark-neutral-content ml-3"
          ></ng-icon>
          <dx-label class="text-sm">New Password</dx-label>
          <input
            dxInput
            formControlName="password"
            [type]="showPassword() ? 'text' : 'password'"
            placeholder="New Password"
          />
          <ng-icon
            dxSuffix
            [name]="showPassword() ? 'heroEyeSlash' : 'heroEye'"
            class="text-xl !text-neutral-content dark:!text-dark-neutral-content m-3 cursor-pointer"
            (click)="toggleVisibility('password')"
          ></ng-icon>
        </dx-form-field>
        <div class="px-4">
          @if (formControlPassword.errors && (formControlPassword.touched ||
          formControlPassword.dirty)) { @if
          (formControlPassword.errors['required']) {
          <dx-error class="error-message">New Password is required.</dx-error>
          } @if (formControlPassword.errors['hasNumber']) {
          <dx-error class="error-message"
            >New Password must contain at least one number.</dx-error
          >
          } @if (formControlPassword.errors['hasCapitalCase']) {
          <dx-error class="error-message"
            >New Password must contain at least one uppercase letter.</dx-error
          >
          } @if (formControlPassword.errors['hasSmallCase']) {
          <dx-error class="error-message"
            >New Password must contain at least one lowercase letter.</dx-error
          >
          } @if (formControlPassword.errors['hasSpecialCharacters']) {
          <dx-error class="error-message"
            >New Password must contain at least one special character.</dx-error
          >
          } }
        </div>
      </div>
      <dx-form-field id="confirmPassword">
        <ng-icon
          dxPrefix
          name="heroLockClosed"
          class="text-2xl !text-neutral-content dark:!text-dark-neutral-content ml-3"
        ></ng-icon>
        <dx-label class="text-sm">Confirm New Password</dx-label>
        <input
          dxInput
          formControlName="confirmPassword"
          [type]="showConfirmPassword() ? 'text' : 'password'"
          placeholder="Confirm New Password"
        />
        <ng-icon
          dxSuffix
          [name]="showConfirmPassword() ? 'heroEyeSlash' : 'heroEye'"
          class="text-xl !text-neutral-content dark:!text-dark-neutral-content m-3 cursor-pointer"
          (click)="toggleVisibility('confirmPassword')"
        ></ng-icon>
        @if (formGroupChangePass.get('confirmPassword')?.errors?.['required'] &&
        formGroupChangePass.touched) {
        <dx-error class="error-message"
          >Confirm New Password is required.</dx-error
        >
        } @if (formControlConfirmPassword.errors?.['mustMatch'] &&
        formControlConfirmPassword.touched) {
        <dx-error class="error-message">Passwords do not match.</dx-error>
        }
      </dx-form-field>

      <!--<div class="w-full flex flex-col">
        <div class="relative">
          <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
            <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
              width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd"
                d="M8 10V7a4 4 0 1 1 8 0v3h1a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h1Zm2-3a2 2 0 1 1 4 0v3h-4V7Zm2 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1Z"
                clip-rule="evenodd" />
            </svg>
          </div>
          <input [type]="showPassword() ? 'text' : 'password'" id="password" name="password"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-light-primary focus:border-light-primary block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-light-primary dark:focus:border-light-primary"
            placeholder="New Password" formControlName="password">

          <div class="absolute inset-y-0 end-0 flex items-center pe-3">
            @if(!showPassword()) {
            <svg (click)="toggleVisibility('password')" class="w-6 h-6 text-gray-800 dark:text-white cursor-pointer"
              aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
              viewBox="0 0 24 24">
              <path stroke="currentColor" stroke-width="2"
                d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z" />
              <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
            </svg>
            } @else {
            <svg (click)="toggleVisibility('password')" class="w-6 h-6 text-gray-800 dark:text-white cursor-pointer"
              aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
              viewBox="0 0 24 24">
              <path
                d="m4 15.6 3.055-3.056A4.913 4.913 0 0 1 7 12.012a5.006 5.006 0 0 1 5-5c.178.009.356.027.532.054l1.744-1.744A8.973 8.973 0 0 0 12 5.012c-5.388 0-10 5.336-10 7A6.49 6.49 0 0 0 4 15.6Z" />
              <path
                d="m14.7 10.726 4.995-5.007A.998.998 0 0 0 18.99 4a1 1 0 0 0-.71.305l-4.995 5.007a2.98 2.98 0 0 0-.588-.21l-.035-.01a2.981 2.981 0 0 0-3.584 3.583c0 .012.008.022.01.033.05.204.12.402.211.59l-4.995 4.983a1 1 0 1 0 1.414 1.414l4.995-4.983c.189.091.386.162.59.211.011 0 .021.007.033.01a2.982 2.982 0 0 0 3.584-3.584c0-.012-.008-.023-.011-.035a3.05 3.05 0 0 0-.21-.588Z" />
              <path
                d="m19.821 8.605-2.857 2.857a4.952 4.952 0 0 1-5.514 5.514l-1.785 1.785c.767.166 1.55.25 2.335.251 6.453 0 10-5.258 10-7 0-1.166-1.637-2.874-2.179-3.407Z" />
            </svg>
            }
          </div>
        </div>
        <app-error-message [control]="formControlPassword" [name]="'New Password'"></app-error-message>
        @if(formControlPassword.errors && (formControlPassword.touched || formControlPassword.dirty)) {
        <div>
          @if (formControlPassword.errors['hasNumber']) {
          <small class="error-message">New Password must contain at least one number.</small>
          }
          @if (formControlPassword.errors['hasCapitalCase']) {
          <small class="error-message">New Password must contain at least one uppercase letter.</small>
          }
          @if (formControlPassword.errors['hasSmallCase']) {
          <small class="error-message">New Password must contain at least one lowercase letter.</small>
          }
          @if (formControlPassword.errors['hasSpecialCharacters']) {
          <small class="error-message">New Password must contain at least one special character.</small>
          }
          @if (formControlPassword.errors['mustNotMatch']) {
          <small class="error-message">Password must be different from old password</small>
          }
        </div>
        }
      </div>-->

      <!--<div class="w-full flex flex-col">
        <div class="relative">
          <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
            <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
              width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd"
                d="M8 10V7a4 4 0 1 1 8 0v3h1a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h1Zm2-3a2 2 0 1 1 4 0v3h-4V7Zm2 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1Z"
                clip-rule="evenodd" />
            </svg>
          </div>
          <input [type]="showConfirmPassword() ? 'text' : 'password'" id="confirmPassword" name="confirmPassword"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-light-primary focus:border-light-primary block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-light-primary dark:focus:border-light-primary"
            placeholder="Confirm New Password" formControlName="confirmPassword">

          <div class="absolute inset-y-0 end-0 flex items-center pe-3">
            @if(!showConfirmPassword()) {
            <svg (click)="toggleVisibility('confirmPassword')"
              class="w-6 h-6 text-gray-800 dark:text-white cursor-pointer" aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
              <path stroke="currentColor" stroke-width="2"
                d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z" />
              <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
            </svg>
            } @else {
            <svg (click)="toggleVisibility('confirmPassword')"
              class="w-6 h-6 text-gray-800 dark:text-white cursor-pointer" aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path
                d="m4 15.6 3.055-3.056A4.913 4.913 0 0 1 7 12.012a5.006 5.006 0 0 1 5-5c.178.009.356.027.532.054l1.744-1.744A8.973 8.973 0 0 0 12 5.012c-5.388 0-10 5.336-10 7A6.49 6.49 0 0 0 4 15.6Z" />
              <path
                d="m14.7 10.726 4.995-5.007A.998.998 0 0 0 18.99 4a1 1 0 0 0-.71.305l-4.995 5.007a2.98 2.98 0 0 0-.588-.21l-.035-.01a2.981 2.981 0 0 0-3.584 3.583c0 .012.008.022.01.033.05.204.12.402.211.59l-4.995 4.983a1 1 0 1 0 1.414 1.414l4.995-4.983c.189.091.386.162.59.211.011 0 .021.007.033.01a2.982 2.982 0 0 0 3.584-3.584c0-.012-.008-.023-.011-.035a3.05 3.05 0 0 0-.21-.588Z" />
              <path
                d="m19.821 8.605-2.857 2.857a4.952 4.952 0 0 1-5.514 5.514l-1.785 1.785c.767.166 1.55.25 2.335.251 6.453 0 10-5.258 10-7 0-1.166-1.637-2.874-2.179-3.407Z" />
            </svg>
            }
          </div>
        </div>
        <app-error-message [control]="formControlConfirmPassword" [name]="'Confirm New Password'"></app-error-message>
        @if(formControlConfirmPassword.errors?.['mustMatch'] && formControlConfirmPassword.touched) {
        <div>
          <small class="error-message">Passwords do not match.</small>
        </div>
        }
      </div>-->
    </div>

    <div
      class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
    >
      <button dxButton="elevated" (click)="closeDialogChangePass()">
        Close
      </button>
      <button
        dxButton="filled"
        [disabled]="formGroupChangePass.invalid"
        (click)="onSavePasswordChange(formGroupChangePass.value)"
      >
        Save
      </button>
    </div>
  </div>
</ng-template>
