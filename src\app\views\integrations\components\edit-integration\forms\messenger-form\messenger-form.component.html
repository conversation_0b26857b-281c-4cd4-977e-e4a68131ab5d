<div class="space-y-4">
  <div>
    <div class="font-medium mb-[6px] text-base text-neutral-content dark:text-dark-neutral-content">Link webhook:</div>
    <div class="rounded-xl border border-primary-border dark:border-dark-primary-border bg-base-300 dark:bg-dark-base-300 px-4 py-3 text-primary-hover relative">
      <span class="block max-w-[calc(100%-40px)] truncate">{{ linkWebHook() }}</span>
      <div class="absolute cursor-pointer top-0 right-0 my-2 mr-2 p-1 flex items-center justify-center w-fit h-fit rounded-lg bg-base-400 dark:bg-dark-base-400"
           (click)="copyText(linkWebHook())">
        <ng-icon name="heroDocumentDuplicate" class="text-2xl !text-primary dark:!text-dark-primary"></ng-icon>
      </div>
    </div>
  </div>

  <form [formGroup]="formGroup" class="space-y-2 mb-6">
    <dx-form-field>
      <dx-label>Facebook Page ID</dx-label>
      <input dxInput formControlName="facebook_page_id" placeholder="Enter Facebook Page ID" />
      @if(formGroup.get('facebook_page_id')?.invalid && formGroup.get('facebook_page_id')?.touched){
        <dx-error>Facebook Page ID is required</dx-error>
      }
    </dx-form-field>

    <dx-form-field>
      <dx-label>Facebook Page Access Token</dx-label>
      <input dxInput formControlName="facebook_page_access_token" placeholder="Enter Access Token" />
      @if(formGroup.get('facebook_page_access_token')?.invalid && formGroup.get('facebook_page_access_token')?.touched){
        <dx-error>Access Token is required</dx-error>
      }
    </dx-form-field>

    <dx-form-field>
      <dx-label>Version API</dx-label>
      <input dxInput formControlName="version_api" placeholder="Enter API version" />
      @if(formGroup.get('version_api')?.invalid && formGroup.get('version_api')?.touched){
        <dx-error>API version is required</dx-error>
      }
    </dx-form-field>

    <dx-slide-toggle formControlName="is_reply_comment">
      <dx-label>Reply to Comments</dx-label>
    </dx-slide-toggle>
    <!--<dx-form-field>

    </dx-form-field>-->
  </form>
</div>
