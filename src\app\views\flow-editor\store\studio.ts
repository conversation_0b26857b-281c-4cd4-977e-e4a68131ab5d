// @ts-nocheck
import { STUDIO_STATUS } from '@flow-editor/constant';
import { StudioState } from '@flow-editor/model';
import { CommonUtils } from '@shared/utils';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export const useStudioState = create<StudioState>()(
  devtools((set) => ({
    status:
      (CommonUtils.isRemember() ? localStorage : sessionStorage).getItem(
        'studio'
      ) ?? STUDIO_STATUS.DEV,
    setStudioStatus: (status) => set(() => ({ status })),
  }))
);
