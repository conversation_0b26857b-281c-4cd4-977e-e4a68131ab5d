/*
!* Main container styling *!
.responsive-height {
  height: calc(100vh - 200px);
  min-height: 400px;
}

!* Responsive height for different screen sizes *!
!* 0 - 639px *!
@media (max-width: 639px) {
  .responsive-height {
    height: calc(100% - 275px);
  }
}

!* 640px - 969px *!
@media (min-width: 640px) and (max-width: 969px) {
  .responsive-height {
    height: calc(100% - 154px);
  }
}

!* 970px - 1279px *!
@media (min-width: 970px) and (max-width: 1279px) {
  .responsive-height {
    height: calc(100% - 100px);
  }
}

!* 1280px - 1490px *!
@media (min-width: 1280px) and (max-width: 1517px) {
  .responsive-height {
    height: calc(100% - 154px);
  }
}

!* > 1490px *!
@media (min-width: 1491px) {
  .responsive-height {
    height: calc(100% - 100px);
  }
}

!* Ensure main container has proper height *!
:host {
  display: block;
  height: 100%;
}

!* Grid and List view container styling *!
app-grid-view,
app-list-view {
  display: block;
  height: 100%;
  overflow: hidden;
}

!* Search input styling *!
input[type="text"] {
  transition: all 0.2s ease;
}

input[type="text"]:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

!* Button styling *!
button {
  transition: all 0.2s ease;
}

button:hover {
  !* transform: translateY(-1px); *!
}

!* Mat-select styling *!
::ng-deep .mat-mdc-select {
  background-color: var(--color-light-background);
  border: 1px solid var(--color-light-border);
  border-radius: 8px;
  padding: 8px 12px;
  transition: all 0.2s ease;
}

::ng-deep .dark .mat-mdc-select {
  background-color: var(--color-dark-background);
  border-color: var(--color-dark-border);
  color: var(--color-dark-text);
}

::ng-deep .mat-mdc-select:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

::ng-deep .mat-mdc-select-panel {
  background-color: var(--color-light-background);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

::ng-deep .dark .mat-mdc-select-panel {
  background-color: var(--color-dark-background);
  box-shadow: 0 4px 20px var(--color-gray-700);
}

::ng-deep .mat-mdc-option {
  color: var(--color-light-text);
  transition: all 0.2s ease;
}

::ng-deep .dark .mat-mdc-option {
  color: var(--color-dark-text);
}

::ng-deep .mat-mdc-option:hover {
  background-color: var(--color-light-hover);
}

::ng-deep .dark .mat-mdc-option:hover {
  background-color: var(--color-dark-hover);
}

!* Breadcrumb styling *!
.breadcrumb-item {
  cursor: pointer;
  transition: color 0.2s ease;
}

.breadcrumb-item:hover {
  color: #3b82f6;
}

!* Empty state styling *!
.empty-state {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.empty-state:hover {
  opacity: 1;
}

!* Filter section styling *!
.filter-section {
  background-color: var(--color-light-background);
  border-bottom: 1px solid var(--color-light-border);
}

.dark .filter-section {
  background-color: var(--color-dark-background);
  border-bottom-color: var(--color-dark-border);
}

!* View toggle buttons *!
.view-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.view-toggle-btn.active {
  background-color: #3b82f6;
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.view-toggle-btn:not(.active) {
  background-color: var(--color-light-hover);
  color: var(--color-light-text);
}

.dark .view-toggle-btn:not(.active) {
  background-color: var(--color-dark-hover);
  color: var(--color-dark-text);
}

.view-toggle-btn:hover {
  !* transform: translateY(-1px); *!
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

!* Search section styling *!
.search-section {
  background: linear-gradient(135deg,
      var(--color-light-background) 0%,
      rgba(59, 130, 246, 0.02) 100%);
  border-bottom: 1px solid var(--color-light-border);
}

.dark .search-section {
  background: linear-gradient(135deg,
      var(--color-dark-background) 0%,
      rgba(59, 130, 246, 0.05) 100%);
  border-bottom-color: var(--color-dark-border);
}

!* Content area styling *!
.content-area {
  background-color: var(--color-light-background);
}

.dark .content-area {
  background-color: var(--color-dark-background);
}

!* Responsive design *!
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    gap: 16px;
  }

  .filter-row {
    flex-direction: column;
    gap: 12px;
  }

  .view-toggle {
    justify-content: center;
  }
}

@media (max-width: 640px) {
  .search-bar {
    flex-direction: column;
    gap: 12px;
  }

  .search-input {
    width: 100%;
  }

  .search-button {
    width: 100%;
  }
}

!* Context Menu styling *!
::ng-deep .my-menu {
  border-radius: 12px !important;
  overflow: hidden !important;
  min-width: 220px !important;
  padding: 8px !important;
  background-color: var(--color-light-background) !important;
  color: var(--color-light-text) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

::ng-deep .dark .my-menu {
  background-color: var(--color-dark-background) !important;
  color: var(--color-dark-text) !important;
  box-shadow: 0 4px 20px var(--color-gray-700) !important;
}

::ng-deep .my-menu .mat-mdc-menu-content {
  padding: 0 !important;
}

::ng-deep .my-menu .mat-mdc-menu-item {
  border-radius: 8px !important;
  margin: 2px 0 !important;
  height: 44px !important;
  line-height: 44px !important;
  transition: all 0.2s ease !important;
}

::ng-deep .my-menu .mat-mdc-menu-item:hover {
  background: var(--color-light-hover) !important;
}

::ng-deep .dark .my-menu .mat-mdc-menu-item:hover {
  background-color: var(--color-dark-hover) !important;
}

!* Menu item icon styling *!
::ng-deep .my-menu .mat-mdc-menu-item ng-icon {
  font-size: 20px !important;
  margin-right: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
*/
