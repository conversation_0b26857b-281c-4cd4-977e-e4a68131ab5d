import { Platform } from '@angular/cdk/platform';
import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  effect,
  inject,
  input,
  signal,
} from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import {
  DxFormField,
  DxInput,
  DxLabel,
  DxOption,
  DxSelect,
  DxSlider,
  DxSliderThumb,
  DxSlideToggle,
  DxSnackBar,
} from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { heroXCircleSolid } from '@ng-icons/heroicons/solid';
import { SvgIconComponent } from '@shared/components';
import { SettingsService } from '@shared/services';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  Subject,
  takeUntil,
} from 'rxjs';

@Component({
  selector: 'app-search-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DxFormField,
    DxLabel,
    DxInput,
    DxSelect,
    DxOption,
    DxSlideToggle,
    DxSlider,
    DxSliderThumb,
    SvgIconComponent,
    NgIconsModule,
  ],
  templateUrl: './search-settings.component.html',
  providers: [provideIcons({ heroXCircleSolid })],
})
export class SearchSettingsComponent {
  settings = input<any>();

  listModel = signal([
    { code: 'gpt-4.1', name: 'gpt-4.1' },
    { code: 'gpt-4.1-mini', name: 'gpt-4.1-mini' },
    { code: 'gpt-4.1-nano', name: 'gpt-4.1-nano' },
    { code: 'gpt-4o', name: 'gpt-4o' },
    { code: 'gpt-4o-mini', name: 'gpt-4o-mini' },
    { code: 'gpt-3.5-turbo-0125', name: 'gpt-3.5-turbo-0125' },
    { code: 'gemini-1.5-flash', name: 'gemini-1.5-flash' },
  ]);
  listModelOptions = computed(() =>
    this.listModel().map((model) => ({
      value: model.code,
      label: model.name,
    }))
  );

  private destroy$ = new Subject<void>();

  private platform = inject(Platform);
  private snackBar = inject(DxSnackBar);
  private settingsService = inject(SettingsService);

  // FORM CONTROL
  enabled = new FormControl<boolean>(false);
  preferSites = new FormControl<string[]>([]);
  preferSitesInput = new FormControl<string>('');
  excludeSites = new FormControl<string[]>([]);
  excludeSitesInput = new FormControl<string>('');
  fullText = new FormControl<boolean>(false);
  modelAnswer = new FormControl<string>('gpt-4o-mini');
  temperatureAnswer = new FormControl<number>(0);

  constructor() {
    effect(() => {
      const currentSettings = this.settings();
      if (currentSettings) {
        this.updateSettingsFormControls(currentSettings);
      }
    });
  }

  ngOnInit(): void {
    this.initializeFormSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  updateTextAreaHeight(id: string) {
    if (this.platform.isBrowser) {
      const textarea = document.getElementById(id);
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 32 + 'px';
      }
    }
  }

  addPreferSite() {
    const value = this.preferSitesInput.value?.trim();
    if (value) {
      const match = value.match(/^(?:https?:\/\/)?(?:www\.)?([^\/]+)/i);
      const domain = match ? match[1] : '';
      if (domain) {
        const currentValue = [...(this.preferSites.value as string[])];
        if (!currentValue.includes(domain)) {
          this.preferSites.setValue([...currentValue, domain]);
          this.preferSitesInput.setValue('');
        } else {
          this.snackBar.open('Domain đã tồn tại trong Prefer sites', '', {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        }
      } else {
        this.snackBar.open('URL không hợp lệ', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      }
    }
  }

  removePreferSite(index: number) {
    const currentValue = [...(this.preferSites.value as string[])];
    currentValue.splice(index, 1);
    this.preferSites.setValue(currentValue);
  }

  addExcludeSite() {
    const value = this.excludeSitesInput.value?.trim();
    if (value) {
      const match = value.match(/^(?:https?:\/\/)?(?:www\.)?([^\/]+)/i);
      const domain = match ? match[1] : '';
      if (domain) {
        const currentValue = [...(this.excludeSites.value as string[])];
        if (!currentValue.includes(domain)) {
          this.excludeSites.setValue([...currentValue, domain]);
          this.excludeSitesInput.setValue('');
        } else {
          this.snackBar.open('Domain đã tồn tại trong Exclude sites', '', {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        }
      } else {
        this.snackBar.open('URL không hợp lệ', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      }
    }
  }

  removeExcludeSite(index: number) {
    const currentValue = [...(this.excludeSites.value as string[])];
    currentValue.splice(index, 1);
    this.excludeSites.setValue(currentValue);
  }

  private updateSettingsFormControls(settings: any) {
    this.enabled.setValue(settings?.settings?.search_setting?.enabled, {
      emitEvent: false,
    });
    this.preferSites.setValue(
      settings?.settings?.search_setting?.prefer_sites ?? [],
      { emitEvent: false }
    );
    this.excludeSites.setValue(
      settings?.settings?.search_setting?.exclude_sites ?? [],
      { emitEvent: false }
    );
    this.fullText.setValue(settings?.settings?.search_setting?.full_text, {
      emitEvent: false,
    });
    this.modelAnswer.setValue(
      settings?.settings?.search_setting?.llm_answer?.model ?? 'gpt-4o-mini',
      { emitEvent: false }
    );
    this.temperatureAnswer.setValue(
      settings?.settings?.search_setting?.llm_answer?.temperature ?? 0,
      { emitEvent: false }
    );
  }

  private subscribeToFormControlWithHandler(
    control: FormControl,
    handler: (value: any) => void,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    let stream = control.valueChanges.pipe(
      takeUntil(this.destroy$),
      distinctUntilChanged(),
      debounceTime(300)
    );

    if (shouldFilter) {
      stream = stream.pipe(filter(shouldFilter));
    }

    stream.subscribe({ next: handler });
  }

  private subscribeToFormControlSettings(
    control: FormControl,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    this.subscribeToFormControlWithHandler(
      control,
      this.buildUpdatedSettings.bind(this),
      shouldFilter
    );
  }

  private initializeFormSubscriptions() {
    this.subscribeToFormControlSettings(this.enabled);
    this.subscribeToFormControlSettings(this.preferSites);
    this.subscribeToFormControlSettings(this.excludeSites);
    this.subscribeToFormControlSettings(this.fullText);
    this.subscribeToFormControlSettings(this.modelAnswer);
    this.subscribeToFormControlWithHandler(
      this.temperatureAnswer,
      this.handleTemperatureAnswerChange.bind(this),
      (value: number) => value >= 0 && value <= 1
    );
  }

  private handleTemperatureAnswerChange(value: number) {
    this.buildUpdatedSettings();
    this.temperatureAnswer.setValue(value, { emitEvent: false });
  }

  private buildUpdatedSettings(): void {
    const currentSettings = this.settings()?.settings;
    const updatedSettings = {
      ...currentSettings,
      search_setting: {
        ...currentSettings?.search_setting,
        enabled: this.enabled.value,
        prefer_sites: this.preferSites.value,
        exclude_sites: this.excludeSites.value,
        full_text: this.fullText.value,
        llm_answer: {
          ...currentSettings?.search_setting?.llm_answer,
          model: this.modelAnswer.value,
          temperature: this.temperatureAnswer.value,
        },
      },
    };
    this.updateSettings(updatedSettings);
  }

  private updateSettings(data: any) {
    this.settingsService.updateSetting(data).subscribe({
      next: (res) => {
        if (res) {
          this.snackBar.open(res.message, '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        }
      },
      error: (err) => {
        this.snackBar.open(err.error.detail, '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
    });
  }
}
