import { Component, inject, OnInit, signal } from '@angular/core';
import {
  <PERSON><PERSON><PERSON>er,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  DIALOG_DATA,
  DxButton,
  DxDialogRef,
  DxError,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxOption,
  DxSelect,
  DxSnackBar,
} from '@dx-ui/ui';
import { NgIcon, provideIcons } from '@ng-icons/core';
import { heroXMark } from '@ng-icons/heroicons/outline';
import { AutosizeDirective } from '@shared/directives';
import { IAgentDev } from '@shared/models';
import { AgentDevService } from '@shared/services';

@Component({
  selector: 'app-add-or-edit-agent',
  imports: [
    NgIcon,
    ReactiveFormsModule,
    DxButton,
    DxLoadingButton,
    DxError,
    DxLabel,
    DxFormField,
    DxInput,
    DxSelect,
    DxOption,
    AutosizeDirective,
  ],
  providers: [provideIcons({ heroXMark })],
  templateUrl: './add-or-edit-agent.component.html',
  styleUrl: './add-or-edit-agent.component.css',
  host: {
    class: 'h-full',
  },
})
export class AddOrEditAgentComponent implements OnInit {
  isSubmitting = signal<boolean>(false);

  aiModels = signal<any[]>([
    { value: 'gpt-4o-mini', type: 'openai', label: 'gpt-4o-mini' },
    { value: 'gpt-4o', type: 'openai', label: 'gpt-4o' },
    { value: 'gpt-4.1-mini', type: 'openai', label: 'gpt-4.1-mini' },
    { value: 'gpt-4.1-nano', type: 'openai', label: 'gpt-4.1-nano' },
    { value: 'gpt-4.1', type: 'openai', label: 'gpt-4.1' },
    { value: 'gemini-1.5-flash', type: 'gemini', label: 'gemini-1.5-flash' },
    { value: 'gemini-2.0-flash', type: 'gemini', label: 'gemini-2.0-flash' },
  ]);

  modelConfig: { name: string; temperature: number } = {
    name: 'openai',
    temperature: 0,
  };

  formGroup: FormGroup = inject(FormBuilder).group({
    id: [null],
    ai_id: [null],
    name: [null, Validators.required],
    description: [null, Validators.required],
    llm_type: [this.aiModels()[0].type, Validators.required],
    instruction: [null, Validators.required],
    model_config: [this.modelConfig],
    rule: [null],
    temperature: [
      0,
      [Validators.required, Validators.min(0), Validators.max(1)],
    ],
    selectedModel: [this.aiModels()[0].value, Validators.required],
  });
  dialogRef = inject(DxDialogRef<AddOrEditAgentComponent>);
  snackBar = inject(DxSnackBar);
  data: {
    id?: number;
    ai_id: string;
    name: string;
    description: string;
    llm_type: string;
    instruction: string;
    model_config: string;
    rule: string;
    temperature: number;
    selectedModel: any;
    isEdit: boolean;
  } = inject(DIALOG_DATA);
  private agentDevService = inject(AgentDevService);

  ngOnInit() {
    this.formGroup.get('selectedModel')?.valueChanges.subscribe((value) => {
      if (value) {
        const selectedModel = this.aiModels().find(
          (model) => model.value === value
        );
        if (selectedModel) {
          this.formGroup.get('llm_type')?.setValue(selectedModel.type);
          this.modelConfig.name = selectedModel.value;
        }
      }
    });
    if (this.data.isEdit) {
      this.formGroup.patchValue({
        id: this.data.id,
        ai_id: this.data.ai_id,
        name: this.data.name,
        description: this.data.description,
        llm_type: this.data.llm_type,
        instruction: this.data.instruction,
        model_config: this.data.model_config,
        rule: this.data.rule,
        temperature: this.data.temperature,
        selectedModel: this.data.selectedModel,
      });
    } else {
      this.formGroup.patchValue({
        llm_type: this.aiModels()[0].type,
        temperature: 0,
        selectedModel: this.aiModels()[0].value,
      });
      this.modelConfig = {
        name: this.aiModels()[0].value,
        temperature: 0,
      };
    }
    // Update modelConfig.temperature when form temperature changes
    this.formGroup.get('temperature')?.valueChanges.subscribe((value) => {
      if (value !== null && value !== undefined) {
        this.modelConfig.temperature = value;
      }
    });
  }

  onSave(isEdit: boolean): void {
    if (this.formGroup.invalid) {
      this.formGroup.markAllAsTouched();
      return;
    }

    const body: IAgentDev = this.formGroup.value;
    body.model_config = JSON.stringify(this.modelConfig);

    this.isSubmitting.set(true);
    if (isEdit) {
      this.agentDevService.update(body).subscribe({
        next: () => {
          this.isSubmitting.set(false);
          this.dialogRef.close();
          this.snackBar.open('Agent updated successfully!', '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        },
        error: () => {
          this.isSubmitting.set(false);
        },
      });
    } else {
      this.agentDevService.insert(body).subscribe({
        next: () => {
          this.isSubmitting.set(false);
          this.dialogRef.close();
          this.snackBar.open('Agent created successfully!', '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        },
        error: (error) => {
          this.isSubmitting.set(false);
        },
      });
    }
  }
}
