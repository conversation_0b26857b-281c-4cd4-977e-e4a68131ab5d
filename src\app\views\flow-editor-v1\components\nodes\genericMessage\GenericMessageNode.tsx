// @ts-nocheck
import GoTo<PERSON><PERSON><PERSON>andle from "@flow-editor-v1/components/flow/GoToBlockHandle";
import InputField from "@flow-editor-v1/components/form/InputField";
import InputFieldSuffix from "@flow-editor-v1/components/form/InputFieldSuffix";
import SelectField from "@flow-editor-v1/components/form/SelectField";
import Icon from "@flow-editor-v1/components/icon/icon";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor-v1/components/styled";
import NodeTooltip from "@flow-editor-v1/components/tooltip/NodeTooltip";
import { useFlowInstance } from "@flow-editor-v1/hook";
import { lang } from "@flow-editor-v1/init";
import { FlowDebugState, GenericMessageNodeData, IGenericMessageAction, LayoutState } from "@flow-editor-v1/model";
import {
  useBuildFlowState,
  useFlowDebugState,
  useFlowInstanceState,
  useLayoutState,
  useStudioState
} from "@flow-editor-v1/store";
import { isValidConnection } from "@flow-editor-v1/utils/flow";
import { DragDropContext, Draggable, Droppable } from "@hello-pangea/dnd";
import { yupResolver } from "@hookform/resolvers/yup";
import { STUDIO_STATUS } from "@shared/app.constant";
import { ColorPicker, Modal, Popover, Spin, Typography } from "antd";
import _, { debounce } from "lodash";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { Handle, Position, useUpdateNodeInternals } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";
import { translationApi } from "../../../api/translationApi";
import NodeHeader from "../NodeHeader";

const StyledHandleSourceAnchor = styled.div<{ $bgColor, $color, $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => props.$color};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => props.$data ? 'normal' : 'italic'};
  background: ${(props) =>
    props.$bgColor ? props.$bgColor : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const titleFormSchema = yup.object().shape({
  type: yup.string().required(),
  value: yup.string().required("Title is required"),
  language: yup.string().required()
});

const subtitleFormSchema = yup.object().shape({
  type: yup.string().required(),
  value: yup.string().required("Subtitle is required"),
  language: yup.string().required()
});

const actionFormSchema = yup.object().shape({
  type: yup.string().required("Type is required"),
  label: yup.string().required("Label is required"),
  bgColor: yup.string().required("Background color is required"),
  textColor: yup.string().required("Text color is required"),
  value: yup.string(),
  link: yup.string(),
  text_aliases: yup.array().of(yup.string()),
  action_id: yup.string()
});

const genericMessageNodeDataFormSchema = yup.object().shape({
  titles: yup.array().of(titleFormSchema).required(),
  subtitles: yup.array().of(subtitleFormSchema).required(),
  image_url: yup.string().required("Image URL is required"),
  actions: yup.array().of(actionFormSchema).required(),
});

const GenericMessageNode = ({data}: { data: GenericMessageNodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [modalLanguageOpen, setModalLanguageOpen] = useState<string | null>(null);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [handlePositions, setHandlePositions] = useState<number[]>([]);
  const [nodeTitles, setNodeTitles] = useState(data.titles || []);
  const [nodeSubtitles, setNodeSubtitles] = useState(data.subtitles || []);
  const [nodeImageUrl, setNodeImageUrl] = useState(data.image_url || "");
  const {flowInstance} = useFlowInstanceState(state => state);
  const {status} = useStudioState(state => state)
  const {debug} = useFlowDebugState<FlowDebugState>(state => state);
  const inputWithPopoverRef = useRef({});
  const {setDirty} = useBuildFlowState((state) => state);
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const updateNodeInternal = useUpdateNodeInternals();
  const {theme} = useLayoutState<LayoutState>(state => state);

  const divDataElementRef = useRef<HTMLDivElement>(null);
  const [heightDivMessage, setHeightDivMessage] = useState<number | null>(null);

  const {
    control,
    setValue,
    handleSubmit,
    reset,
    getValues,
    watch
  } = useForm({
    resolver: yupResolver(genericMessageNodeDataFormSchema)
  })

  const {
    fields: titleFields,
    append: titleAppend,
    remove: titleRemove,
    replace: titleReplace,
    update: titleUpdate
  } = useFieldArray({
    control,
    name: 'titles'
  });

  const {
    fields: subtitleFields,
    append: subtitleAppend,
    remove: subtitleRemove,
    replace: subtitleReplace,
    update: subtitleUpdate
  } = useFieldArray({
    control,
    name: 'subtitles'
  });

  const {
    fields: actionFields,
    append: actionAppend,
    remove: actionRemove,
    replace: actionReplace,
    update: actionUpdate
  } = useFieldArray({
    control,
    name: 'actions'
  });

  const handleAddTitle = () => {
    titleAppend({
      type: 'text',
      value: '',
      language: ''
    })
  }

  const handleDeleteTitle = (index: number, fieldId: string) => {
    titleRemove(index)
    if (inputWithPopoverRef.current) {
      delete inputWithPopoverRef.current[fieldId]
    }
  }

  const handleAddSubtitle = () => {
    subtitleAppend({
      type: 'text',
      value: '',
      language: ''
    })
  }

  const handleDeleteSubtitle = (index: number, fieldId: string) => {
    subtitleRemove(index)
    if (inputWithPopoverRef.current) {
      delete inputWithPopoverRef.current[fieldId]
    }
  }

  const handleAddAction = () => {
    actionAppend({
      type: "button",
      label: "",
      bgColor: "#dadada",
      textColor: "#000000",
      value: "",
      link: "",
      text_aliases: [],
    } as IGenericMessageAction)
  }

  const handleDeleteAction = (index: number, fieldId: string) => {
    actionRemove(index)
    if (inputWithPopoverRef.current) {
      delete inputWithPopoverRef.current[fieldId]
    }
  }

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const reorderedItems = Array.from(getValues().actions);
    const [movedItem] = reorderedItems.splice(result.source.index, 1);
    reorderedItems.splice(result.destination.index, 0, movedItem);
    actionReplace(reorderedItems);
  };

  const resetForm = () => {
    reset()
  }

  const onSubmit = (formValue) => {
    data.titles = _.cloneDeep(formValue?.titles);
    data.subtitles = _.cloneDeep(formValue?.subtitles);
    data.image_url = formValue?.image_url;
    formValue?.actions.forEach((action, index) => {
      action.action_id = `${data.id}_source#${index + 1}`;
    });
    data.actions = _.cloneDeep(formValue?.actions);
    setHeightDivMessage(heightDivMessage);
    resetForm();
    setModalOpen(false);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  }

  const onSubmitLanguage = (e) => {
    e.preventDefault();
    data.titles = _.cloneDeep(getValues().titles) as any;
    data.subtitles = _.cloneDeep(getValues().subtitles) as any;
    setModalLanguageOpen(null);
  }

  useEffect(() => {
    resetForm();
    titleReplace(_.cloneDeep(data.titles && data.titles.length ? data.titles : [{
      type: 'text',
      value: '',
      language: ''
    }]));
    subtitleReplace(_.cloneDeep(data.subtitles && data.subtitles.length ? data.subtitles : [{
      type: 'text',
      value: '',
      language: ''
    }]));
    data.image_url && setValue('image_url', data.image_url);
    actionReplace(_.cloneDeep(data.actions && data.actions.length ? data.actions : [{
      type: "button",
      label: "",
      bgColor: "#dadada",
      textColor: "#000000",
      value: "",
      link: "",
      text_aliases: [],
      action_id: ""
    }]));
    updateNodeInternal(data.id);
  }, [JSON.stringify(data), modalOpen]);

  useEffect(() => {
    if (heightDivMessage !== null && data.actions) {
      const positions = data.actions.map((_, index) => heightDivMessage + 59 + 37 * index);
      setHandlePositions(positions);
      updateNodeInternal(data.id);
    }
  }, [heightDivMessage, data.actions]);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      for (let entry of entries) {
        setHeightDivMessage(entry.contentRect.height + 0);
      }
    });

    if (divDataElementRef.current) {
      observer.observe(divDataElementRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [nodeTitles, nodeSubtitles, nodeImageUrl]);

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    if (!data.debug) setModalOpen(true)
  }, [data.debug]);

  const handleClickInput = () => {
    setModalLanguageOpen(null)
  }

  const handleOnFocusInput = () => {
    setModalLanguageOpen(null)
  }

  const handleOnChangeInput = async (sentence: string, index: number, fieldType: 'title' | 'subtitle') => {
    if (sentence && index > -1) {
      setConfirmLoading(true);
      const {language} = await translationApi.detectLanguage({
        sentence,
        api_keys: [localStorage.getItem('current-api-key')]
      });
      if (language) {
        if (fieldType === 'title') {
          titleUpdate(index, {...getValues().titles[index], language});
        } else {
          subtitleUpdate(index, {...getValues().subtitles[index], language});
        }
        setConfirmLoading(false);
        if (fieldType === 'title') {
          data.titles = _.cloneDeep(getValues().titles) as any;
        }
        if (fieldType === 'subtitle') {
          data.subtitles = _.cloneDeep(getValues().subtitles) as any;
        }
      }
    }
  };

  const debounceOnChangeInput = useCallback(debounce(handleOnChangeInput, 1000), []);

  const handleOnOpenModalLanguageChange = () => {
    setModalLanguageOpen(null)
  }

  const handleClickSuffix = async (e: React.MouseEvent, fieldId: string, index: number, fieldType: 'title' | 'subtitle') => {
    e.preventDefault();
    setModalLanguageOpen(fieldId);
    const currentValue = fieldType === 'title' ? getValues().titles[index] : getValues().subtitles[index];
    if (!currentValue.language) {
      const {language} = await translationApi.detectLanguage({
        sentence: currentValue.value,
        api_keys: [localStorage.getItem('current-api-key')]
      });
      if (language) {
        if (fieldType === 'title') {
          titleUpdate(index, {...currentValue, language});
        }
        if (fieldType === 'subtitle') {
          subtitleUpdate(index, {...currentValue, language});
        }
        setConfirmLoading(false);
        if (fieldType === 'title') {
          data.titles = _.cloneDeep(getValues().titles) as any;
        }
        if (fieldType === 'subtitle') {
          data.subtitles = _.cloneDeep(getValues().subtitles) as any;
        }
      }
    }
  };

  const handleMouseDownSuffix = (e) => {
    e.preventDefault();
  };

  const handleClosePopover = (e) => {
    e.stopPropagation()
    setModalLanguageOpen(null)
  }

  const renderHandles = () => {
    if (!data.actions || data.actions.length === 0) return null;

    return data.actions.map((_, index) => {
      const key = `${data.id}_source#${index + 1}`;
      const goToBlock = data.goToBlockSource?.find(v => v.sourceHandle === key);

      if (goToBlock) {
        return (
          <GoToBlockHandle
            key={key}
            data={data}
            sourceHandle={key}
            style={{
              height: 14,
              width: 84,
              top: `${handlePositions[index] || 0}px`,
              right: -80,
              backgroundColor: theme === 'dark' ? 'white' : 'black',
              fontSize: 10,
              color: theme === 'dark' ? 'black' : 'white',
              border: 'none',
              borderRadius: 10,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          />
        );
      }

      return (
        <Handle
          key={key}
          type="source"
          position={Position.Right}
          id={key}
          style={{
            height: 8,
            width: 8,
            top: `${handlePositions[index]}px`,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
      );
    });
  };

  useEffect(() => {
    const subscription = watch((value) => {
      if (value.titles && value.titles.length > 0) {
        const validTitles = value.titles.filter(title => title.value && title.value.trim() !== '');
        if (validTitles.length > 0) {
          const newTitles = validTitles.map(msg => ({
            type: msg.type || 'text',
            value: msg.value || '',
            language: msg.language || ''
          }));
          setNodeTitles(newTitles);
        }
      }
      if (value.titles && value.titles.length > 0) {
        const validSubtitles = value.subtitles?.filter(subtitle => subtitle.value && subtitle.value.trim() !== '');
        if (validSubtitles && validSubtitles.length > 0) {
          const newSubtitles = validSubtitles.map(msg => ({
            type: msg.type || 'text',
            value: msg.value || '',
            language: msg.language || ''
          }));
          setNodeSubtitles(newSubtitles);
        }
      }
      if (value.image_url) {
        setNodeImageUrl(value.image_url);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  useEffect(() => {
    if (nodeTitles.length > 0) {
      updateNodeInternal(data.id);
    }
    if (nodeSubtitles.length > 0) {
      updateNodeInternal(data.id);
    }
    if (nodeImageUrl) {
      updateNodeInternal(data.id);
    }
  }, [nodeTitles, nodeSubtitles, nodeImageUrl]);

  const isImageUrl = useCallback((url) => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  }, []);

  const isValidImageUrl = (url: string) => {
    const isImageUrl = /\.(jpeg|jpg|gif|png|svg|webp|bmp|tiff)$/i.test(url);
    const isVariablePlaceholder = /{{{(.*?)}}}/.test(url);
    if (isVariablePlaceholder || isImageUrl) {
      return null;
    }
    return "URL must point to an image or be a valid variable placeholder";
  };

  const renderMessageDiv = () => {
    if (!nodeTitles?.length || !nodeTitles[0]?.value) return null;
    if (!nodeSubtitles?.length || !nodeSubtitles[0]?.value) return null;
    if (!nodeImageUrl) return null;

    return (
      <div
        ref={divDataElementRef}
        className="flex flex-col space-y-2"
      >
         <img
          className={"zoom rounded-md"}
          src={isImageUrl(nodeImageUrl) ? nodeImageUrl : '/assets/img/default-img.png'}
          alt={`${nodeImageUrl}`}
          style={{width: "auto", height: 100, objectFit: 'cover', marginTop: 12}}
        />
        <div className="line-clamp-2 mt-3" style={{ fontSize: '10px', fontWeight: 'bold' }}>{nodeTitles[0].value}</div>
        <div className="line-clamp-3" style={{ fontSize: '10px' }}>{nodeSubtitles[0].value}</div>
      </div>
    );
  };

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $debug={data.debug} $theme={theme}>
      {
        debug && (
          <div className="absolute left-0 text-[10px]" style={{top: -20, color: '#D4D9E2'}}>{data.id}</div>
        )
      }
      {
        status && status === STUDIO_STATUS.DEV && (
          <NodeTooltip data={data} selected={data.selected}/>
        )
      }
      <NodeHeader data={data} iconName={data.icon}/>
      {renderMessageDiv()}
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
        {data.actions && data.actions.length ? (
          <div>
            {data.actions.map((action, index) => {
              if (index === 0) {
                return (
                  <StyledHandleSourceAnchor
                    key={index}
                    $bgColor={action.bgColor}
                    $color={action.textColor}
                    $data={data.actions}
                    onDoubleClick={handleOpenModal}
                  >
                    {action.label ? action.label : "Configure"}
                  </StyledHandleSourceAnchor>
                );
              }
              if (index > 0) {
                return (
                  <StyledHandleSourceAnchor
                    key={index}
                    $bgColor={action.bgColor}
                    $color={action.textColor}
                    $data={data.actions}
                    onDoubleClick={handleOpenModal}
                  >
                    {action.label ? action.label : "Configure"}
                  </StyledHandleSourceAnchor>
                );
              }
            })}
          </div>
        ) : (
          <StyledHandleSourceAnchor
            $bgColor={data.node_color}
            $color={'white'}
            $data={data.actions}
            onDoubleClick={handleOpenModal}
          >
            Configure
          </StyledHandleSourceAnchor>
        )}
        <div>
          {renderHandles()}
        </div>
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => {
          if (!confirmLoading) setModalOpen(false)
        }}
        onCancel={() => {
          if (!confirmLoading) setModalOpen(false)
        }}
        footer={null}
        closable={false}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {
          setNodeSelected(false);
          resetStyle()
        }}
      >
        <Spin spinning={confirmLoading}>
          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col space-y-4 mt-6">
            <div className="flex flex-col">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Title <span style={{color: 'red'}}>*</span>
              </Typography.Text>
              {
                titleFields.map((field, index) => (
                  <div key={field.id} className="flex items-center justify-between gap-4 mt-2">
                    <Popover
                      color={theme === 'light' ? '#EBEBEF' : '#1E232E'}
                      content={
                        <div className="flex flex-col space-y-4 mt-6">
                          <SelectField
                            name={`titles[${index}].language`}
                            control={control}
                            options={lang}
                          />

                          <div
                            className="w-full flex justify-end items-center space-x-4"
                            style={{marginTop: 24}}
                          >
                            <StyledButtonModal
                              type="button"
                              key="cancel"
                              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
                              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
                              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
                              onClick={handleClosePopover}
                            >
                              Cancel
                            </StyledButtonModal>
                            <StyledButtonModal
                              $bgColor={"#7F75CF"}
                              $color={"white"}
                              type={undefined}
                              onClick={onSubmitLanguage}
                            >
                              Save
                            </StyledButtonModal>
                          </div>
                        </div>
                      }
                      title="Localization"
                      placement="left"
                      getPopupContainer={() => inputWithPopoverRef.current[field.id]}
                      open={modalLanguageOpen === field.id}
                      onOpenChange={handleOnOpenModalLanguageChange}>
                      <InputFieldSuffix ref={(el) => inputWithPopoverRef.current[field.id] = el} style={{flexGrow: 1}}
                                        type={'textarea'}
                                        name={`titles.${index}.value`}
                                        suffix={<Icon iconName={'RiTranslate2'} size={16} style={{color: '#6F767E'}}/>}
                                        onMouseDownSuffix={(e) => handleMouseDownSuffix(e)}
                                        onClickSuffix={(e) => handleClickSuffix(e, field.id, index, 'title')}
                                        onChangeInput={(e, name) => debounceOnChangeInput(e, index, 'title')}
                                        onClickInput={(e) => handleClickInput()}
                                        onFocusInput={(e, name) => handleOnFocusInput()}
                                        disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                                        setValue={setValue}/>
                    </Popover>
                    {
                      status && status === STUDIO_STATUS.DEV && (
                        <div className="hover:cursor-pointer" onClick={(e) => handleDeleteTitle(index, field.id)}>
                          <Icon iconName={'RiDeleteBinLine'} size={24} style={{color: '#6F767E'}}/>
                        </div>
                      )
                    }
                  </div>
                ))
              }
            </div>
            {
              status && status === STUDIO_STATUS.DEV && (
                <div className="flex gap-3 items-center hover:cursor-pointer" onClick={handleAddTitle}>
                  <Icon iconName={'RiAddCircleFill'} size={18} style={{color: data.node_color}}/>
                  <div style={{color: data.node_color}}>Add multiple title for randomizing</div>
                </div>
              )
            }

            <div className="flex flex-col">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Subtitle <span style={{color: 'red'}}>*</span></Typography.Text>
              {
                subtitleFields.map((field, index) => (
                  <div key={field.id} className="flex items-center justify-between gap-4 mt-2">
                    <Popover
                      color={theme === 'light' ? '#EBEBEF' : '#1E232E'}
                      content={
                        <div className="flex flex-col space-y-4 mt-6">
                          <SelectField
                            name={`subtitles[${index}].language`}
                            control={control}
                            options={lang}
                          />

                          <div
                            className="w-full flex justify-end items-center space-x-4"
                            style={{marginTop: 24}}
                          >
                            <StyledButtonModal
                              type="button"
                              key="cancel"
                              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
                              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
                              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
                              onClick={handleClosePopover}
                            >
                              Cancel
                            </StyledButtonModal>
                            <StyledButtonModal
                              $bgColor={"#7F75CF"}
                              $color={"white"}
                              type={undefined}
                              onClick={onSubmitLanguage}
                            >
                              Save
                            </StyledButtonModal>
                          </div>
                        </div>
                      }
                      title="Localization"
                      placement="left"
                      getPopupContainer={() => inputWithPopoverRef.current[field.id]}
                      open={modalLanguageOpen === field.id}
                      onOpenChange={handleOnOpenModalLanguageChange}>
                      <InputFieldSuffix ref={(el) => inputWithPopoverRef.current[field.id] = el} style={{flexGrow: 1}}
                                        type={'textarea'}
                                        name={`subtitles.${index}.value`}
                                        suffix={<Icon iconName={'RiTranslate2'} size={16} style={{color: '#6F767E'}}/>}
                                        onMouseDownSuffix={(e) => handleMouseDownSuffix(e)}
                                        onClickSuffix={(e) => handleClickSuffix(e, field.id, index, 'subtitle')}
                                        onChangeInput={(e, name) => debounceOnChangeInput(e, index, 'subtitle')}
                                        onClickInput={(e) => handleClickInput()}
                                        onFocusInput={(e, name) => handleOnFocusInput()}
                                        disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                                        setValue={setValue}/>
                    </Popover>
                    {
                      status && status === STUDIO_STATUS.DEV && (
                        <div className="hover:cursor-pointer" onClick={(e) => handleDeleteSubtitle(index, field.id)}>
                          <Icon iconName={'RiDeleteBinLine'} size={24} style={{color: '#6F767E'}}/>
                        </div>
                      )
                    }
                  </div>
                ))
              }
            </div>
            {
              status && status === STUDIO_STATUS.DEV && (
                <div className="flex gap-3 items-center hover:cursor-pointer" onClick={handleAddSubtitle}>
                  <Icon iconName={'RiAddCircleFill'} size={18} style={{color: data.node_color}}/>
                  <div style={{color: data.node_color}}>Add multiple subtitle for randomizing</div>
                </div>
              )
            }

            <div className="flex flex-col space-y-2">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Image URL <span style={{color: 'red'}}>*</span></Typography.Text>
              <InputField
                className="w-full"
                style={{flexGrow: 1}}
                type="input"
                name={`image_url`}
                control={control}
                disabled={status && status === STUDIO_STATUS.LIVE}
                setValue={setValue}
                validate={isValidImageUrl}
              />
            </div>

            <div className="flex flex-col">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Actions</Typography.Text>
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="droppable-1" direction="vertical">
                  {
                    (provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className="flex flex-col mt-2 space-y-3"
                      >
                        {
                          actionFields.map((field, index) => (
                            <Draggable key={field.id} draggableId={field.id} index={index}>
                              {
                                (provided) => (
                                  <div ref={provided.innerRef}
                                       {...provided.draggableProps}
                                       {...provided.dragHandleProps}
                                       style={{
                                         userSelect: "none",
                                         ...provided.draggableProps.style
                                       }}
                                       className="p-2 rounded-lg flex items-center justify-center border border-gray-700">
                                    <div className="cursor-move">
                                      <Icon iconName={'RiDraggable'} size={20} style={{color: '#6F767E'}}/>
                                    </div>
                                    <div className="flex-grow grid grid-cols-1 lg:grid-cols-2">
                                      <div className="col-span-1 flex flex-col space-y-2">
                                        <div className="grid grid-cols-4 gap-6 items-center">
                                          <Typography.Text
                                            className="col-span-1 text-white text-right">Label</Typography.Text>
                                          <div className="col-span-3 w-full">
                                            <InputField
                                              type={"input"}
                                              name={`actions[${index}].label`}
                                              control={control}
                                              disabled={status && status === STUDIO_STATUS.LIVE}
                                              setValue={setValue}
                                            />
                                          </div>
                                        </div>
                                        <div className="grid grid-cols-4 gap-6 items-center">
                                          <Typography.Text
                                            className="text-white col-span-1 text-right">Text</Typography.Text>
                                          <div className="col-span-3 w-full">
                                            <ColorPicker
                                              value={getValues(`actions.${index}.textColor`) as string}
                                              onChange={(color) => setValue(`actions.${index}.textColor`, color.toHexString())}
                                              disabled={status && status === STUDIO_STATUS.LIVE}
                                            />
                                          </div>
                                        </div>
                                        <div className="grid grid-cols-4 gap-6 items-center">
                                          <Typography.Text
                                            className="text-white col-span-1 text-right">Background</Typography.Text>
                                          <div className="col-span-3 w-full">
                                            <ColorPicker
                                              value={getValues(`actions.${index}.bgColor`) as string}
                                              onChange={(color) => setValue(`actions.${index}.bgColor`, color.toHexString())}
                                              disabled={status && status === STUDIO_STATUS.LIVE}
                                            />
                                          </div>
                                        </div>
                                      </div>
                                      <div className="col-span-1 flex flex-col space-y-2">
                                        <div className="grid grid-cols-4 gap-6 items-center">
                                          <Typography.Text
                                            className="col-span-1 text-white text-right">Aliases</Typography.Text>
                                          <div className="col-span-3 w-full">
                                            <SelectField name={`actions[${index}].text_aliases`}
                                                         disabled={status && status === STUDIO_STATUS.LIVE}
                                                         control={control}
                                                         mode={'tags'} options={[]} style={{width: '100%'}}/>
                                          </div>
                                        </div>
                                        <div className="grid grid-cols-4 gap-6 items-center">
                                          <Typography.Text
                                            className="col-span-1 text-white text-right">Value</Typography.Text>
                                          <div className="col-span-3 w-full">
                                            <InputField
                                              type={"textarea"}
                                              name={`actions[${index}].value`}
                                              control={control}
                                              disabled={status && status === STUDIO_STATUS.LIVE}
                                              setValue={setValue}
                                            />
                                          </div>
                                        </div>
                                        <div className="grid grid-cols-4 gap-6 items-center">
                                          <Typography.Text
                                            className="col-span-1 text-white text-right">Link</Typography.Text>
                                          <div className="col-span-3 w-full">
                                            <InputField
                                              type={"input"}
                                              name={`actions[${index}].link`}
                                              control={control}
                                              disabled={status && status === STUDIO_STATUS.LIVE}
                                              setValue={setValue}
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    {
                                      status && status === STUDIO_STATUS.DEV && (
                                        <div className="ml-3 hover:cursor-pointer"
                                             onClick={(e) => handleDeleteAction(index, field.id)}>
                                          <Icon iconName={'RiDeleteBinLine'} size={24} style={{color: '#6F767E'}}/>
                                        </div>
                                      )
                                    }
                                  </div>
                                )
                              }
                            </Draggable>
                          ))
                        }
                        {provided.placeholder}
                      </div>
                    )
                  }
                </Droppable>
              </DragDropContext>
            </div>
            {
              status && status === STUDIO_STATUS.DEV && (
                <div className="flex gap-3 items-center hover:cursor-pointer" onClick={handleAddAction}>
                  <Icon iconName={'RiAddCircleFill'} size={18} style={{color: data.node_color}}/>
                  <div style={{color: data.node_color}}>Add action</div>
                </div>
              )
            }

            <div className="w-full flex justify-end items-center space-x-4" style={{marginTop: 24}}>
              <StyledButtonModal
                type="button"
                key="cancel"
                $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
                $color={theme === 'light' ? '#282826' : '#F8FAFF'}
                $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
                onClick={() => {if (!confirmLoading) setModalOpen(false)}}
              >
                Cancel
              </StyledButtonModal>
              <StyledButtonModal
                type="submit"
                key="save"
                $bgColor={"#7F75CF"}
                $color={"white"}
                disabled={status && status === STUDIO_STATUS.LIVE}
              >
                Save
              </StyledButtonModal>
            </div>
          </form>
        </Spin>
      </Modal>
    </StyledNodeFlow>
  );
};

export default GenericMessageNode;
