import {
  Directive,
  ElementRef,
  EventEmitter,
  Output,
  inject,
} from '@angular/core';

@Directive({
  selector: '[clickOutside]',
  standalone: true,
})
export class ClickOutsideDirective {
  private _elementRef = inject(ElementRef);

  @Output('clickOutside') clickOutside: EventEmitter<any> = new EventEmitter();

  constructor() {
    setTimeout(() => {
      document.addEventListener('click', (e) => this.clickOutsideFn(e));
    }, 1);

    setTimeout(() => {
      document.addEventListener('contextmenu', (e) => this.clickOutsideFn(e));
    }, 1);
  }

  ngOnDestroy(): void {
    document.removeEventListener('click', (e) => this.clickOutsideFn(e));
  }

  private clickOutsideFn(event: MouseEvent): void {
    const targetElement = event.target;
    const clickedInside =
      this._elementRef.nativeElement.contains(targetElement);

    if (!clickedInside) {
      this.clickOutside.emit();
    }
  }
}
