<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
        {{ data.isCreate ? "Add folder" : "Edit folder" }}
    </div>
    <div class="flex items-center justify-end space-x-4">
      <app-svg-icon
        type="icClose"
        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="this.dialogRef.close()"
      ></app-svg-icon>
    </div>
  </div>

  <div class="flex-1 overflow-y-auto mt-18 mb-20">
    <div [formGroup]="formGroup" class="px-6 pt-6 pb-[3px] flex flex-col space-x-4">
      <dx-form-field class="w-full" id="name">
        <dx-label class="text-sm">Name</dx-label>
        <input
          dxInput
          formControlName="name"
          type="text"
          placeholder="Folder name"
          (keydown)="onKeyDownFolderDialog($event)"
        />
        @if (formGroup.get('name')?.errors?.['required'] && (formGroup.get('name')?.touched || formGroup.get('name')?.dirty)) {
          <dx-error>Folder name is required</dx-error>
        }
      </dx-form-field>
    </div>
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Cancel</button>
    <button
      dxLoadingButton="filled"
      [loading]="isCreatingFolder()"
      [disabled]="formGroup.invalid || isCreatingFolder()"
      (click)="saveCreatingFolder()"
    >
      {{ data.isCreate ? "Create" : "Update" }}{{ isCreatingFolder() ? 'ing...' : '' }}
    </button>
  </div>
</div>
