<div class="settings-tab-content w-full p-6 flex flex-col space-y-6">
  <div class="flex items-center space-x-2">
    <dx-slide-toggle [formControl]="enabled"></dx-slide-toggle>
    <div
      class="text-xl font-bold text-base-content dark:text-dark-base-content"
    >
      Search settings
    </div>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div class="flex flex-col lg:w-3/4">
    <dx-form-field
      [subscriptHidden]="true"
      [class.!mb-7]="!(preferSites.value && preferSites.value.length > 0)"
    >
      <dx-label>Prefer sites</dx-label>
      <app-svg-icon
        dxPrefix
        type="icLinkPrefix"
        class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
      ></app-svg-icon>
      <input
        dxInput
        placeholder="Paste url here"
        [formControl]="preferSitesInput"
        (keyup.enter)="addPreferSite()"
      />
    </dx-form-field>
    @if (preferSites.value && preferSites.value.length > 0) {
    <div class="flex flex-wrap gap-2 mb-7">
      @for (url of preferSites.value; track url; let index = $index) {
      <div
        class="flex items-center space-x-2 px-4 py-1 bg-base-100 dark:bg-dark-base-100 rounded-[12px]"
      >
        <div class="text-sm text-base-content dark:text-dark-base-content">
          {{ url }}
        </div>
        <ng-icon
          name="heroXCircleSolid"
          class="!text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          (click)="removePreferSite(index)"
        ></ng-icon>
      </div>
      }
    </div>
    }
    <dx-form-field
      [subscriptHidden]="true"
      [class.!mb-7]="!(excludeSites.value && excludeSites.value.length > 0)"
    >
      <dx-label>Exclude sites</dx-label>
      <app-svg-icon
        dxPrefix
        type="icLinkPrefix"
        class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
      ></app-svg-icon>
      <input
        dxInput
        placeholder="Paste url here"
        [formControl]="excludeSitesInput"
        (keyup.enter)="addExcludeSite()"
      />
    </dx-form-field>
    @if (excludeSites.value && excludeSites.value.length > 0) {
    <div class="flex flex-wrap gap-2 mb-7">
      @for (url of excludeSites.value; track url; let index = $index) {
      <div
        class="flex items-center space-x-2 px-4 py-1 bg-base-100 dark:bg-dark-base-100 rounded-[12px]"
      >
        <div class="text-sm text-base-content dark:text-dark-base-content">
          {{ url }}
        </div>
        <ng-icon
          name="heroXCircleSolid"
          class="!text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          (click)="removeExcludeSite(index)"
        ></ng-icon>
      </div>
      }
    </div>
    }
    <dx-slide-toggle [formControl]="fullText">Full text</dx-slide-toggle>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div
    class="text-lg font-bold text-base-content dark:text-dark-base-content mb-4"
  >
    LLM answer
  </div>
  <div class="flex flex-col lg:w-3/4">
    <div class="flex items-center space-x-4">
      <dx-form-field class="flex-1">
        <dx-label>AI Model</dx-label>
        <dx-select
          placeholder="Choose the AI model you would like to use"
          [formControl]="modelAnswer"
        >
          @for (item of listModelOptions(); track item.value) {
          <dx-option [value]="item.value">
            {{ item.label }}
          </dx-option>
          }
        </dx-select>
      </dx-form-field>
      <div class="flex-1 flex flex-wrap items-center space-x-3">
        <div class="flex-1 w-full flex items-center space-x-4">
          <dx-label class="flex items-center space-x-1 !mb-0"
            >Temperature</dx-label
          >
          <dx-slider class="flex-1" [max]="1" [min]="0" [step]="0.1">
            <input dxSliderThumb #slider [formControl]="temperatureAnswer" />
          </dx-slider>
        </div>
        <input
          type="number"
          class="h-8 w-18 pl-3 py-1 text-center text-base-content dark:text-dark-base-content rounded-lg bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border"
          [formControl]="temperatureAnswer"
        />
      </div>
    </div>
  </div>
</div>
