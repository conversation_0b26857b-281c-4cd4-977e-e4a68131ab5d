export const icDashboard = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5 22C3.34315 22 2 20.6569 2 19V11.3361C2 10.4857 2.36096 9.67518 2.99311 9.10625L9.9931 2.80625C11.134 1.77943 12.866 1.77943 14.0069 2.80625L21.0069 9.10625C21.639 9.67518 22 10.4857 22 11.3361V19C22 20.6569 20.6569 22 19 22H5ZM20 11.3361V19C20 19.5523 19.5523 20 19 20H16V15C16 13.3432 14.6569 12 13 12H11C9.34315 12 8 13.3432 8 15V20H5C4.44772 20 4 19.5523 4 19V11.3361C4 11.0526 4.12032 10.7825 4.33104 10.5928L11.331 4.29284C11.7113 3.95056 12.2887 3.95056 12.669 4.29284L19.669 10.5928C19.8797 10.7825 20 11.0526 20 11.3361ZM10 20V15C10 14.4478 10.4477 14 11 14H13C13.5523 14 14 14.4478 14 15V20H10Z" fill="currentColor"/>
</svg>
`,
};

export const icStudio = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M19 5H5C4.44772 5 4 5.44772 4 6V15C4 15.5523 4.44772 16 5 16H19C19.5523 16 20 15.5523 20 15V6C20 5.44771 19.5523 5 19 5ZM5 3C3.34315 3 2 4.34315 2 6V15C2 16.6569 3.34315 18 5 18H19C20.6569 18 22 16.6569 22 15V6C22 4.34315 20.6569 3 19 3H5Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M1 20C1 19.4477 1.44772 19 2 19H22C22.5523 19 23 19.4477 23 20C23 20.5523 22.5523 21 22 21H2C1.44772 21 1 20.5523 1 20Z" fill="currentColor"/>
</svg>
`,
};

export const icInbox = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M22 13.1468C22 13.0976 21.9964 13.0485 21.9892 13C21.9821 12.9525 21.9717 12.9055 21.9578 12.8594L19.6414 5.13796C19.2607 3.869 18.0927 3 16.7679 3H7.23209C5.90727 3 4.7393 3.869 4.35861 5.13796L2.04217 12.8594C2.02834 12.9055 2.01788 12.9525 2.01083 13C2.00363 13.0485 2 13.0976 2 13.1468V18C2 19.6569 3.34315 21 5 21H19C20.6569 21 22 19.6569 22 18V13.1468ZM16.7679 5H7.23209C6.79048 5 6.40116 5.28967 6.27427 5.71265L4.08806 13H8.38197C8.76074 13 9.107 13.214 9.27639 13.5528L9.99724 14.9945C9.99893 14.9979 10.0024 15 10.0062 15H13.9938C13.9976 15 14.0011 14.9979 14.0028 14.9945L14.7236 13.5528C14.893 13.214 15.2393 13 15.618 13H19.9119L17.7257 5.71265C17.5988 5.28967 17.2095 5 16.7679 5ZM20 15H16.2361L15.7916 15.8889C15.4511 16.5698 14.7552 17 13.9938 17H10.0062C9.24487 17 8.54887 16.5699 8.20838 15.8889L7.76393 15H4V18C4 18.5523 4.44772 19 5 19H19C19.5523 19 20 18.5523 20 18V15Z" fill="currentColor"/>
</svg>
`,
};

export const icKnowledgeBase = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5 4C3.34315 4 2 5.34315 2 7V17C2 18.6569 3.34315 20 5 20H8.02633C8.67123 20 9.3119 20.104 9.9237 20.3079L11.6838 20.8946C11.889 20.963 12.111 20.963 12.3162 20.8946L14.0763 20.3079C14.6881 20.104 15.3288 20 15.9737 20H19C20.6569 20 22 18.6569 22 17V7C22 5.34315 20.6569 4 19 4H15C13.9072 4 12.9167 4.43825 12.1948 5.14858C12.0876 5.25406 11.9124 5.25406 11.8052 5.14858C11.0833 4.43825 10.0928 4 9 4H5ZM13 18.5585L13.4438 18.4105C14.2596 18.1386 15.1138 18 15.9737 18H19C19.5523 18 20 17.5523 20 17V7C20 6.44772 19.5523 6 19 6H15C13.8954 6 13 6.89543 13 8V18.5585Z" fill="currentColor"/>
</svg>
`,
};

export const icIntegration = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8 5H6C5.44772 5 5 5.44772 5 6V8C5 8.55228 5.44772 9 6 9H8C8.55228 9 9 8.55228 9 8V6C9 5.44772 8.55228 5 8 5ZM6 3C4.34315 3 3 4.34315 3 6V8C3 9.65685 4.34315 11 6 11H8C9.65685 11 11 9.65685 11 8V6C11 4.34315 9.65685 3 8 3H6Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8 15H6C5.44772 15 5 15.4477 5 16V18C5 18.5523 5.44772 19 6 19H8C8.55228 19 9 18.5523 9 18V16C9 15.4477 8.55228 15 8 15ZM6 13C4.34315 13 3 14.3431 3 16V18C3 19.6569 4.34315 21 6 21H8C9.65685 21 11 19.6569 11 18V16C11 14.3431 9.65685 13 8 13H6Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M18 5H16C15.4477 5 15 5.44772 15 6V8C15 8.55228 15.4477 9 16 9H18C18.5523 9 19 8.55228 19 8V6C19 5.44772 18.5523 5 18 5ZM16 3C14.3431 3 13 4.34315 13 6V8C13 9.65685 14.3431 11 16 11H18C19.6569 11 21 9.65685 21 8V6C21 4.34315 19.6569 3 18 3H16Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M18 15H16C15.4477 15 15 15.4477 15 16V18C15 18.5523 15.4477 19 16 19H18C18.5523 19 19 18.5523 19 18V16C19 15.4477 18.5523 15 18 15ZM16 13C14.3431 13 13 14.3431 13 16V18C13 19.6569 14.3431 21 16 21H18C19.6569 21 21 19.6569 21 18V16C21 14.3431 19.6569 13 18 13H16Z" fill="currentColor"/>
</svg>
`,
};

export const icTag = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.68633 12.2678L12.1959 4.75824C12.4024 4.55181 12.6887 4.4459 12.9797 4.46829L18.2325 4.87235C18.7243 4.91018 19.1151 5.30093 19.1529 5.79271L19.557 11.0455C19.5793 11.3366 19.4734 11.6229 19.267 11.8293L11.7574 19.3389C11.3669 19.7294 10.7337 19.7294 10.3432 19.3389L4.68633 13.6821C4.2958 13.2915 4.2958 12.6584 4.68633 12.2678ZM21.5511 10.8921C21.6182 11.7653 21.3005 12.6243 20.6812 13.2435L13.1716 20.7531C12 21.9247 10.1005 21.9247 8.92896 20.7531L3.27211 15.0963C2.10054 13.9247 2.10054 12.0252 3.27211 10.8536L10.7817 3.34402C11.401 2.72476 12.2599 2.40701 13.1331 2.47418L18.3859 2.87824C19.8613 2.99173 21.0335 4.16398 21.147 5.63931L21.5511 10.8921Z" fill="currentColor"/>
<path d="M14.5858 7.31776C14 7.90354 14 8.85329 14.5858 9.43908C15.1716 10.0249 16.1214 10.0249 16.7071 9.43908C17.2929 8.85329 17.2929 7.90354 16.7071 7.31776C16.1214 6.73197 15.1716 6.73197 14.5858 7.31776Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.51469 13.6817L10.3431 16.5101C10.7336 16.9007 11.3668 16.9007 11.7573 16.5101C12.1479 16.1196 12.1479 15.4865 11.7573 15.0959L8.92891 12.2675C8.53838 11.877 7.90522 11.877 7.5147 12.2675C7.12417 12.658 7.12417 13.2912 7.51469 13.6817Z" fill="currentColor"/>
</svg>
`,
};

export const icFAQ = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" fill="currentColor"/>
<circle cx="12" cy="18" r="1" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 8C11.1307 8 10.3886 8.5551 10.1135 9.33325C9.92948 9.85396 9.35815 10.1269 8.83744 9.94284C8.31672 9.75879 8.0438 9.18747 8.22784 8.66675C8.77648 7.11451 10.2568 6 12 6C14.2091 6 16 7.79086 16 10C16 11.8638 14.7252 13.4299 13 13.874V15C13 15.5523 12.5523 16 12 16C11.4477 16 11 15.5523 11 15V13C11 12.4477 11.4477 12 12 12C13.1045 12 14 11.1046 14 10C14 8.89543 13.1045 8 12 8Z" fill="currentColor"/>
</svg>
`,
};

export const icSettings = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 14C13.1046 14 14 13.1046 14 12C14 10.8954 13.1046 10 12 10C10.8954 10 10 10.8954 10 12C10 13.1046 10.8954 14 12 14ZM16 12C16 14.2091 14.2091 16 12 16C9.79086 16 8 14.2091 8 12C8 9.79086 9.79086 8 12 8C14.2091 8 16 9.79086 16 12Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.3791 3L10.9915 4.16266C10.6165 5.28756 9.79908 6.07025 8.96536 6.53447C8.90606 6.56749 8.84735 6.60145 8.78923 6.63634C7.96925 7.12859 6.88111 7.44611 5.71804 7.20811L4.51627 6.96218L4.91723 5.00278L6.11901 5.24871C6.68428 5.36439 7.26514 5.21857 7.75983 4.9216C7.83655 4.87554 7.91408 4.83069 7.9924 4.78709C8.49591 4.50673 8.91189 4.07694 9.09413 3.53021L9.48168 2.36754C9.75391 1.55086 10.5182 1 11.3791 1H12.621C13.4819 1 14.2462 1.55086 14.5184 2.36754L14.9059 3.53021C15.0882 4.07694 15.5042 4.50673 16.0077 4.78709C16.086 4.83069 16.1635 4.87554 16.2402 4.92159C16.7349 5.21857 17.3158 5.36438 17.881 5.2487L19.0828 5.00279C19.9262 4.8302 20.7854 5.21666 21.2158 5.96218L21.8368 7.03775C22.2672 7.78328 22.1723 8.72059 21.6011 9.36469L20.7862 10.2838C20.4042 10.7144 20.2392 11.2888 20.2483 11.8644C20.2497 11.9548 20.2497 12.0452 20.2483 12.1356C20.2392 12.7111 20.4042 13.2855 20.7862 13.7162L21.6011 14.6352C22.1723 15.2793 22.2672 16.2167 21.8368 16.9622L21.2158 18.0378C20.7854 18.7833 19.9262 19.1697 19.0828 18.9971L17.8811 18.7512C17.3158 18.6356 16.735 18.7814 16.2403 19.0784C16.1635 19.1244 16.086 19.1693 16.0077 19.2129C15.5042 19.4933 15.0882 19.9231 14.9059 20.4698L14.5184 21.6325C14.2462 22.4491 13.4819 23 12.621 23H11.3791C10.5182 23 9.75391 22.4491 9.48169 21.6325L9.09413 20.4698C8.91189 19.9231 8.49591 19.4933 7.9924 19.2129C7.91406 19.1693 7.83651 19.1244 7.75977 19.0784C7.26507 18.7814 6.68421 18.6356 6.11892 18.7512L4.91723 18.9971C4.07385 19.1697 3.21465 18.7833 2.78422 18.0378L2.16324 16.9622C1.73281 16.2167 1.82773 15.2793 2.39888 14.6352L3.89529 15.9622L4.51627 17.0378L5.71796 16.7918C6.88105 16.5538 7.9692 16.8714 8.78918 17.3636C8.84732 17.3985 8.90605 17.4325 8.96536 17.4655C9.79908 17.9298 10.6165 18.7124 10.9915 19.8373L11.3791 21L12.621 21L13.0086 19.8373C13.3835 18.7124 14.201 17.9298 15.0347 17.4655C15.094 17.4325 15.1527 17.3985 15.2109 17.3636C16.0309 16.8714 17.119 16.5538 18.2821 16.7919L19.4837 17.0378L20.1047 15.9622L19.2898 15.0431C18.505 14.1581 18.2334 13.0606 18.2486 12.1039C18.2497 12.0346 18.2497 11.9653 18.2486 11.8961C18.2334 10.9394 18.505 9.8418 19.2898 8.95681L20.1047 8.03775L19.4837 6.96218L18.282 7.2081C17.1189 7.4461 16.0308 7.12858 15.2108 6.63633C15.1527 6.60145 15.094 6.56749 15.0347 6.53447C14.201 6.07025 13.3835 5.28756 13.0086 4.16266L12.621 3L11.3791 3ZM2.39888 14.6352L3.89529 15.9622L4.71031 15.0431C5.49507 14.1581 5.76666 13.0605 5.75151 12.1039C5.75041 12.0346 5.75041 11.9653 5.75151 11.8961C5.76667 10.9394 5.49508 9.84185 4.71032 8.95687L3.89529 8.03775L4.51627 6.96218L4.91723 5.00278C4.07385 4.8302 3.21465 5.21665 2.78422 5.96218L2.16324 7.03775C1.73281 7.78328 1.82773 8.72059 2.39888 9.36469L3.21391 10.2838C3.59582 10.7145 3.76088 11.2889 3.75176 11.8644C3.75033 11.9548 3.75033 12.0452 3.75176 12.1355C3.76087 12.7111 3.59582 13.2854 3.21391 13.7161L2.39888 14.6352Z" fill="currentColor"/>
</svg>
`,
};

export const icPreview = {
  viewBox: '0 0 24 24',
  svgInner: `<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
  <path stroke="currentColor" stroke-width="2" d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z"/>
  <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
</svg>
`,
};

export const icAdmin = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.6838 3.71343L5.68377 5.71343C5.27543 5.84954 5 6.23168 5 6.66211V11.9998C5 14.0255 6.01463 15.7552 7.48432 17.2095C8.95718 18.6669 10.7303 19.6838 11.8268 20.2264C11.9405 20.2826 12.0595 20.2826 12.1731 20.2264C13.2697 19.6838 15.0428 18.6669 16.5157 17.2095C17.9854 15.7552 19 14.0255 19 11.9998V6.66211C19 6.23168 18.7246 5.84954 18.3162 5.71343L12.3162 3.71343C12.111 3.645 11.889 3.645 11.6838 3.71343ZM5.05132 3.81606C3.82629 4.2244 3 5.37082 3 6.66211V11.9998C3 17.5018 8.56019 20.8416 10.9399 22.019C11.6125 22.3518 12.3875 22.3518 13.0601 22.019C15.4398 20.8416 21 17.5018 21 11.9998V6.66211C21 5.37082 20.1737 4.2244 18.9487 3.81606L12.9487 1.81606C12.3329 1.61079 11.6671 1.61079 11.0513 1.81606L5.05132 3.81606Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 11C12.5523 11 13 10.5523 13 10C13 9.44772 12.5523 9 12 9C11.4477 9 11 9.44772 11 10C11 10.5523 11.4477 11 12 11ZM12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z" fill="currentColor"/>
<path d="M17.2679 16.3901C15.9848 14.9251 14.1005 14 12 14C9.8996 14 8.01524 14.9251 6.73218 16.3901C6.96567 16.6724 7.21751 16.9456 7.48435 17.2096C7.69865 17.4217 7.9193 17.6244 8.1433 17.8177C9.06039 16.7075 10.4476 16 12 16C13.5525 16 14.9397 16.7075 15.8568 17.8177C16.0808 17.6244 16.3014 17.4217 16.5157 17.2096C16.7826 16.9456 17.0344 16.6724 17.2679 16.3901Z" fill="currentColor"/>
</svg>
`,
};

export const icLeads = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 10C13.6569 10 15 8.65685 15 7C15 5.34315 13.6569 4 12 4C10.3431 4 9 5.34315 9 7C9 8.65685 10.3431 10 12 10ZM12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" fill="currentColor"/>
<path d="M17.8484 13.5596C17.9354 13.571 18.0201 13.5929 18.0994 13.625L18.2244 13.6855L18.3425 13.7646C18.4077 13.8153 18.4666 13.8747 18.5183 13.9404L18.6091 14.0752L19.78 16.0859L19.9089 16.1133L22.0476 16.5762L22.0574 16.5791L22.2097 16.6221C22.2489 16.6365 22.2878 16.6528 22.325 16.6719L22.4392 16.7412L22.5369 16.8193L22.6414 16.9258C22.6947 16.9895 22.7397 17.06 22.7761 17.1348L22.8298 17.2666L22.863 17.4014C22.8805 17.4981 22.8854 17.5967 22.8748 17.6943L22.8591 17.793C22.8317 17.9232 22.7778 18.0461 22.7039 18.1553L22.6199 18.2646L21.1443 19.915L21.0564 20.0137L21.0701 20.1455L21.2918 22.3301L21.2976 22.4883C21.2941 22.5859 21.2772 22.683 21.2459 22.7764L21.2097 22.8701C21.1549 22.9925 21.0765 23.1027 20.9802 23.1943L20.8777 23.2793C20.7693 23.358 20.6459 23.4149 20.5164 23.4463L20.3845 23.4697C20.2519 23.4838 20.1174 23.4706 19.99 23.4336L19.8572 23.3848L17.8357 22.4941L17.7146 22.4404L17.5935 22.4941L15.5671 23.3877C15.4442 23.4418 15.3122 23.4712 15.1794 23.4746L15.0466 23.4697C14.9133 23.4555 14.784 23.4143 14.6668 23.3506L14.5544 23.2793C14.4456 23.2002 14.3529 23.101 14.283 22.9873L14.2214 22.8701C14.1485 22.7069 14.1194 22.5267 14.1375 22.3486L14.3601 20.1445L14.3728 20.0127L14.2849 19.9141L12.8113 18.2637L12.7976 18.248C12.7149 18.1516 12.6502 18.0413 12.6082 17.9219L12.572 17.7939C12.5356 17.621 12.5439 17.4408 12.5974 17.2725L12.6531 17.1338C12.709 17.0192 12.7864 16.9157 12.8806 16.8291L12.9949 16.7383C13.1042 16.6635 13.2273 16.61 13.3562 16.582L15.5203 16.1133L15.6492 16.0859L15.7166 15.9717L16.8318 14.0586L16.9109 13.9404C16.9906 13.8392 17.0895 13.7527 17.2019 13.6875L17.3269 13.626C17.4479 13.5764 17.5791 13.5503 17.7117 13.5498L17.8484 13.5596ZM17.0632 17.2695L17.0437 17.3027C16.9899 17.395 16.922 17.4781 16.8435 17.5488L16.7615 17.6152C16.6521 17.6946 16.5272 17.7511 16.3962 17.7822L16.3748 17.7871L15.073 18.0684L15.4158 18.4531L15.9558 19.0576L16.031 19.1523C16.1564 19.3283 16.2243 19.5397 16.2224 19.7559L16.2166 19.8486L16.1335 20.6621L16.0798 21.1758L17.2966 20.6396L17.406 20.5977C17.503 20.567 17.6055 20.5516 17.7078 20.5508L17.822 20.5576C17.8909 20.5649 17.9584 20.5777 18.0222 20.5977L18.1326 20.6396L19.3484 21.1758L19.2146 19.8496L19.2087 19.7363C19.2092 19.6856 19.2135 19.635 19.2214 19.585L19.2585 19.4307L19.2966 19.3311C19.3392 19.2352 19.397 19.1446 19.4685 19.0645L20.3572 18.0684L19.0544 17.7871C18.9497 17.7645 18.8493 17.7256 18.7576 17.6729L18.6697 17.6152C18.5553 17.5322 18.4592 17.4257 18.3875 17.3027L17.7146 16.1484L17.0632 17.2695ZM13.1668 16.3232C13.0848 16.3498 13.0055 16.384 12.9304 16.4258C12.9679 16.4049 13.0063 16.3853 13.0457 16.3682L13.1668 16.3232Z" fill="currentColor"/>
<path d="M11 15C11 15.5523 10.5523 16 10 16H9C6.79086 16 5 17.7909 5 20V21C5 21.5523 4.55228 22 4 22C3.44772 22 3 21.5523 3 21V20C3 16.6863 5.68629 14 9 14H10C10.5523 14 11 14.4477 11 15Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.8484 13.5596C17.9354 13.571 18.0201 13.5929 18.0994 13.625L18.2244 13.6855L18.3425 13.7646C18.4077 13.8153 18.4666 13.8747 18.5183 13.9404L18.6091 14.0752L19.78 16.0859L19.9089 16.1133L22.0476 16.5762L22.0574 16.5791L22.2097 16.6221C22.2489 16.6365 22.2878 16.6528 22.325 16.6719L22.4392 16.7412L22.5369 16.8193L22.6414 16.9258C22.6947 16.9895 22.7397 17.06 22.7761 17.1348L22.8298 17.2666L22.863 17.4014C22.8805 17.4981 22.8854 17.5967 22.8748 17.6943L22.8591 17.793C22.8317 17.9232 22.7778 18.0461 22.7039 18.1553L22.6199 18.2646L21.1443 19.915L21.0564 20.0137L21.0701 20.1455L21.2918 22.3301L21.2976 22.4883C21.2941 22.5859 21.2772 22.683 21.2459 22.7764L21.2097 22.8701C21.1549 22.9925 21.0765 23.1027 20.9802 23.1943L20.8777 23.2793C20.7693 23.358 20.6459 23.4149 20.5164 23.4463L20.3845 23.4697C20.2519 23.4838 20.1174 23.4706 19.99 23.4336L19.8572 23.3848L17.8357 22.4941L17.7146 22.4404L17.5935 22.4941L15.5671 23.3877C15.4442 23.4418 15.3122 23.4712 15.1794 23.4746L15.0466 23.4697C14.9133 23.4555 14.784 23.4143 14.6668 23.3506L14.5544 23.2793C14.4456 23.2002 14.3529 23.101 14.283 22.9873L14.2214 22.8701C14.1485 22.7069 14.1194 22.5267 14.1375 22.3486L14.3601 20.1445L14.3728 20.0127L14.2849 19.9141L12.8113 18.2637L12.7976 18.248C12.7149 18.1516 12.6502 18.0413 12.6082 17.9219L12.572 17.7939C12.5356 17.621 12.5439 17.4408 12.5974 17.2725L12.6531 17.1338C12.709 17.0192 12.7864 16.9157 12.8806 16.8291L12.9949 16.7383C13.1042 16.6635 13.2273 16.61 13.3562 16.582L15.5203 16.1133L15.6492 16.0859L15.7166 15.9717L16.8318 14.0586L16.9109 13.9404C16.9906 13.8392 17.0895 13.7527 17.2019 13.6875L17.3269 13.626C17.4479 13.5764 17.5791 13.5503 17.7117 13.5498L17.8484 13.5596ZM17.0632 17.2695L17.0437 17.3027C16.9899 17.395 16.922 17.4781 16.8435 17.5488L16.7615 17.6152C16.6521 17.6946 16.5272 17.7511 16.3962 17.7822L16.3748 17.7871L15.073 18.0684L15.4158 18.4531L15.9558 19.0576L16.031 19.1523C16.1564 19.3283 16.2243 19.5397 16.2224 19.7559L16.2166 19.8486L16.1335 20.6621L16.0798 21.1758L17.2966 20.6396L17.406 20.5977C17.503 20.567 17.6055 20.5516 17.7078 20.5508L17.822 20.5576C17.8909 20.5649 17.9584 20.5777 18.0222 20.5977L18.1326 20.6396L19.3484 21.1758L19.2146 19.8496L19.2087 19.7363C19.2092 19.6856 19.2135 19.635 19.2214 19.585L19.2585 19.4307L19.2966 19.3311C19.3392 19.2352 19.397 19.1446 19.4685 19.0645L20.3572 18.0684L19.0544 17.7871C18.9497 17.7645 18.8493 17.7256 18.7576 17.6729L18.6697 17.6152C18.5553 17.5322 18.4592 17.4257 18.3875 17.3027L17.7146 16.1484L17.0632 17.2695Z" fill="currentColor"/>
</svg>`,
};

export const icInfo = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"> <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11h2v5m-2 0h4m-2.592-8.5h.01M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/> </svg>',
};
export const icTrash = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M10 10a1 1 0 0 1 1 1v5a1 1 0 1 1-2 0v-5a1 1 0 0 1 1-1m4 0a1 1 0 0 1 1 1v5a1 1 0 1 1-2 0v-5a1 1 0 0 1 1-1" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M10 2a3 3 0 0 0-3 3H3a1 1 0 0 0 0 2h1v12a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V7h1a1 1 0 1 0 0-2h-4a3 3 0 0 0-3-3zm5 3a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1zM7 7H6v12a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V7z" fill="currentColor"/></svg>',
};

export const icSend = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.192 6.579C1.36 4.086 3.909 1.805 6.295 2.906l13.802 6.37c2.324 1.073 2.324 4.376 0 5.448l-13.802 6.37c-2.386 1.101-4.934-1.18-4.103-3.672l1.49-4.473a3 3 0 0 0 0-1.897zm3.265-1.857 13.802 6.37a1 1 0 0 1 0 1.816l-13.802 6.37c-.795.367-1.645-.393-1.368-1.224l1.491-4.473q.096-.286.156-.58h8.263a1 1 0 0 0 0-2H5.736a5 5 0 0 0-.156-.582L4.09 5.946c-.278-.83.572-1.591 1.367-1.224" fill="currentColor"/></svg>',
};
export const icPaperPlane = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg class="w-6 h-6 text-gray-800 dark:text-white rotate-45" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"> <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m12 18-7 3 7-18 7 18-7-3Zm0 0v-5"/> </svg>',
};
export const icAdminUser = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.6838 3.71343L5.68377 5.71343C5.27543 5.84954 5 6.23168 5 6.66211V11.9998C5 14.0255 6.01463 15.7552 7.48432 17.2095C8.95718 18.6669 10.7303 19.6838 11.8268 20.2264C11.9405 20.2826 12.0595 20.2826 12.1731 20.2264C13.2697 19.6838 15.0428 18.6669 16.5157 17.2095C17.9854 15.7552 19 14.0255 19 11.9998V6.66211C19 6.23168 18.7246 5.84954 18.3162 5.71343L12.3162 3.71343C12.111 3.645 11.889 3.645 11.6838 3.71343ZM5.05132 3.81606C3.82629 4.2244 3 5.37082 3 6.66211V11.9998C3 17.5018 8.56019 20.8416 10.9399 22.019C11.6125 22.3518 12.3875 22.3518 13.0601 22.019C15.4398 20.8416 21 17.5018 21 11.9998V6.66211C21 5.37082 20.1737 4.2244 18.9487 3.81606L12.9487 1.81606C12.3329 1.61079 11.6671 1.61079 11.0513 1.81606L5.05132 3.81606Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 11C12.5523 11 13 10.5523 13 10C13 9.44772 12.5523 9 12 9C11.4477 9 11 9.44772 11 10C11 10.5523 11.4477 11 12 11ZM12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z" fill="currentColor"/>
<path d="M17.2681 16.3901C15.9851 14.9251 14.1007 14 12.0003 14C9.89984 14 8.01548 14.9251 6.73242 16.3901C6.96591 16.6724 7.21775 16.9456 7.4846 17.2096C7.69889 17.4217 7.91955 17.6244 8.14355 17.8177C9.06063 16.7075 10.4478 16 12.0003 16C13.5527 16 14.9399 16.7075 15.857 17.8177C16.081 17.6244 16.3017 17.4217 16.516 17.2096C16.7828 16.9456 17.0346 16.6724 17.2681 16.3901Z" fill="currentColor"/>
</svg>
`,
};
export const icUserEdit = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">\n' +
    '  <path stroke="currentColor" stroke-linecap="square" stroke-linejoin="round" stroke-width="2" d="M7 19H5a1 1 0 0 1-1-1v-1a3 3 0 0 1 3-3h1m4-6a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm7.441 1.559a1.907 1.907 0 0 1 0 2.698l-6.069 6.069L10 19l.674-3.372 6.07-6.07a1.907 1.907 0 0 1 2.697 0Z"/>\n' +
    '</svg>\n',
};
export const icHeadset = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">\n' +
    '  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.079 6.839a3 3 0 0 0-4.255.1M13 20h1.083A3.916 3.916 0 0 0 18 16.083V9A6 6 0 1 0 6 9v7m7 4v-1a1 1 0 0 0-1-1h-1a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1Zm-7-4v-6H5a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h1Zm12-6h1a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-1v-6Z"/>\n' +
    '</svg>',
};
export const icClose = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6"> <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" /> </svg>',
};
export const heroXMarkCircle = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6"> <path stroke-linecap="round" stroke-linejoin="round" d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /> </svg>',
};

export const icAlarm = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg class="text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">\n' +
    '                <path fill-rule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clip-rule="evenodd"/>\n' +
    '            </svg>',
};

export const faMessage = {
  viewBox: '0 0 512 512',
  svgInner:
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path d="M160 368c26.5 0 48 21.5 48 48v16l72.5-54.4c8.3-6.2 18.4-9.6 28.8-9.6H448c8.8 0 16-7.2 16-16V64c0-8.8-7.2-16-16-16H64c-8.8 0-16 7.2-16 16V352c0 8.8 7.2 16 16 16h96zm48 124l-.2 .2-5.1 3.8-17.1 12.8c-4.8 3.6-11.3 4.2-16.8 1.5s-8.8-8.2-8.8-14.3V474.7v-6.4V468v-4V416H112 64c-35.3 0-64-28.7-64-64V64C0 28.7 28.7 0 64 0H448c35.3 0 64 28.7 64 64V352c0 35.3-28.7 64-64 64H309.3L208 492z"></path></svg>',
};

export const heroArrowLeftOnRectangle = {
  viewBox: '0 0 512 512',
  svgInner:
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path d="M464 256A208 208 0 1 0 48 256a208 208 0 1 0 416 0zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zM188.3 147.1c7.6-4.2 16.8-4.1 24.3 .5l144 88c7.1 4.4 11.5 12.1 11.5 20.5s-4.4 16.1-11.5 20.5l-144 88c-7.4 4.5-16.7 4.7-24.3 .5s-12.3-12.2-12.3-20.9V168c0-8.7 4.7-16.7 12.3-20.9z"></path></svg>',
};

export const heroArrowRightOnRectangle = {
  viewBox: '0 0 512 512',
  svgInner:
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="transform: scaleX(-1);" fill="currentColor"><path d="M464 256A208 208 0 1 0 48 256a208 208 0 1 0 416 0zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zM188.3 147.1c7.6-4.2 16.8-4.1 24.3 .5l144 88c7.1 4.4 11.5 12.1 11.5 20.5s-4.4 16.1-11.5 20.5l-144 88c-7.4 4.5-16.7 4.7-24.3 .5s-12.3-12.2-12.3-20.9V168c0-8.7 4.7-16.7 12.3-20.9z"></path></svg>',
};

export const heroInformationCircle = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" /></svg>',
};

export const faCirclePlay = {
  viewBox: '0 0 512 512',
  svgInner:
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path d="M464 256A208 208 0 1 0 48 256a208 208 0 1 0 416 0zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zM188.3 147.1c7.6-4.2 16.8-4.1 24.3 .5l144 88c7.1 4.4 11.5 12.1 11.5 20.5s-4.4 16.1-11.5 20.5l-144 88c-7.4 4.5-16.7 4.7-24.3 .5s-12.3-12.2-12.3-20.9V168c0-8.7 4.7-16.7 12.3-20.9z"></path></svg>',
};

export const faLinkSimple = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"><path d="M9.75 4.75c3 0 4.5 1.5 4.5 3.25s-1.5 3.25-4.5 3.25M5.75 8h4.5m-4-3.25c-3 0-4.5 1.5-4.5 3.25s1.5 3.25 4.5 3.25"/></svg>',
};

export const faCircleStop = {
  viewBox: '0 0 512 512',
  svgInner:
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"><path d="M464 256A208 208 0 1 0 48 256a208 208 0 1 0 416 0zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zm192-96H320c17.7 0 32 14.3 32 32V320c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V192c0-17.7 14.3-32 32-32z"></path></svg>',
};
export const icLogo = {
  viewBox: '0 0 148 36',
  svgInner:
    '<svg width="148" height="36" viewBox="0 0 148 36" fill="none" xmlns="http://www.w3.org/2000/svg">' +
    '<path d="M1.98 30V7.65h7.29q3.63 0 6.3 1.44t4.14 3.96 1.47 5.76-1.47 5.79q-1.47 2.52-4.14 3.96T9.27 30zm4.65-4.05h2.76q2.19 0 3.78-.87 1.59-.9 2.46-2.49.9-1.62.9-3.78 0-2.19-.9-3.78a6.07 6.07 0 0 0-2.46-2.46q-1.59-.87-3.78-.87H6.63zm38.598 4.314a8.4 8.4 0 0 1-3.278-.638 8.1 8.1 0 0 1-2.618-1.782 8.4 8.4 0 0 1-1.76-2.684q-.615-1.54-.616-3.366 0-1.826.616-3.366a7.8 7.8 0 0 1 1.738-2.684 7.8 7.8 0 0 1 2.618-1.76q1.518-.638 3.3-.638t3.19.594q1.43.594 2.42 1.584t1.408 2.2l-3.014 1.452a4.04 4.04 0 0 0-1.474-1.958q-1.035-.792-2.53-.792-1.452 0-2.552.682A4.66 4.66 0 0 0 40.96 19q-.594 1.188-.594 2.794t.594 2.816q.616 1.21 1.716 1.892t2.552.682q1.495 0 2.53-.77 1.056-.792 1.474-1.98l3.014 1.452q-.418 1.21-1.408 2.2t-2.42 1.584q-1.408.594-3.19.594m17.192 0q-1.848 0-3.432-.638a8.6 8.6 0 0 1-2.772-1.782 8.6 8.6 0 0 1-1.826-2.706q-.66-1.54-.66-3.344t.638-3.344a8.3 8.3 0 0 1 1.826-2.684 8.6 8.6 0 0 1 2.772-1.782q1.584-.638 3.454-.638t3.454.638a8.3 8.3 0 0 1 2.75 1.782 7.9 7.9 0 0 1 1.826 2.684q.66 1.54.66 3.344t-.66 3.344a8.32 8.32 0 0 1-4.598 4.488q-1.584.638-3.432.638m0-3.08q1.122 0 2.068-.396a5.3 5.3 0 0 0 1.694-1.1 5.05 5.05 0 0 0 1.122-1.716q.396-.99.396-2.178t-.396-2.156a5.05 5.05 0 0 0-1.122-1.716 4.8 4.8 0 0 0-1.694-1.1 5.3 5.3 0 0 0-2.068-.396q-1.122 0-2.09.396-.946.374-1.672 1.1a5.05 5.05 0 0 0-1.122 1.716q-.396.968-.396 2.156t.396 2.178 1.122 1.716q.726.704 1.672 1.1a5.5 5.5 0 0 0 2.09.396M73.308 30V13.61h2.618l8.8 11.594-1.408.33V13.61h3.41V30h-2.64l-8.646-11.682 1.276-.33V30zm16.108 0V13.61h2.618l8.801 11.594-1.408.33V13.61h3.41V30h-2.64L91.55 18.318l1.276-.33V30zm16.109 0V13.61h11.022v2.97h-7.612v3.718h7.172v2.97h-7.172v3.762h7.612V30zm20.523.264a8.4 8.4 0 0 1-3.278-.638 8.1 8.1 0 0 1-2.618-1.782 8.4 8.4 0 0 1-1.76-2.684q-.616-1.54-.616-3.366t.616-3.366a7.8 7.8 0 0 1 1.738-2.684 7.8 7.8 0 0 1 2.618-1.76q1.518-.638 3.3-.638t3.19.594q1.43.594 2.42 1.584t1.408 2.2l-3.014 1.452a4.04 4.04 0 0 0-1.474-1.958q-1.035-.792-2.53-.792-1.452 0-2.552.682A4.65 4.65 0 0 0 121.78 19q-.594 1.188-.594 2.794t.594 2.816q.615 1.21 1.716 1.892t2.552.682q1.495 0 2.53-.77 1.056-.792 1.474-1.98l3.014 1.452q-.418 1.21-1.408 2.2t-2.42 1.584q-1.408.594-3.19.594M137.982 30V16.58h-4.158v-2.97h11.66v2.97h-4.092V30z" fill="#282826"/>' +
    '<path d="m22.12 30 5.368-8.206-5.368-8.184h3.916l4.312 6.556h-1.914l4.29-6.556h3.916l-5.346 8.184L36.64 30h-3.916l-4.29-6.578 1.914.022L26.036 30z" fill="#7F75CF"/></svg>',
};

export const icLogoDark = {
  viewBox: '0 0 148 36',
  svgInner:
    '<svg width="148" height="36" viewBox="0 0 148 36" fill="none" xmlns="http://www.w3.org/2000/svg">' +
    '<path d="M1.914 30V8.395h7.047q3.51 0 6.09 1.392t4.002 3.828q1.42 2.436 1.421 5.568 0 3.132-1.421 5.597-1.42 2.436-4.002 3.828Q12.471 30 8.961 30zm4.495-3.915h2.668q2.117 0 3.654-.841a6.06 6.06 0 0 0 2.378-2.407q.87-1.566.87-3.654 0-2.117-.87-3.654a5.87 5.87 0 0 0-2.378-2.378q-1.538-.84-3.654-.841H6.409zm38.09 4.179a8.4 8.4 0 0 1-3.278-.638 8.1 8.1 0 0 1-2.618-1.782 8.4 8.4 0 0 1-1.76-2.684q-.616-1.54-.616-3.366t.616-3.366a7.8 7.8 0 0 1 1.738-2.684 7.8 7.8 0 0 1 2.618-1.76q1.518-.638 3.3-.638t3.19.594q1.43.594 2.42 1.584t1.408 2.2l-3.014 1.452a4.04 4.04 0 0 0-1.474-1.958q-1.035-.792-2.53-.792-1.453 0-2.552.682A4.66 4.66 0 0 0 40.23 19q-.594 1.188-.594 2.794t.594 2.816q.615 1.21 1.716 1.892t2.552.682q1.495 0 2.53-.77 1.056-.792 1.474-1.98l3.014 1.452q-.419 1.21-1.408 2.2-.99.99-2.42 1.584-1.409.594-3.19.594m17.192 0q-1.848 0-3.432-.638a8.6 8.6 0 0 1-2.772-1.782 8.6 8.6 0 0 1-1.826-2.706q-.66-1.54-.66-3.344t.638-3.344a8.3 8.3 0 0 1 1.826-2.684 8.6 8.6 0 0 1 2.772-1.782q1.584-.638 3.454-.638t3.454.638a8.3 8.3 0 0 1 2.75 1.782 7.9 7.9 0 0 1 1.826 2.684q.66 1.54.66 3.344t-.66 3.344a8.3 8.3 0 0 1-4.598 4.488q-1.584.638-3.432.638m0-3.08q1.122 0 2.068-.396a5.3 5.3 0 0 0 1.694-1.1 5.05 5.05 0 0 0 1.122-1.716q.396-.99.396-2.178t-.396-2.156a5.05 5.05 0 0 0-1.122-1.716 4.8 4.8 0 0 0-1.694-1.1 5.3 5.3 0 0 0-2.068-.396q-1.122 0-2.09.396-.946.374-1.672 1.1a5.05 5.05 0 0 0-1.122 1.716q-.396.968-.396 2.156t.396 2.178 1.122 1.716q.726.704 1.672 1.1a5.5 5.5 0 0 0 2.09.396M72.579 30V13.61h2.618l8.8 11.594-1.408.33V13.61H86V30h-2.64l-8.646-11.682 1.276-.33V30zm16.108 0V13.61h2.618l8.8 11.594-1.408.33V13.61h3.41V30h-2.64l-8.646-11.682 1.276-.33V30zm16.108 0V13.61h11.022v2.97h-7.612v3.718h7.172v2.97h-7.172v3.762h7.612V30zm20.523.264a8.4 8.4 0 0 1-3.278-.638 8.1 8.1 0 0 1-2.618-1.782 8.4 8.4 0 0 1-1.76-2.684q-.615-1.54-.616-3.366 0-1.826.616-3.366a7.8 7.8 0 0 1 1.738-2.684 7.8 7.8 0 0 1 2.618-1.76q1.518-.638 3.3-.638 1.783 0 3.19.594 1.43.594 2.42 1.584t1.408 2.2l-3.014 1.452a4.04 4.04 0 0 0-1.474-1.958q-1.033-.792-2.53-.792-1.451 0-2.552.682A4.67 4.67 0 0 0 121.05 19q-.594 1.188-.594 2.794t.594 2.816a4.67 4.67 0 0 0 1.716 1.892q1.1.682 2.552.682 1.497 0 2.53-.77 1.056-.792 1.474-1.98l3.014 1.452q-.417 1.21-1.408 2.2t-2.42 1.584q-1.407.594-3.19.594M137.253 30V16.58h-4.158v-2.97h11.66v2.97h-4.092V30z" fill="#F8FAFF"/>' +
    '<path d="m21.39 30 5.369-8.206-5.369-8.184h3.916l4.313 6.556h-1.915l4.29-6.556h3.916l-5.345 8.184L35.91 30h-3.915l-4.29-6.578 1.914.022L25.307 30z" fill="#7F75CF"/></svg>',
};

export const icLinkPrefix = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2 12C2 9.23858 4.23858 7 7 7H10C10.5523 7 11 7.44772 11 8C11 8.55228 10.5523 9 10 9H7C5.34315 9 4 10.3431 4 12C4 13.6569 5.34315 15 7 15H10C10.5523 15 11 15.4477 11 16C11 16.5523 10.5523 17 10 17H7C4.23858 17 2 14.7614 2 12Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13 8C13 7.44772 13.4477 7 14 7H17C19.7614 7 22 9.23858 22 12C22 14.7614 19.7614 17 17 17H14C13.4477 17 13 16.5523 13 16C13 15.4477 13.4477 15 14 15H17C18.6569 15 20 13.6569 20 12C20 10.3431 18.6569 9 17 9H14C13.4477 9 13 8.55228 13 8Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8 12C8 11.4477 8.44772 11 9 11H15C15.5523 11 16 11.4477 16 12C16 12.5523 15.5523 13 15 13H9C8.44772 13 8 12.5523 8 12Z" fill="currentColor"/>
</svg>
  `,
};

export const icHeadPhone = {
  viewBox: '0 0 24 24',
  svgInner: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M3 18v-6a9 9 0 0 1 18 0v6"/><path d="M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z"/></svg>`,
};

export const icSun = {
  viewBox: '0 0 24 24',
  svgInner: `<svg viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M12 0a1 1 0 0 1 1 1v2a1 1 0 1 1-2 0V1a1 1 0 0 1 1-1M0 12a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2H1a1 1 0 0 1-1-1m21-1a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2zm-8 10a1 1 0 1 0-2 0v2a1 1 0 1 0 2 0zm-6.657-3.343a1 1 0 0 1 0 1.414L4.93 20.485a1 1 0 1 1-1.414-1.414l1.414-1.414a1 1 0 0 1 1.414 0ZM20.485 3.515a1 1 0 0 1 0 1.414l-1.414 1.414a1 1 0 1 1-1.414-1.414l1.414-1.414a1 1 0 0 1 1.414 0m-16.97 0a1 1 0 0 1 1.414 0l1.414 1.414A1 1 0 1 1 4.93 6.343L3.515 4.93a1 1 0 0 1 0-1.414Zm14.142 14.142a1 1 0 0 1 1.414 0l1.414 1.414a1 1 0 1 1-1.414 1.414l-1.414-1.414a1 1 0 0 1 0-1.414M5 12a7 7 0 1 1 14 0 7 7 0 0 1-14 0"/></svg>`,
};

export const icMoon = {
  viewBox: '0 0 24 24',
  svgInner: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79"/></svg>`,
};

export const icDialpad = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"><circle cx="7" cy="5" r="1"/><circle cx="12" cy="5" r="1"/><circle cx="17" cy="5" r="1"/><circle cx="7" cy="10" r="1"/><circle cx="12" cy="10" r="1"/><circle cx="17" cy="10" r="1"/><circle cx="7" cy="15" r="1"/><circle cx="12" cy="15" r="1"/><circle cx="12" cy="20" r="1"/><circle cx="17" cy="15" r="1"/></svg>',
};

export const icUserShield = {
  viewBox: '0 0 24 24',
  svgInner: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960" fill="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M480-440q-59 0-99.5-40.5T340-580t40.5-99.5T480-720t99.5 40.5T620-580t-40.5 99.5T480-440m0-80q26 0 43-17t17-43-17-43-43-17-43 17-17 43 17 43 43 17m0 440q-139-35-229.5-159.5T160-516v-244l320-120 320 120v244q0 152-90.5 276.5T480-80m0-715-240 90v189q0 54 15 105t41 96q42-21 88-33t96-12 96 12 88 33q26-45 41-96t15-105v-189zm0 515q-36 0-70 8t-65 22q29 30 63 52t72 34q38-12 72-34t63-52q-31-14-65-22t-70-8"/></svg>`,
};

export const icUnion = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path d="M21.951 18H2.05c.501-5.053 4.764-9 9.95-9s9.449 3.946 9.95 9Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/><path fill="currentColor" d="M8.5 14.5a1 1 0 0 1-1 1 1 1 0 0 1-1-1 1 1 0 0 1 2 0m9 0a1 1 0 0 1-1 1 1 1 0 0 1-1-1 1 1 0 0 1 2 0"/></svg>',
};
export const icDrag = {
  viewBox: '0 0 24 24',
  svgInner:
    '<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><circle fill="currentColor" cx="5.5" cy="5.5" r="2"/><circle fill="currentColor" cx="12.5" cy="5.5" r="2"/><circle fill="currentColor" cx="19.5" cy="5.5" r="2"/><circle fill="currentColor" cx="5.5" cy="12.5" r="2"/><circle fill="currentColor" cx="12.5" cy="12.5" r="2"/><circle fill="currentColor" cx="19.5" cy="12.5" r="2"/><circle fill="currentColor" cx="5.5" cy="19.5" r="2"/><circle fill="currentColor" cx="12.5" cy="19.5" r="2"/><circle fill="currentColor" cx="19.5" cy="19.5" r="2"/></svg>',
};

export const icWebDev = {
  viewBox: '0 0 20 20',
  svgInner: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.83464 5.00033C5.83464 5.46056 5.46154 5.83366 5.0013 5.83366C4.54106 5.83366 4.16797 5.46056 4.16797 5.00033C4.16797 4.54009 4.54106 4.16699 5.0013 4.16699C5.46154 4.16699 5.83464 4.54009 5.83464 5.00033Z" fill="currentColor"/>
<path d="M8.33464 5.00033C8.33464 5.46056 7.96154 5.83366 7.5013 5.83366C7.04106 5.83366 6.66797 5.46056 6.66797 5.00033C6.66797 4.54009 7.04106 4.16699 7.5013 4.16699C7.96154 4.16699 8.33464 4.54009 8.33464 5.00033Z" fill="currentColor"/>
<path d="M10.8346 5.00033C10.8346 5.46056 10.4615 5.83366 10.0013 5.83366C9.54106 5.83366 9.16797 5.46056 9.16797 5.00033C9.16797 4.54009 9.54106 4.16699 10.0013 4.16699C10.4615 4.16699 10.8346 4.54009 10.8346 5.00033Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.3261 14.5176C12.0007 14.1922 12.0007 13.6646 12.3261 13.3391L13.1654 12.4998L12.3261 11.6605C12.0007 11.3351 12.0007 10.8074 12.3261 10.482C12.6515 10.1565 13.1792 10.1565 13.5046 10.482L14.9332 11.9106C15.2586 12.236 15.2586 12.7636 14.9332 13.0891L13.5046 14.5176C13.1792 14.8431 12.6515 14.8431 12.3261 14.5176Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.67389 14.5176C7.99933 14.1922 7.99933 13.6646 7.67389 13.3391L6.83457 12.4998L7.67389 11.6605C7.99933 11.3351 7.99933 10.8074 7.67389 10.482C7.34845 10.1565 6.82082 10.1565 6.49538 10.482L5.06681 11.9106C4.74137 12.236 4.74137 12.7636 5.06681 13.0891L6.49538 14.5176C6.82082 14.8431 7.34845 14.8431 7.67389 14.5176Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.9423 9.19829C10.4997 9.07186 10.0385 9.3281 9.91206 9.77063L8.48349 14.7706C8.35705 15.2132 8.61329 15.6744 9.05582 15.8008C9.49835 15.9273 9.95959 15.671 10.086 15.2285L11.5146 10.2285C11.641 9.78597 11.3848 9.32473 10.9423 9.19829Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M1.66797 4.16699C1.66797 2.78628 2.78726 1.66699 4.16797 1.66699H15.8346C17.2153 1.66699 18.3346 2.78628 18.3346 4.16699V15.8337C18.3346 17.2144 17.2153 18.3337 15.8346 18.3337H4.16797C2.78726 18.3337 1.66797 17.2144 1.66797 15.8337V4.16699ZM4.16797 3.33366H15.8346C16.2949 3.33366 16.668 3.70675 16.668 4.16699V6.66699H3.33464V4.16699C3.33464 3.70675 3.70773 3.33366 4.16797 3.33366ZM3.33464 8.33366V15.8337C3.33464 16.2939 3.70773 16.667 4.16797 16.667H15.8346C16.2949 16.667 16.668 16.2939 16.668 15.8337V8.33366H3.33464Z" fill="currentColor"/>
</svg>
`,
};

export const icCopy = {
  viewBox: '0 0 24 24',
  svgInner: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 5h8a1 1 0 0 1 1 1v1h2V6a3 3 0 0 0-3-3H6a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h1v-2H6a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M18 9h-8a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-8a1 1 0 0 0-1-1m-8-2a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3v-8a3 3 0 0 0-3-3z" fill="currentColor"/></svg>`,
};
export const icEdit = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M14.536 3.807a3 3 0 0 1 4.242 0l1.415 1.415a3 3 0 0 1 0 4.242L9.8 19.857a1 1 0 0 1-.511.273l-5.303 1.06a1 1 0 0 1-1.177-1.176l1.06-5.303a1 1 0 0 1 .274-.511zm2.828 1.415 1.414 1.414a1 1 0 0 1 0 1.414l-1.414 1.414-2.828-2.828 1.414-1.414a1 1 0 0 1 1.414 0M13.121 8.05l-7.35 7.35-.707 3.536 3.536-.708 7.35-7.35z" fill="currentColor"/></svg>`,
};

export const icSearch = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M18 11C18 14.866 14.866 18 11 18C7.13401 18 4 14.866 4 11C4 7.13401 7.13401 4 11 4C14.866 4 18 7.13401 18 11ZM18.0319 16.6177C19.2635 15.078 20 13.125 20 11C20 6.02944 15.9706 2 11 2C6.02944 2 2 6.02944 2 11C2 15.9706 6.02944 20 11 20C13.125 20 15.078 19.2635 16.6177 18.0319L19.2929 20.7071C19.6834 21.0976 20.3166 21.0976 20.7071 20.7071C21.0976 20.3166 21.0976 19.6834 20.7071 19.2929L18.0319 16.6177Z" fill="currentColor"/>
</svg>
`,
};

export const icAgentFlow = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6.25 9.75H3.25C2.83579 9.75 2.5 10.0858 2.5 10.5V13.5C2.5 13.9142 2.83579 14.25 3.25 14.25H6.25C6.66421 14.25 7 13.9142 7 13.5V10.5C7 10.0858 6.66421 9.75 6.25 9.75Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M20.5 3.75H16C15.5858 3.75 15.25 4.08579 15.25 4.5V9C15.25 9.41421 15.5858 9.75 16 9.75H20.5C20.9142 9.75 21.25 9.41421 21.25 9V4.5C21.25 4.08579 20.9142 3.75 20.5 3.75Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M20.5 14.25H16C15.5858 14.25 15.25 14.5858 15.25 15V19.5C15.25 19.9142 15.5858 20.25 16 20.25H20.5C20.9142 20.25 21.25 19.9142 21.25 19.5V15C21.25 14.5858 20.9142 14.25 20.5 14.25Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7 12H11.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M15.25 17.25H13C12.6022 17.25 12.2206 17.092 11.9393 16.8107C11.658 16.5294 11.5 16.1478 11.5 15.75V8.25C11.5 7.85218 11.658 7.47064 11.9393 7.18934C12.2206 6.90804 12.6022 6.75 13 6.75H15.25" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
};

export const icBasicFlow = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_754_14963)">
<path d="M4.5 19.5C6.15685 19.5 7.5 18.1569 7.5 16.5C7.5 14.8431 6.15685 13.5 4.5 13.5C2.84315 13.5 1.5 14.8431 1.5 16.5C1.5 18.1569 2.84315 19.5 4.5 19.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M19.5 4.5L22.5 7.5L19.5 10.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.5 16.5C15.75 16.5 11.25 7.5 19.5 7.5H22.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_754_14963">
<rect width="24" height="24" fill="none"/>
</clipPath>
</defs>
</svg>
`,
};

export const icApi = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.4082 3C10.8219 3.00009 12.0141 4.05323 12.1895 5.45605L13.7949 18.2959C13.8452 18.6979 14.1867 18.9999 14.5918 19C14.9604 19 15.2816 18.7491 15.3711 18.3916L16.6514 13.2725C16.9605 12.0359 18.012 11.1425 19.2588 11.0166C19.6022 10.4098 20.253 10 21 10C22.1046 10 23 10.8954 23 12C23 13.1046 22.1046 14 21 14C20.2766 14 19.6451 13.6145 19.2939 13.0391C18.9539 13.1337 18.6807 13.4023 18.5918 13.7578L17.3115 18.877C16.9994 20.1247 15.878 21 14.5918 21C13.1781 20.9999 11.9859 19.9468 11.8105 18.5439L10.2051 5.7041C10.1548 5.30213 9.81328 5.00009 9.4082 5C9.03963 5 8.71837 5.25086 8.62891 5.6084L7.34863 10.7275C7.03943 11.9643 5.98743 12.8569 4.74023 12.9824C4.39685 13.5894 3.74712 14 3 14C1.89543 14 1 13.1046 1 12C1 10.8954 1.89543 10 3 10C3.72297 10 4.35374 10.3852 4.70508 10.96C5.0454 10.8655 5.31928 10.5979 5.4082 10.2422L6.68848 5.12305C7.00061 3.8753 8.12197 3 9.4082 3Z" fill="currentColor"/>
</svg>
`,
};

export const icFunction = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.2929 16.7071C15.9024 16.3166 15.9024 15.6834 16.2929 15.2929L19.5858 12L16.2929 8.7071C15.9024 8.31657 15.9024 7.68341 16.2929 7.29289C16.6834 6.90236 17.3166 6.90236 17.7071 7.29289L21.7071 11.2929C22.0976 11.6834 22.0976 12.3166 21.7071 12.7071L17.7071 16.7071C17.3166 17.0976 16.6834 17.0976 16.2929 16.7071Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.70711 16.7071C8.09763 16.3166 8.09763 15.6834 7.70711 15.2929L4.41421 12L7.70711 8.7071C8.09763 8.31657 8.09763 7.68341 7.70711 7.29289C7.31658 6.90236 6.68342 6.90236 6.29289 7.29289L2.29289 11.2929C1.90237 11.6834 1.90237 12.3166 2.29289 12.7071L6.29289 16.7071C6.68342 17.0976 7.31658 17.0976 7.70711 16.7071Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.2745 4.03825C13.7434 3.88652 13.1899 4.19401 13.0382 4.72505L9.03822 18.725C8.88649 19.2561 9.19399 19.8096 9.72502 19.9613C10.2561 20.113 10.8095 19.8055 10.9613 19.2745L14.9613 5.27449C15.113 4.74346 14.8055 4.18997 14.2745 4.03825Z" fill="currentColor"/>
</svg>
`,
};

export const icEvent = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7 6C7 6.55228 6.55228 7 6 7C5.44772 7 5 6.55228 5 6C5 5.44772 5.44772 5 6 5C6.55228 5 7 5.44772 7 6Z" fill="currentColor"/>
<path d="M10 6C10 6.55228 9.55228 7 9 7C8.44772 7 8 6.55228 8 6C8 5.44772 8.44772 5 9 5C9.55228 5 10 5.44772 10 6Z" fill="currentColor"/>
<path d="M13 6C13 6.55228 12.5523 7 12 7C11.4477 7 11 6.55228 11 6C11 5.44772 11.4477 5 12 5C12.5523 5 13 5.44772 13 6Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.7929 17.4215C14.4024 17.0309 14.4024 16.3978 14.7929 16.0072L15.8001 15.0001L14.7929 13.9929C14.4024 13.6024 14.4024 12.9692 14.7929 12.5787C15.1834 12.1881 15.8166 12.1881 16.2071 12.5787L17.9214 14.293C18.3119 14.6835 18.3119 15.3166 17.9214 15.7072L16.2071 17.4215C15.8166 17.812 15.1834 17.812 14.7929 17.4215Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.20711 17.4215C9.59763 17.0309 9.59763 16.3978 9.20711 16.0072L8.19993 15.0001L9.20711 13.9929C9.59763 13.6024 9.59763 12.9692 9.20711 12.5787C8.81658 12.1881 8.18342 12.1881 7.79289 12.5787L6.07861 14.293C5.68808 14.6835 5.68808 15.3166 6.07861 15.7072L7.79289 17.4215C8.18342 17.812 8.81658 17.812 9.20711 17.4215Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.1299 11.0382C12.5989 10.8865 12.0454 11.194 11.8937 11.725L10.1794 17.725C10.0277 18.2561 10.3352 18.8096 10.8662 18.9613C11.3972 19.113 11.9507 18.8055 12.1024 18.2745L13.8167 12.2745C13.9685 11.7435 13.661 11.19 13.1299 11.0382Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M2 5C2 3.34315 3.34315 2 5 2H19C20.6569 2 22 3.34315 22 5V19C22 20.6569 20.6569 22 19 22H5C3.34315 22 2 20.6569 2 19V5ZM5 4H19C19.5523 4 20 4.44771 20 5V8H4V5C4 4.44772 4.44771 4 5 4ZM4 10V19C4 19.5523 4.44772 20 5 20H19C19.5523 20 20 19.5523 20 19V10H4Z" fill="currentColor"/>
</svg>
`,
};

export const icUser = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 10C13.6569 10 15 8.65685 15 7C15 5.34315 13.6569 4 12 4C10.3431 4 9 5.34315 9 7C9 8.65685 10.3431 10 12 10ZM12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9 16C6.79086 16 5 17.7909 5 20V21C5 21.5523 4.55228 22 4 22C3.44772 22 3 21.5523 3 21V20C3 16.6863 5.68629 14 9 14H15C18.3137 14 21 16.6863 21 20V21C21 21.5523 20.5523 22 20 22C19.4477 22 19 21.5523 19 21V20C19 17.7909 17.2091 16 15 16H9Z" fill="currentColor"/>
</svg>
`,
};

export const icPlan = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M22.1597 7.1842L19.2326 3.21844C18.667 2.45219 17.7713 2 16.8189 2H7.18101C6.22864 2 5.33285 2.45219 4.76729 3.21844L1.84018 7.1842C1.04645 8.25957 1.05987 9.73033 1.87309 10.791L9.61913 20.8946C10.8199 22.4609 13.18 22.4609 14.3808 20.8946L22.1268 10.791C22.94 9.73033 22.9534 8.25957 22.1597 7.1842ZM3.72383 8L6.37644 4.40615C6.56496 4.15073 6.86355 4 7.18101 4H16.8189C17.1363 4 17.4349 4.15073 17.6235 4.40615L20.2761 8H3.72383ZM3.78676 10L11.2063 19.6777C11.6066 20.1998 12.3933 20.1998 12.7936 19.6777L20.2131 10H3.78676Z" fill="currentColor"/>
</svg>
`,
};

export const icPayment = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M19 11H9C8.44772 11 8 11.4477 8 12V16C8 16.5523 8.44772 17 9 17H19C19.5523 17 20 16.5523 20 16V12C20 11.4477 19.5523 11 19 11ZM9 9C7.34315 9 6 10.3431 6 12V16C6 17.6569 7.34315 19 9 19H19C20.6569 19 22 17.6569 22 16V12C22 10.3431 20.6569 9 19 9H9Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5 7H15C15.5523 7 16 7.44772 16 8V9H18V8C18 6.34315 16.6569 5 15 5H5C3.34315 5 2 6.34315 2 8V12C2 13.6569 3.34315 15 5 15H6V13H5C4.44772 13 4 12.5523 4 12V8C4 7.44772 4.44772 7 5 7Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14 16C15.1046 16 16 15.1046 16 14C16 12.8954 15.1046 12 14 12C12.8954 12 12 12.8954 12 14C12 15.1046 12.8954 16 14 16Z" fill="currentColor"/>
<path d="M11 14C11 14.5523 10.5523 15 10 15C9.44772 15 9 14.5523 9 14C9 13.4477 9.44772 13 10 13C10.5523 13 11 13.4477 11 14Z" fill="currentColor"/>
<path d="M19 14C19 14.5523 18.5523 15 18 15C17.4477 15 17 14.5523 17 14C17 13.4477 17.4477 13 18 13C18.5523 13 19 13.4477 19 14Z" fill="currentColor"/>
</svg>
`,
};

export const icShow = {
  viewBox: '0 0 24 24',
  svgInner: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 17c4.112 0 6.587-3.067 7.683-4.895a.2.2 0 0 0 .032-.105.2.2 0 0 0-.032-.105C18.587 10.067 16.113 7 12 7s-6.587 3.067-7.683 4.895a.2.2 0 0 0-.*********** 0 0 0 .032.105C5.413 13.933 7.888 17 12 17m9.398-3.867C20.21 15.115 17.215 19 12 19s-8.21-3.885-9.398-5.867a2.19 2.19 0 0 1 0-2.266C3.79 8.885 6.785 5 12 5s8.21 3.885 9.398 5.867a2.19 2.19 0 0 1 0 2.266" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M12 13a1 1 0 1 0 0-2 1 1 0 0 0 0 2m0 2a3 3 0 1 0 0-6 3 3 0 0 0 0 6" fill="currentColor"/></svg>`,
};
export const icHide = {
  viewBox: '0 0 24 24',
  svgInner: '<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.708 9.292a3 3 0 1 0 4 4zM13 12a1 1 0 1 1-2 0 1 1 0 0 1 2 0" fill="currentColor"/><path d="M7.501 6.085c-2.516 1.299-4.108 3.462-4.9 4.782a2.19 2.19 0 0 0 0 2.266C3.792 15.115 6.786 19 12 19c2.613 0 4.668-.975 6.218-2.198l-1.426-1.426C15.548 16.303 13.96 17 12 17c-4.112 0-6.587-3.067-7.683-4.895A.2.2 0 0 1 4.285 12a.2.2 0 0 1 .032-.105c.799-1.332 2.329-3.321 4.69-4.303zm10.767 7.939a12.4 12.4 0 0 0 1.415-******** 0 0 0 .032-.104.2.2 0 0 0-.032-.105C18.587 10.067 16.113 7 12 7q-.37 0-.723.032L9.548 5.304A10 10 0 0 1 12 5c5.215 0 8.21 3.885 9.398 5.867a2.19 2.19 0 0 1 0 2.266 14.4 14.4 0 0 1-1.716 2.305z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M2.293 2.293a1 1 0 0 1 1.414 0l18 18a1 1 0 0 1-1.414 1.414l-18-18a1 1 0 0 1 0-1.414" fill="currentColor"/></svg>'
};
export const icCheck = {
  viewBox: '0 0 24 24',
  svgInner: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M20.707 6.293a1 1 0 0 1 0 1.414l-8.586 8.586a3 3 0 0 1-4.242 0l-3.586-3.586a1 1 0 1 1 1.414-1.414l3.586 3.586a1 1 0 0 0 1.414 0l8.586-8.586a1 1 0 0 1 1.414 0" fill="currentColor"/></svg>`,
};

export const icVerticalLine = {
  viewBox: '0 0 24 44',
  svgInner: `<svg width="24" height="44" viewBox="0 0 24 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12 0V44" stroke="currentColor"/>
</svg>
`,
};

export const icTool = {
  viewBox: '0 0 24 24',
  svgInner: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M12.596 12.859a2 2 0 0 1 1.93-.518c1.34.358 2.82.01 3.866-1.036a4 4 0 0 0 1.164-2.578l-.457.457a3 3 0 0 1-4.243-4.243l.457-.457a4 4 0 0 0-2.578 1.164A4 4 0 0 0 11.7 9.514a2 2 0 0 1-.518 1.93l-5.517 5.518a1 1 0 0 0 1.414 1.414zm1.414 1.414a6.002 6.002 0 0 0 7.031-8.25c-.253-.564-.985-.625-1.422-.188L17.685 7.77a1 1 0 1 1-1.414-1.414l1.934-1.934c.437-.437.376-1.17-.189-1.422a6.002 6.002 0 0 0-8.249 7.031L4.25 15.548a3 3 0 0 0 4.242 4.242z" fill="currentColor"/></svg>`,
};

export const icPlus = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13 5a1 1 0 1 0-2 0v6H5a1 1 0 1 0 0 2h6v6a1 1 0 1 0 2 0v-6h6a1 1 0 1 0 0-2h-6z" fill="currentColor"/></svg>`,
};

export const icRightCorner = {
  viewBox: '0 0 11 16',
  svgInner: `<svg viewBox="0 0 11 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 16h11V0c0 8.837-4.925 16-11 16" fill="currentColor"/></svg>`,
};

export const icClock = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 6C12.5523 6 13 6.44772 13 7V11.5858L15.2071 13.7929C15.5976 14.1834 15.5976 14.8166 15.2071 15.2071C14.8166 15.5976 14.1834 15.5976 13.7929 15.2071L11.5858 13C11.2107 12.6249 11 12.1162 11 11.5858V7C11 6.44772 11.4477 6 12 6Z" fill="currentColor"/>
</svg>
`,
};

export const icOwner = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.97494 4.47635C10.9668 3.09904 13.0319 3.09902 14.0238 4.47635L14.1195 4.61795L17.3626 9.8074L18.4173 8.75369L18.5853 8.59939C20.3483 7.10438 23.107 8.66182 22.637 11.0115L21.0413 18.99C20.8077 20.1582 19.7815 20.9994 18.5902 20.9998L5.39193 21.0047C4.19839 21.005 3.17096 20.1617 2.93881 18.991L1.35677 11.0066C0.891704 8.65915 3.64678 7.10543 5.40853 8.59744L5.57552 8.75174L6.63412 9.80838L9.87924 4.61795L9.97494 4.47635ZM12.4232 5.6785C12.2273 5.36516 11.7714 5.36516 11.5755 5.6785L7.33236 12.4676L7.25912 12.5584C7.09643 12.7193 6.84164 12.7486 6.64681 12.6287L6.55502 12.5554L4.16244 10.1668C3.81285 9.81776 3.22268 10.1333 3.31869 10.6179L4.90072 18.6023C4.94727 18.8362 5.15252 19.0046 5.39095 19.0047L18.5892 18.9998C18.7977 18.9997 18.9812 18.8711 19.055 18.6824L19.0794 18.5984L20.6751 10.6189C20.7721 10.1339 20.1812 9.81797 19.8314 10.1678L17.4437 12.5554L17.3519 12.6277C17.1571 12.7479 16.9024 12.7192 16.7396 12.5584L16.6663 12.4666L12.4232 5.6785Z" fill="currentColor"/>
</svg>
`,
};

export const icEditor = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.5357 3.80739C15.7073 2.63582 17.6067 2.63582 18.7783 3.80739L20.1925 5.2216C21.3641 6.39318 21.3641 8.29267 20.1925 9.46424L9.80003 19.8568C9.66043 19.9964 9.48263 20.0915 9.28904 20.1302L3.98573 21.1909C3.28601 21.3308 2.66909 20.7139 2.80904 20.0142L3.8697 14.7109C3.90842 14.5173 4.00357 14.3395 4.14317 14.1999L14.5357 3.80739ZM17.3641 5.2216L18.7783 6.63582C19.1688 7.02634 19.1688 7.65951 18.7783 8.05003L17.3641 9.46425L14.5357 6.63582L15.9499 5.2216C16.3404 4.83108 16.9736 4.83108 17.3641 5.2216ZM13.1215 8.05003L5.77148 15.4L5.06437 18.9356L8.59991 18.2284L15.9499 10.8785L13.1215 8.05003Z" fill="currentColor"/>
</svg>
`,
};

export const icSupport = {
  viewBox: '0 0 24 24',
  svgInner: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 4C7.58172 4 4 7.58172 4 12H6C7.65685 12 9 13.3431 9 15V18C9 19.6569 7.65685 21 6 21H5C3.34315 21 2 19.6569 2 18V12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12V18C22 19.6569 20.6569 21 19 21H18C16.3431 21 15 19.6569 15 18V15C15 13.3431 16.3431 12 18 12H20C20 7.58172 16.4183 4 12 4ZM20 14H18C17.4477 14 17 14.4477 17 15V18C17 18.5523 17.4477 19 18 19H19C19.5523 19 20 18.5523 20 18V14ZM4 14V18C4 18.5523 4.44772 19 5 19H6C6.55228 19 7 18.5523 7 18V15C7 14.4477 6.55228 14 6 14H4Z" fill="currentColor"/>
</svg>
`,
};

export const icUpload = {
  viewBox: '0 0 24 24',
  svgInner:
  '<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.207 9.29a1 1 0 0 1-1.414 1.415l-2.791-2.791V15a1 1 0 1 1-2 0V7.914l-2.793 2.793a1 1 0 0 1-1.414-1.415l4.5-4.5a1 1 0 0 1 1.414 0z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M4 14a1 1 0 0 1 1 1v2a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2a1 1 0 1 1 2 0v2a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-2a1 1 0 0 1 1-1" fill="currentColor"/></svg>'
};

export const icList = {
  viewBox: '0 0 24 24',
  svgInner: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 7a1 1 0 0 1 1-1h12a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1m0 5a1 1 0 0 1 1-1h12a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1m0 5a1 1 0 0 1 1-1h12a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1" fill="currentColor"/><circle cx="4" cy="7" r="1" fill="currentColor"/><circle cx="4" cy="12" r="1" fill="currentColor"/><circle cx="4" cy="17" r="1" fill="currentColor"/></svg>`
};
export const icCategory = {
  viewBox: '0 0 24 24',
  svgInner:
  `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5H6a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1M6 3a3 3 0 0 0-3 3v2a3 3 0 0 0 3 3h2a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3zm2 12H6a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1m-2-2a3 3 0 0 0-3 3v2a3 3 0 0 0 3 3h2a3 3 0 0 0 3-3v-2a3 3 0 0 0-3-3zm12-8h-2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1m-2-2a3 3 0 0 0-3 3v2a3 3 0 0 0 3 3h2a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3zm2 12h-2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1m-2-2a3 3 0 0 0-3 3v2a3 3 0 0 0 3 3h2a3 3 0 0 0 3-3v-2a3 3 0 0 0-3-3z" fill="currentColor"/></svg>`
};
export const icMoreHorizontal = {
  viewBox: '0 0 24 24',
  svgInner:
  '<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 12a2 2 0 1 1-4 0 2 2 0 0 1 4 0m6 0a2 2 0 1 1-4 0 2 2 0 0 1 4 0m6 0a2 2 0 1 1-4 0 2 2 0 0 1 4 0" fill="currentColor"/></svg>'
};
export const icFolderMove = {
  viewBox: '0 0 24 24',
  svgInner:
  `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 12.043a1 1 0 1 0 0 2h5.043l-1.293 1.293a1 1 0 0 0 1.414 1.414l2.293-2.293a2 2 0 0 0 0-2.828l-2.293-2.293a1 1 0 1 0-1.414 1.414l1.293 1.293z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M11 7a2 2 0 0 1-1.414-.586L8.464 5.293A1 1 0 0 0 7.757 5H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1zm0-2h8a3 3 0 0 1 3 3v10a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V6a3 3 0 0 1 3-3h2.757a3 3 0 0 1 2.122.879z" fill="currentColor"/></svg>`
};
export const icFolderFilled = {
  viewBox: '0 0 24 24',
  svgInner:
  `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5 21h14a3 3 0 0 0 3-3V8a3 3 0 0 0-3-3h-7.586a1 1 0 0 1-.707-.293L9.88 3.88A3 3 0 0 0 7.757 3H5a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3" fill="#DAB674"/></svg>`
};

export const svgIcons = {
  icSupport,
  icEditor,
  icOwner,
  icClock,
  icVerticalLine,
  icPayment,
  icPlan,
  icUser,
  icEvent,
  icFunction,
  icApi,
  icBasicFlow,
  icAgentFlow,
  icSearch,
  icDashboard,
  icStudio,
  icInbox,
  icKnowledgeBase,
  icIntegration,
  icTag,
  icFAQ,
  icSettings,
  icPreview,
  icAdmin,
  icLeads,
  icInfo,
  icTrash,
  icSend,
  icPaperPlane,
  icAdminUser,
  icUserEdit,
  icHeadset,
  icHeadPhone,
  icSun,
  icMoon,
  icDialpad,
  icUserShield,
  icClose,
  icLogo,
  icLogoDark,
  icAlarm,
  faMessage,
  heroArrowLeftOnRectangle,
  heroArrowRightOnRectangle,
  heroInformationCircle,
  heroXMarkCircle,
  faCirclePlay,
  faCircleStop,
  faLinkSimple,
  icLinkPrefix,
  icUnion,
  icDrag,
  icWebDev,
  icCopy,
  icEdit,
  icShow,
  icHide,
  icCheck,
  icTool,
  icPlus,
  icRightCorner,
  icUpload,
  icList,
  icCategory,
  icMoreHorizontal,
  icFolderMove,
  icFolderFilled
};

export type IconTypes = keyof typeof svgIcons;
