// @ts-nocheck
import SelectField from "@flow-editor-v1/components/form/SelectField";
import NodeHeader from "@flow-editor-v1/components/nodes/NodeHeader";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor-v1/components/styled";
import NodeTooltip from "@flow-editor-v1/components/tooltip/NodeTooltip";
import { STUDIO_STATUS } from "@flow-editor-v1/constant";
import { useFlowInstance } from "@flow-editor-v1/hook";
import { FlowDebugState, FunctionNodeData, LayoutState, VariableDefState, VariableLocatedState } from "@flow-editor-v1/model";
import {
  useBuildFlowState,
  useFlowDebugState,
  useFlowInstanceState,
  useFunctionState,
  useLayoutState,
  useStudioState, useVariableDefState,
  useVariableLocatedState
} from "@flow-editor-v1/store";
import { isValidConnection } from "@flow-editor-v1/utils/flow";
import { hexToRgb } from "@flow-editor-v1/utils/util";
import { yupResolver } from "@hookform/resolvers/yup";
import { Divider, Modal, Typography } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Handle, Position } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";
import { VARIABLE_POSITION_TYPE } from "../../../constant/variable";

const StyledHandleSourceAnchor = styled.div<{ $bgColor, $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => props.$data ? 'white' : `rgba(255, 255, 255, 0.5)`};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => props.$data ? 'normal' : 'italic'};
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const functionNodeDataFormSchema = yup.object().shape({
  function_id: yup.number().required('Function required'),
  var_store_id: yup.number().nullable(),
});

const FunctionNode = ({data}: { data: FunctionNodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const {flowInstance} = useFlowInstanceState(state => state);
  const {variablesDev} = useVariableDefState<VariableDefState>(state => state);
  const {functions} = useFunctionState(state => state)
  const {flow, setDirty} = useBuildFlowState(state => state);
  const {status} = useStudioState(state => state)
  const {debug} = useFlowDebugState<FlowDebugState>(state => state);
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const {setVariableLocation} = useVariableLocatedState<VariableLocatedState>(state => state);
  const {theme} = useLayoutState<LayoutState>(state => state);

  const {
    control,
    setValue,
    handleSubmit,
    reset
  } = useForm({
    resolver: yupResolver(functionNodeDataFormSchema)
  })

  const resetForm = () => {
    reset()
  }

  const onSubmit = (formValue) => {
    data.function_id = formValue?.function_id;
    data.var_store_id = formValue?.var_store_id;

    if (data.var_store_id) {
      setVariableLocation({
        position_type: VARIABLE_POSITION_TYPE.FLOW,
        flow_dev_id: flow.id,
        node_id: data.id,
        var_def_dev_id: data.var_store_id
      })
    }

    resetForm();
    setModalOpen(false);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  }

  useEffect(() => {
    resetForm();
    data.function_id && setValue('function_id', data.function_id);
    data.var_store_id && setValue('var_store_id', data.var_store_id)
  }, [JSON.stringify(data), JSON.stringify(functions)]);

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    if (!data.debug) setModalOpen(true);
  }, [data.debug]);

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $debug={data.debug} $theme={theme}>
      {
        debug && (
          <div className="absolute left-0 text-[10px]" style={{top: -20, color: '#D4D9E2'}}>{data.id}</div>
        )
      }
      {
        status && status === STUDIO_STATUS.DEV && (
          <NodeTooltip data={data} selected={data.selected}/>
        )
      }
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
        <StyledHandleSourceAnchor $bgColor={data.node_color} $data={!!(data.function_id)} onDoubleClick={handleOpenModal}>
          {
            data.function_id ? functions.find(func => func.id === data.function_id) ? functions.find(func => func.id === data.function_id).name : 'Configure' : 'Configure'
          }
        </StyledHandleSourceAnchor>
        <Handle
          type="source"
          position={Position.Right}
          id={`${data.id}_source#1`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 10,
            width: 10,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col space-y-4 mt-6">
          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
              Select function <span style={{color: 'red'}}>*</span>
            </Typography.Text>
            <SelectField name={'function_id'} control={control} disabled={status && status === STUDIO_STATUS.LIVE}
                         options={functions.map(v => ({
                           ...v,
                           key: v.id,
                           value: v.id,
                           label: v.name
                         }))}/>
          </div>

          <Divider style={{borderColor: "white"}}/>

          <div className="w-full flex justify-end">
            <div className="flex justify-end items-center space-x-2">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Store response in:</Typography.Text>
              <SelectField style={{width: '250px'}} name={'var_store_id'}
                           disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                           options={variablesDev.map(v => ({
                             ...v,
                             key: v.id,
                             value: v.id,
                             label: v.var_name
                           }))}/>
            </div>
          </div>

          <div className="w-full flex justify-end items-center space-x-4" style={{marginTop: 24}}>
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() =>  setModalOpen(false)}
            >
              Cancel
            </StyledButtonModal>
            <StyledButtonModal
              type="submit"
              key="save"
              $bgColor={"#7F75CF"}
              $color={"white"}
              disabled={status && status === STUDIO_STATUS.LIVE}
            >
              Save
            </StyledButtonModal>
          </div>
        </form>
      </Modal>
    </StyledNodeFlow>
  );
};

export default FunctionNode;
