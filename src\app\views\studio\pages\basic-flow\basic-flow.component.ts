import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { Component, effect, inject, OnInit, signal } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroEllipsisHorizontalMini,
  heroPencilSquareMini,
  heroPlayMini,
  heroTrashMini,
} from '@ng-icons/heroicons/mini';

import { Router } from '@angular/router';
import { APP_ROUTES, STUDIO_PATH, STUDIO_STATUS } from '@core/constants';
import { StudioStore, UIStore } from '@core/stores';
import {
  DxButton,
  DxDialog,
  DxFormField,
  DxInput,
  DxOption,
  DxPrefix,
  DxSelect,
  DxSnackBar,
} from '@dx-ui/ui';
import { heroBolt, heroPlus } from '@ng-icons/heroicons/outline';
import { FLOW_TRIGGER_TYPE } from '@shared/app.constant';
import {
  ConfirmDialogComponent,
  DataTableComponent,
  FlowEnvSelectComponent,
  IColumn,
  SvgIconComponent,
} from '@shared/components';
import { ClickOutsideDirective } from '@shared/directives';
import { IFlow, IFlowDev, IFlowDevFilter, IFlowFilter } from '@shared/models';
import { FlowDevService, FlowService } from '@shared/services';
import { PublishFlowDialogComponent } from '@views/studio/components/publish-flow-dialog/publish-flow-dialog.component';
import { AddFlowComponent } from '@views/studio/pages/basic-flow/add-flow/add-flow.component';
import { MonacoEditorModule } from 'ngx-monaco-editor-v2';
import { debounceTime, Subject } from 'rxjs';

@Component({
  selector: 'app-basic-flow',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatProgressSpinnerModule,
    OverlayModule,
    NgIconsModule,
    DataTableComponent,
    MonacoEditorModule,
    DxFormField,
    DxInput,
    DxPrefix,
    DxSelect,
    DxOption,
    DxButton,
    SvgIconComponent,
    ClickOutsideDirective,
    FlowEnvSelectComponent,
  ],
  providers: [
    provideIcons({
      heroPlus,
      heroPencilSquareMini,
      heroPlayMini,
      heroTrashMini,
      heroEllipsisHorizontalMini,
      heroBolt,
    }),
  ],
  templateUrl: './basic-flow.component.html',
  styleUrls: ['./basic-flow.component.css'],
})
export class BasicFlowComponent implements OnInit {
  flows = signal<Array<IFlowDev | IFlow>>([]);
  totalFlows = signal<number>(0);
  isSubmitting = signal<boolean>(false);
  columns = signal<IColumn[]>([]);
  allowPublish = signal(false);
  allowEdit = signal(false);
  searchModel: IFlowDevFilter | IFlowFilter = {
    key_word: '',
    trigger_type: FLOW_TRIGGER_TYPE.INTENT,
  };
  readonly listTriggerType = [
    {
      value: FLOW_TRIGGER_TYPE.INTENT,
      label: 'Intent',
    },
    {
      value: FLOW_TRIGGER_TYPE.EVENT,
      label: 'Event',
    },
  ];
  searchSubject = new Subject<string>();

  readonly STUDIO_STATUS = STUDIO_STATUS;

  studioStore = inject(StudioStore);
  private dialog = inject(DxDialog);
  private uiStore = inject(UIStore);
  private flowDevService = inject(FlowDevService);
  private flowService = inject(FlowService);
  private snackbar = inject(DxSnackBar);
  private router = inject(Router);

  constructor() {
    effect(() => {
      const status = this.studioStore.status();
      const baseColumns: IColumn[] = [
        {
          columnDef: 'name',
          headerName: 'Flow Name',
          flex: 2,
        },
        {
          columnDef: 'description',
          headerName: 'Description',
          flex: 3,
        },
        {
          columnDef: 'version',
          headerName: 'Version',
          flex: 1,
        },
      ];

      if (status === this.STUDIO_STATUS.DEV) {
        this.columns.set([
          ...baseColumns,
          {
            columnDef: 'action',
            headerName: 'Actions',
            alignHeader: 'center',
            flex: 1,
            actions: [
              {
                case: 'edit',
                name: 'Edit',
                title: 'Edit',
              },
              {
                case: 'publish',
                name: 'Publish',
                title: 'Publish',
              },
              {
                case: 'delete',
                name: 'Delete',
                title: 'Delete',
              },
            ],
          },
        ]);
        this.allowPublish.set(true);
        this.allowEdit.set(true);
      }

      if (status === this.STUDIO_STATUS.LIVE) {
        this.columns.set([
          ...baseColumns,
          {
            columnDef: 'action',
            headerName: 'Actions',
            flex: 1,
            actions: [
              {
                case: 'delete',
                name: 'Delete',
                title: 'Delete',
              },
            ],
          },
        ]);
        this.allowPublish.set(false);
        this.allowEdit.set(false);
      }

      this.getFlows();
    });
  }

  ngOnInit() {
    this.searchSubject.pipe(debounceTime(300)).subscribe(() => {
      this.doSearch();
    });
  }

  getFlows() {
    if (this.studioStore.status() === STUDIO_STATUS.DEV) {
      this.flowDevService.getListFlowDev(this.searchModel).subscribe({
        next: (response: IFlowDev[]) => {
          this.flows.set(response);
          this.totalFlows.set(response.length);
        },
      });
    }
    if (this.studioStore.status() === STUDIO_STATUS.LIVE) {
      this.flowService.getListFlow(this.searchModel).subscribe({
        next: (response) => {
          this.flows.set(response);
          this.totalFlows.set(response.length);
        },
      });
    }
  }

  doSearch(): void {
    this.searchModel = {
      ...this.searchModel,
      key_word: this.searchModel.key_word,
    };
    this.getFlows();
  }

  onAction(event: any): void {
    const { type, data } = event;
    switch (type) {
      case 'edit':
        this.onEdit(data);
        break;
      case 'publish':
        this.onPublish(data);
        break;
      case 'delete':
        this.onDelete(data);
        break;
      default:
        break;
    }
  }

  openCreateDialog(): void {
    this.dialog
      .open(AddFlowComponent, {
        width: '50vw',
        data: { isEdit: false },
      })
      .afterClosed()
      .subscribe(() => {
        this.getFlows();
      });
  }

  onEdit(flow: IFlowDev) {
    this.dialog
      .open(AddFlowComponent, {
        width: '50vw',
        data: {
          id: flow.id,
          category_id: flow.category_id,
          name: flow.name,
          description: flow.description,
          intent_dev_id: flow.intent_dev_id,
          event_id: flow.event_id,
          is_start: flow.is_start,
          trigger_type: flow.trigger_type,
          isEdit: true,
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getFlows();
      });
  }

  onPublish(flow: IFlowDev) {
    if (this.studioStore.status() === STUDIO_STATUS.DEV) {
      this.dialog
        .open(PublishFlowDialogComponent, {
          width: '40dvw',
          data: {
            flow_id: flow.id,
            trigger_type: flow.trigger_type,
            isMultiple: false,
          },
        })
        .afterClosed()
        .subscribe(() => {
          this.studioStore.setStudioStatus(STUDIO_STATUS.LIVE);
          this.getFlows();
        });
    }
  }

  onDelete(flow: IFlowDev) {
    if (flow.id) {
      const id = flow.id;
      this.dialog
        .open(ConfirmDialogComponent, {
          data: {
            title: 'Delete Flow',
            content: `Are you sure you want to delete flow "${flow.name}"?`,
            confirmText: 'Delete',
            cancelText: 'Cancel',
            isDelete: true,
          },
        })
        .afterClosed()
        .subscribe((result: any) => {
          if (!!result && this.studioStore.status() === STUDIO_STATUS.DEV) {
            this.flowDevService.deleteFlowDev(id).subscribe({
              next: () => {
                this.snackbar.open('Flow deleted successfully', '', {
                  duration: 2000,
                  horizontalPosition: 'right',
                  verticalPosition: 'top',
                  panelClass: 'dx-snack-bar-success',
                });
                this.getFlows();
              },
            });
          }
          if (result && this.studioStore.status() === STUDIO_STATUS.LIVE) {
            this.flowService.deleteFlow(id).subscribe({
              next: () => {
                this.snackbar.open('Flow deleted successfully', '', {
                  duration: 2000,
                  horizontalPosition: 'right',
                  verticalPosition: 'top',
                  panelClass: 'dx-snack-bar-success',
                });
                this.getFlows();
              },
            });
          }
        });
    }
  }

  onEditFlow(flow: IFlowDev | IFlow) {
    void this.router.navigate([
      `${APP_ROUTES.STUDIO}/${STUDIO_PATH.BASIC_FLOW}/${flow.id}`,
    ]);
  }

  getVersionLabel(flowId: number): string {
    const flow = this.flows().find((flow) => flow.id === flowId);
    if (flow) {
      let versionLabel = '';
      if ('flow_versions' in flow) {
        versionLabel = flow.flow_versions
          .filter((flv) => flv.status === 'ACTIVE')
          .map((flv) => flv.version_name)
          .join(', ');
      } else if ('flow_version' in flow) {
        versionLabel = flow.flow_version.version_name;
      }
      return versionLabel;
    }
    return '';
  }
}
