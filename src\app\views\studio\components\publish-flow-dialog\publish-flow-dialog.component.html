<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      Publish changes to
      <span class="ml-2 text-info dark:text-dark-info text-3xl">Live</span>
    </div>
    <div class="flex items-center justify-end space-x-4">
      <app-svg-icon
        type="icClose"
        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="close()"
      ></app-svg-icon>
    </div>
  </div>

  <div class="flex-1 overflow-y-auto mt-18 mb-20">
    <div
      [formGroup]="formGroup"
      class="px-6 pt-6 pb-[3px] flex flex-col space-x-4"
    >
      <div
        class="w-full bg-light-third dark:bg-dark-third p-3 rounded-lg flex space-x-5 !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border mb-6"
      >
        <app-custom-icon
          iconName="heroInformationCircleSolid"
          class="text-4xl"
        ></app-custom-icon>
        <div class="flex-grow w-full flex flex-col space-y-2">
          <div>
            1. Publishing this flow live will immediately apply all changes if
            your status is active.
          </div>
          <div>
            2. Reverting to a previous version is possible if issues arise.
          </div>
        </div>
      </div>
      <dx-form-field class="w-full">
        <dx-label>Flow</dx-label>
        <dx-select formControlName="flow_dev_ids">
          @for (flowDev of listFlowDevSelect; track $index) {
          <dx-option [value]="flowDev.value">{{ flowDev.label }}</dx-option>
          }
        </dx-select>
        @if (formGroup.get('flow_dev_ids')?.errors &&
        (formGroup.get('flow_dev_ids')?.dirty ||
        formGroup.get('flow_dev_ids')?.touched)) {
        <dx-error>Flow is required </dx-error>
        }
      </dx-form-field>

      <dx-form-field class="w-full">
        <dx-label>Version name</dx-label>
        <input dxInput type="text" formControlName="version_name" />
        @if (formGroup.get('version_name')?.errors &&
        (formGroup.get('version_name')?.dirty ||
        formGroup.get('version_name')?.touched)) {
        <dx-error>Version name is required </dx-error>
        }
      </dx-form-field>

      <dx-form-field class="w-full">
        <dx-label>Status</dx-label>
        <dx-select formControlName="status">
          @for (status of STATUS_LIST; track $index) {
          <dx-option [value]="status.value">{{ status.label }}</dx-option>
          }
        </dx-select>
        @if (formGroup.get('status')?.errors && (formGroup.get('status')?.dirty
        || formGroup.get('status')?.touched)) {
        <dx-error>Status is required </dx-error>
        }
      </dx-form-field>
    </div>
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="close()">Cancel</button>
    <button
      dxLoadingButton="filled"
      [loading]="isPublishing"
      [disabled]="formGroup.invalid"
      (click)="publishFlow()"
    >
      Publish
    </button>
  </div>
</div>
