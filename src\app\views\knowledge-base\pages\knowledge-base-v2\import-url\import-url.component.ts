import {Component, inject, signal, CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {CommonModule} from '@angular/common';
import {
  DIALOG_DATA,
  DxButton,
  DxDialogRef, DxError,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxSnackBar,
  DxSelect,
  DxOption
} from '@dx-ui/ui';
import { KnowledgeBaseService } from '@shared/services';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {ICollection} from '@views/knowledge-base/pages/knowledge-base-v2/knowledge-base-v2.component';
import {SvgIconComponent} from '@shared/components';
import {MatRadioModule} from '@angular/material/radio';
import {CustomValidators} from '@shared/validators';

@Component({
  selector: 'app-import-url',
  imports: [
    CommonModule,
    DxLoadingButton,
    DxButton,
    DxInput,
    FormsModule,
    DxLabel,
    DxFormField,
    DxSelect,
    DxOption,
    SvgIconComponent,
    DxError,
    ReactiveFormsModule,
    MatRadioModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './import-url.component.html',
  styleUrl: './import-url.component.css',
  host : {
    class: 'h-full'
  }
})
export class ImportUrlComponent {
  isImportingUrl = signal(false);


  collectionList = signal<ICollection[]>([]);

  dialogRef = inject(DxDialogRef<ImportUrlComponent>);
  data:{
    rootURL: string,
    collectionOfFile: number,
    modeGetUrl: string,
    parentId: number,
    collectionList: ICollection[]
  } = inject(DIALOG_DATA);

  formGroup: FormGroup = inject(FormBuilder).group({
    url: [this.data.rootURL || '', [Validators.required, CustomValidators.urlValidator]],
    collectionOfFile: [this.data.collectionOfFile],
    modeGetUrl: [this.data.modeGetUrl || 'getOne', [Validators.required]],
  });
  snackBar = inject(DxSnackBar);
  private KnowledgeBaseService = inject(KnowledgeBaseService);

  ngOnInit() {
    if (this.data) {
      this.formGroup.patchValue({
        url: this.data.rootURL,
        files: this.data.collectionOfFile,
      });
    }
    this.collectionList.set(this.data.collectionList);
  }
  importUrl() {
    this.isImportingUrl.set(true);
    this.KnowledgeBaseService.importUrl(
      this.formGroup.value.url,
      this.formGroup.value.collectionOfFile,
      this.formGroup.value.modeGetUrl,
      this.data.parentId
    ).subscribe({
      next: (res) => {
        this.showSnackBar(res.message, 'info');
        // this.viewFolder({ ...this.searchModel, id: this.getParentId() });
        this.isImportingUrl.set(false);
        this.dialogRef.close(true);
      },
      error: (error) => {
        this.showSnackBar('Failed to import data from url', 'error');
        this.isImportingUrl.set(false);
      },
    });
  }

  showSnackBar(message: string, type: 'success' | 'error' | 'info') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }
}
