import { NgStyle } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import {DIALOG_DATA, DxButton, DxCheckbox, DxDialogRef, DxLabel, DxTooltip} from '@dx-ui/ui';
import { NgIcon, provideIcons } from '@ng-icons/core';
import { heroXMark } from '@ng-icons/heroicons/outline';
import { IFile } from '@shared/models';
import {FormsModule} from '@angular/forms';

@Component({
  selector: 'app-add-more-source',
  imports: [NgStyle, NgIcon, DxButton, DxTooltip, DxCheckbox, DxLabel, FormsModule],
  providers: [provideIcons({ heroXMark })],
  templateUrl: './add-more-source.component.html',
  styleUrl: './add-more-source.component.css',
  host:{ class: 'h-full' }
})
export class AddMoreSourceComponent implements OnInit {
  colors = [
    '#baf3db',
    '#f8e6a0',
    '#fedec8',
    '#ffd5d2',
    '#dfd8fd',
    '#4bce97',
    '#f5cd47',
    '#fea362',
    '#f87168',
    '#9f8fef',
    '#1f845a',
    '#946f00',
    '#c25100',
    '#c9372c',
    '#6e5dc6',
    '#cce0ff',
    '#c6edfb',
    '#d3f1a7',
    '#fdd0ec',
    '#dcdfe4',
    '#579dff',
    '#6cc3e0',
    '#94c748',
    '#e774bb',
    '#8590a2',
    '#0c66e4',
    '#227d9b',
    '#5b7f24',
    '#ae4787',
    '#626f86',
  ];

  knowledgeBaseSelected: number[] = [];
  listKnowledgeBase: IFile[] = [];
  knowledgeBaseSelectedDraft: number[] = [];
  dialogRef = inject(DxDialogRef<AddMoreSourceComponent>);
  data: {
    knowledgeBaseSelected: number[];
    listKnowledgeBase: IFile[];
    knowledgeBaseSelectedDraft: number[];
  } = inject(DIALOG_DATA);

  ngOnInit() {
    this.knowledgeBaseSelected = this.data.knowledgeBaseSelected;
    this.listKnowledgeBase = this.data.listKnowledgeBase;
    this.knowledgeBaseSelectedDraft = this.data.knowledgeBaseSelectedDraft;
  }
  onKnowledgeBaseSelectionChange(event: any, sourceId: number) {
    // Handle both MatCheckboxChange and regular Event
    const isChecked =
      event.checked !== undefined
        ? event.checked
        : (event.target as HTMLInputElement).checked;

    if (isChecked) {
      // Thêm id vào mảng nếu được chọn
      this.knowledgeBaseSelectedDraft.push(sourceId);
    } else {
      // Loại bỏ id khỏi mảng nếu bị bỏ chọn
      this.knowledgeBaseSelectedDraft = this.knowledgeBaseSelectedDraft.filter(
        (id) => id !== sourceId
      );
    }
  }
  getColor(config: string | null): string {
    if (!config) return '#7241FF'; // Default primary color

    try {
      const parsedConfig = JSON.parse(config);
      return parsedConfig.color || '#7241FF';
    } catch (error) {
      console.error('Invalid config:', config);
      return '#7241FF'; // Default color if error
    }
  }

  /**
   * Determine text color based on background color brightness
   * @param bgColor Background color in hex format
   * @returns 'black' or 'white' depending on background brightness
   */
  getTextColor(bgColor: string): string {
    // Convert from hex to RGB
    const r = parseInt(bgColor.slice(1, 3), 16);
    const g = parseInt(bgColor.slice(3, 5), 16);
    const b = parseInt(bgColor.slice(5, 7), 16);

    // Calculate luminance
    const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

    // Decide text color
    return luminance > 128 ? 'black' : 'white';
  }

  /**
   * Get a random color from the colors array
   * @param id ID to use for consistent color selection
   * @returns A color from the colors array
   */
  getRandomColor(id: number): string {
    return this.colors[id % this.colors.length];
  }

  knowledgeBaseTooltip(knowledgeBase: IFile | null): string {
    if (!knowledgeBase) return 'No information available';
    return `name: ${knowledgeBase.name}\nsource: ${
      knowledgeBase.file_path ?? knowledgeBase.url ?? 'N/A'
    }`;
  }
  saveMoreSource() {
    this.dialogRef.close(this.knowledgeBaseSelectedDraft);
  }
}
