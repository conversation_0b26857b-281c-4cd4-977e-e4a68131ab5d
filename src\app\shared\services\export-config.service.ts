import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from '@env/environment';
import { Observable } from 'rxjs';
import { IExportConfig, IExportMessageBody } from '../models/export.model';

@Injectable({
  providedIn: 'root',
})
export class ExportConfigService {
  private apiUrl = `${environment.SERVER_URL}/export`;

  private httpClient = inject(HttpClient);

  getExportConfig(): Observable<IExportConfig[]> {
    return this.httpClient.post<IExportConfig[]>(this.apiUrl + '/get-all', {});
  }

  createExportConfig(body: Partial<IExportConfig>): Observable<any> {
    return this.httpClient.post<any>(this.apiUrl + '/create', body);
  }

  updateExportConfig(body: Partial<IExportConfig>): Observable<any> {
    return this.httpClient.put<any>(this.apiUrl+'/', body);
  }

  exportMessage(body: IExportMessageBody): Observable<any> {
    return this.httpClient.post<any>(this.apiUrl + '/export_data', body);
  }
}
