<div class="h-full flex flex-col overflow-hidden">
  <div
    class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
  >
    Payment Management
  </div>
  <div
    class="mt-6 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
  >
    <div class="flex flex-wrap items-center justify-between">
      <div class="flex items-center space-x-4 flex-wrap">
        <dx-form-field
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <app-svg-icon
            dxPrefix
            type="icSearch"
            class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
          ></app-svg-icon>
          <input
            dxInput
            [type]="'text'"
            [(ngModel)]="searchModel.key_word"
            (ngModelChange)="onSearchChange($event)"
            placeholder="Search by Name/Email"
          />
        </dx-form-field>

        <dx-form-field
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <dx-select
            [(ngModel)]="searchModel.payment_method"
            (selectionChange)="onPaymentMethodChange($event)"
          >
            <dx-option
              *ngFor="let paymentMethod of listPaymentMethod"
              [value]="paymentMethod.value"
            >
              {{ paymentMethod.label }}
            </dx-option>
          </dx-select>
        </dx-form-field>

        <dx-form-field
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <dx-select
            [(ngModel)]="searchModel.plan_id"
            (selectionChange)="changeFilter()"
          >
            <dx-option *ngFor="let plan of listPlanSearch" [value]="plan.value">
              {{ plan.label }}
            </dx-option>
          </dx-select>
        </dx-form-field>

        <dx-form-field
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <dx-select
            [(ngModel)]="searchModel.status"
            (selectionChange)="changeFilter()"
          >
            <dx-option *ngFor="let status of listStatus" [value]="status.value">
              {{ status.label }}
            </dx-option>
          </dx-select>
        </dx-form-field>
      </div>
    </div>

    <div class="flex-1 flex overflow-hidden">
      <app-data-table
        [rows]="listPayment"
        [columns]="columns"
        (pageChange)="doSearch($event)"
        [limit]="searchModel.pageSize"
        [pageIndex]="searchModel.page"
        [count]="count"
        [rowTemplate]="rowTemplate"
        [actionTemplate]="actionTemplate"
        class="w-full"
      >
      </app-data-table>
    </div>
  </div>
</div>

<ng-template #rowTemplate let-row="row" let-column="column">
  @switch (column.columnDef) { @case ('status') {
  <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
    <div
      class="px-4 rounded-full text-center"
      [ngClass]="{
        'bg-warning text-warning-content': row[column.columnDef] === 'pending',
        'bg-success text-success-content': row[column.columnDef] === 'done',
        'bg-error text-error-content':
          row[column.columnDef] !== 'pending' && 'done'
      }"
    >
      {{ row[column.columnDef] | titlecase }}
    </div>
  </div>
  } @case ('plan') {
  <div
    class="flex"
    [ngClass]="{
      'cursor-pointer': currentUser.email === EMAIL_SUPER_ADMIN
    }"
    [ngStyle]="{ 'justify-content': column.align }"
  >
    @if (row[column.columnDef]) {
    <div
      class="px-4 border rounded-full text-center"
      [ngClass]="handleClassesPlan(row[column.columnDef])"
    >
      {{ row[column.columnDef] }}
    </div>
    } @if (!row[column.columnDef]) {
    <div
      class="px-2 border rounded text-center border-light-red text-light-red hover:bg-light-red hover:text-light-white"
    >
      None
    </div>
    }
  </div>
  } @case ('amount') {
  <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
    {{ row[column.columnDef] | currency : "" : "" : "1.0-0" }}
  </div>
  } @case ('currency') {
  <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
    {{ row[column.columnDef] | uppercase }}
  </div>
  } @default {
  <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
    {{ row[column.columnDef] }}
  </div>
  } }
</ng-template>

<ng-template #actionTemplate let-row>
  <div class="flex justify-center items-center">
    <button
      class="flex"
      (click)="row.isActions = !row.isActions"
      cdkOverlayOrigin
      #trigger="cdkOverlayOrigin"
    >
      <ng-icon
        name="heroEllipsisHorizontal"
        size="24"
        class="flex items-center justify-center cursor-pointer"
      ></ng-icon>
    </button>
    <ng-template
      cdkConnectedOverlay
      [cdkConnectedOverlayOrigin]="trigger"
      [cdkConnectedOverlayOpen]="row.isActions"
      [cdkConnectedOverlayPush]="true"
      [cdkConnectedOverlayPositions]="[
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'top',
          offsetY: 10
        },
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'bottom',
          offsetY: 10
        }
      ]"
    >
      <ul
        class="w-[245px] flex flex-col justify-between p-1 border border-primary-border dark:border-dark-primary-border rounded-xl bg-base-400 dark:bg-dark-base-400 z-50 shadow-lg"
        (clickOutside)="row.isActions = false"
      >
        <li
          (click)="
            $event.stopPropagation();
            viewPaymentDetails(row);
            row.isActions = false
          "
        >
          <div
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400 dark:hover:bg-dark-base-400 !text-neutral-content dark:!text-dark-neutral-content"
          >
            <ng-icon
              name="heroEye"
              class="text-2xl flex items-center justify-center"
            ></ng-icon>
            <div class="flex items-center justify-between text-[16px]">
              View details
            </div>
          </div>
        </li>
        @if (row.status === 'pending') {
        <li
          (click)="
            $event.stopPropagation();
            updatePaymentStatus(row, 'done');
            row.isActions = false
          "
        >
          <div
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg !text-success dark:!text-dark-success hover:bg-base-400 dark:hover:bg-dark-base-400"
          >
            <ng-icon
              name="heroCheckCircle"
              class="text-2xl flex items-center justify-center"
            ></ng-icon>
            <div class="flex items-center justify-between text-[16px]">
              Mark as done
            </div>
          </div>
        </li>
        } @if (row.status === 'pending') {
        <li
          (click)="
            $event.stopPropagation();
            updatePaymentStatus(row, 'cancel');
            row.isActions = false
          "
        >
          <div
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg !text-warning dark:!text-dark-warning hover:bg-base-400 dark:hover:bg-dark-base-400"
          >
            <ng-icon
              name="heroXCircle"
              class="text-2xl flex items-center justify-center"
            ></ng-icon>
            <div class="flex items-center justify-between text-[16px]">
              Cancel payment
            </div>
          </div>
        </li>
        } @if (!row.isDeleting && row.email !== EMAIL_SUPER_ADMIN && row.email
        !== currentUser.email) {
        <li
          (click)="
            $event.stopPropagation(); showDeleteUser(row); row.isActions = false
          "
        >
          <div
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg !text-error dark:!text-dark-error hover:bg-base-400 dark:hover:bg-dark-base-400"
          >
            <ng-icon
              name="heroTrash"
              class="text-2xl flex items-center justify-center"
            ></ng-icon>
            <div class="flex items-center justify-between text-[16px]">
              Delete payment
            </div>
          </div>
        </li>
        } @if (row.isDeleting) {
        <li>
          <mat-spinner diameter="24"></mat-spinner>
        </li>
        }
      </ul>
    </ng-template>
  </div>
</ng-template>
