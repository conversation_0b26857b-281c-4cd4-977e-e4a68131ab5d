<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div class="flex items-center space-x-4 ">
      <img
        [ngSrc]="data.integration.icon!"
        alt="Icon description"
        width="32"
        height="32"
        class="w-8 h-8 object-cover shrink-0"
      />
      <div
        class="text-2xl font-bold truncate text-base-content dark:text-dark-base-content max-w-[calc(100% - 24px)]"
      >
        {{ data.integration.title }}
      </div>
    </div>
    <div class="flex items-center justify-end space-x-3 xs:space-x-4">
      <dx-slide-toggle
        [(checked)]="enableForm"
        (checkedChange)="toggleEnable()"
      ></dx-slide-toggle>
      <div class="text-[12px] text-primary-border dark:text-dark-primary-border">|</div>
      <ng-icon
        name="heroXMark"
        class="text-2xl !text-neutral-content shrink-0 dark:!text-dark-neutral-content cursor-pointer"
        (click)="close()"
      ></ng-icon>
    </div>
  </div>

  <div class="h-[600px] flex-1 flex overflow-y-auto mt-18 mb-20">
    <div
      class="flex flex-col gap-8 px-6 pt-6 h-full bg-base-200 dark:bg-dark-base-200 w-full"
    >
      <!--    nhiều loại template phụ thuộc vào data.integration.type-->
      <ng-container [ngSwitch]="data.integration.type">
        <app-embed-form
          *ngSwitchCase="'embed'"
          [initialData]="data.integration.config"
          [aiId]="aiId"
          [isEnabled]="!data.integration.isEnabled"
          (configChange)="onEmbedConfigChange($event)"
        >
        </app-embed-form>

        <app-freshchat-form
          *ngSwitchCase="'freshchat'"
          [initialData]="data.integration.config"
          [webhookUrl]="linkWebHook()"
          (formChange)="onFormChange($event)"
        >
        </app-freshchat-form>

        <app-messenger-form
          *ngSwitchCase="'facebook'"
          [initialData]="data.integration.config"
          [webhookUrl]="linkWebHook()"
          (formChange)="onFormChange($event)"
        >
        </app-messenger-form>

        <app-whatsapp-form
          *ngSwitchCase="'whatsapp'"
          [initialData]="data.integration.config"
          [webhookUrl]="linkWebHook()"
          (formChange)="onFormChange($event)"
        >
        </app-whatsapp-form>

        <app-slack-form
          *ngSwitchCase="'slack'"
          [initialData]="data.integration.config"
          [webhookUrl]="linkWebHook()"
          (formChange)="onFormChange($event)"
        >
        </app-slack-form>

        <app-zalo-form
          *ngSwitchCase="'zalo'"
          [initialData]="data.integration.config"
          [webhookUrl]="linkWebHook()"
          (formChange)="onFormChange($event)"
        >
        </app-zalo-form>

        <div *ngSwitchDefault>No form available for this integration.</div>
      </ng-container>
    </div>
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="close()">Close</button>
    <button
      dxLoadingButton
      [disabled]="formGroupControl && (!formGroupControl.dirty || formGroupControl.invalid)"
      (click)="saveConfig()"
    >
      {{ data.integration.id ? "Update" : "Create" }}
    </button>
  </div>
</div>
