<div class="settings-tab-content w-full p-6 flex flex-col space-y-6">
  <div class="flex items-center space-x-2">
    <div
      class="text-xl font-bold text-base-content dark:text-dark-base-content"
    >
      LLM settings
    </div>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div
    class="text-lg font-bold text-base-content dark:text-dark-base-content mb-4"
  >
    RAG
  </div>
  <div class="flex flex-col lg:w-3/4">
    <div class="flex items-center space-x-4">
      <dx-form-field class="flex-1">
        <dx-label>AI Model</dx-label>
        <dx-select
          placeholder="Choose the AI model you would like to use"
          [formControl]="modelRag"
        >
          @for (item of listModelOptions(); track item.value) {
          <dx-option [value]="item.value">
            {{ item.label }}
          </dx-option>
          }
        </dx-select>
      </dx-form-field>
      <div class="flex-1 flex flex-wrap items-center space-x-3">
        <div class="flex-1 w-full flex items-center space-x-4">
          <dx-label class="flex items-center space-x-1 !mb-0"
            >Temperature</dx-label
          >
          <dx-slider class="flex-1" [max]="1" [min]="0" [step]="0.1">
            <input dxSliderThumb #slider [formControl]="temperatureRag" />
          </dx-slider>
        </div>
        <input
          type="number"
          class="h-8 w-18 pl-3 py-1 text-center text-base-content dark:text-dark-base-content rounded-lg bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border"
          [formControl]="temperatureRag"
        />
      </div>
    </div>
    <div class="flex items-center space-x-4">
      <dx-form-field class="flex-1">
        <dx-label>Limit (0-20)</dx-label>
        <input
          dxInput
          type="number"
          placeholder="Enter limit"
          [formControl]="limit"
        />
      </dx-form-field>
      <div class="flex-1 flex flex-wrap items-center space-x-3">
        <div class="flex-1 w-full flex items-center space-x-4">
          <dx-label class="flex items-center space-x-1 !mb-0">
            <div>Threshold</div>
            <ng-icon
              name="heroInformationCircle"
              class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
              dxTooltip="Predefined value that determines the decision boundary for classifying outcomes based on a model's output."
              dxTooltipPosition="right"
            ></ng-icon>
          </dx-label>
          <dx-slider class="flex-1" [max]="1" [min]="0" [step]="0.1">
            <input dxSliderThumb #slider [formControl]="threshold" />
          </dx-slider>
        </div>
        <input
          type="number"
          class="h-8 w-18 pl-3 py-1 text-center text-base-content dark:text-dark-base-content rounded-lg bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border"
          [formControl]="threshold"
        />
      </div>
    </div>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div
    class="text-lg font-bold text-base-content dark:text-dark-base-content mb-4"
  >
    Check Fit
  </div>
  <div class="flex flex-col lg:w-3/4">
    <div class="flex items-center space-x-4">
      <dx-form-field class="flex-1">
        <dx-label>AI Model</dx-label>
        <dx-select
          placeholder="Choose the AI model you would like to use"
          [formControl]="modelCF"
        >
          @for (item of listModelOptions(); track item.value) {
          <dx-option [value]="item.value">
            {{ item.label }}
          </dx-option>
          }
        </dx-select>
      </dx-form-field>
      <div class="flex-1 flex flex-wrap items-center space-x-3">
        <div class="flex-1 w-full flex items-center space-x-4">
          <dx-label class="flex items-center space-x-1 !mb-0"
            >Temperature</dx-label
          >
          <dx-slider class="flex-1" [max]="1" [min]="0" [step]="0.1">
            <input dxSliderThumb #slider [formControl]="temperatureCF" />
          </dx-slider>
        </div>
        <input
          type="number"
          class="h-8 w-18 pl-3 py-1 text-center text-base-content dark:text-dark-base-content rounded-lg bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border"
          [formControl]="temperatureCF"
        />
      </div>
    </div>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div
    class="text-lg font-bold text-base-content dark:text-dark-base-content mb-4"
  >
    Detect Intent
  </div>
  <div class="flex flex-col lg:w-3/4">
    <div class="flex items-center space-x-4">
      <dx-form-field class="flex-1">
        <dx-label>AI Model</dx-label>
        <dx-select
          placeholder="Choose the AI model you would like to use"
          [formControl]="modelDI"
        >
          @for (item of listModelOptions(); track item.value) {
          <dx-option [value]="item.value">
            {{ item.label }}
          </dx-option>
          }
        </dx-select>
      </dx-form-field>
      <div class="flex-1 flex flex-wrap items-center space-x-3">
        <div class="flex-1 w-full flex items-center space-x-4">
          <dx-label class="flex items-center space-x-1 !mb-0"
            >Temperature</dx-label
          >
          <dx-slider class="flex-1" [max]="1" [min]="0" [step]="0.1">
            <input dxSliderThumb #slider [formControl]="temperatureDI" />
          </dx-slider>
        </div>
        <input
          type="number"
          class="h-8 w-18 pl-3 py-1 text-center text-base-content dark:text-dark-base-content rounded-lg bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border"
          [formControl]="temperatureDI"
        />
      </div>
    </div>
  </div>
</div>
