import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { AuthService } from '@core/services';
import {
  DIALOG_DATA,
  DxButton,
  DxDialogRef,
  DxError,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxSnackBar,
  DxSuffix,
} from '@dx-ui/ui';
import { NgIcon, provideIcons } from '@ng-icons/core';
import { heroEye, heroEyeSlash, heroXMark } from '@ng-icons/heroicons/outline';
import { CustomValidators } from '@shared/validators';

@Component({
  selector: 'app-add-user',
  imports: [
    ReactiveFormsModule,
    NgIcon,
    DxButton,
    DxLoadingButton,
    DxFormField,
    DxInput,
    DxLabel,
    DxError,
    DxSuffix,
    CommonModule,
  ],
  providers: [provideIcons({ heroEye, heroEyeSlash, heroXMark })],
  templateUrl: './add-user.component.html',
  styleUrl: './add-user.component.css',
  host: {
    class: 'h-full',
  },
})
export class AddUserComponent {
  isLoading = signal<boolean>(false);

  showPasswordCreate = false;
  showConfirmPasswordCreate = false;
  regexPhone =
    '^((\\(\\+\\d{2,4}[^\\+84]\\))((\\s?((\\d{2,}\\s?\\d{3,}\\s?\\d{3,})|(\\d{7,})))|(\\.(\\d{2,}\\.\\d{3,}\\.\\d{3,}))|(-(\\d{2,}-\\d{3,}-\\d{3,}))))|((\\(\\+84\\)|\\+84|0)((\\s?(\\d{3}\\s?\\d{3}\\s?\\d{3}))|(\\s?(\\d{2}\\s?\\d{3}\\s?\\d{5}))))$';
  regexMail = '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$';

  fb = inject(FormBuilder);
  formGroup: FormGroup = new FormGroup({});
  dialogRef = inject(DxDialogRef<AddUserComponent>);
  data: { user_id: number } = inject(DIALOG_DATA);
  snackBar = inject(DxSnackBar);
  authService = inject(AuthService);

  constructor() {
    this.formGroup = this.fb.group(
      {
        password: [
          null,
          [
            Validators.required,
            Validators.minLength(8),
            CustomValidators.patternValidator(/\d/, { hasNumber: true }),
            CustomValidators.patternValidator(
              /[A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ]/,
              { hasCapitalCase: true }
            ),
            CustomValidators.patternValidator(
              /[a-zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/,
              { hasSmallCase: true }
            ),
            CustomValidators.patternValidator(
              /[ `!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?~]/,
              {
                hasSpecialCharacters: true,
              }
            ),
          ],
        ],
        confirmPassword: ['', [Validators.required]],
        first_name: [null, [Validators.required, Validators.maxLength(64)]],
        last_name: [null, [Validators.required, Validators.maxLength(64)]],
        email: [
          null,
          [Validators.required, Validators.pattern(this.regexMail)],
        ],
        phone_number: [null, [Validators.pattern(this.regexPhone)]],
      },
      {
        validators: [CustomValidators.mustMatch('password', 'confirmPassword')],
      }
    );
  }

  signUp() {
    this.isLoading.set(true);
    const data = this.formGroup.value;
    delete data.confirmPassword;
    data.username = data.email;
    data.phone_number =
      data.phone_number &&
      data.phone_number.replace(/\s(?=\s)/g, '').trim() !== ''
        ? data.phone_number.replace(/\s(?=\s)/g, '').trim()
        : null;
    this.authService.signUp(data).subscribe({
      next: (res: any) => {
        this.isLoading.set(false);
        this.closeCreateUserDialog();
        this.showSnackBar(
          'Create user successfully!',
          'success'
        );
      },
      error: (err) => {
        this.isLoading.set(false);
        console.log(err);
        
        const errorMessage =
          (err?.error?.detail && typeof err.error.detail === 'string')
            ? err.error.detail
            : (err?.detail || 'Create user failed!');
        this.showSnackBar(errorMessage, 'error');
      }
    });
  }

  closeCreateUserDialog() {
    this.dialogRef.close();
    this.formGroup.reset();
  }

  togglePasswordVisibility() {
    this.showPasswordCreate = !this.showPasswordCreate;
  }

  toggleConfirmPasswordVisibility(): void {
    this.showConfirmPasswordCreate = !this.showConfirmPasswordCreate;
  }

  showSnackBar(message: string, type: 'success' | 'error') {
    if (type === 'success') {
      this.snackBar.open(message, '', {
        panelClass: 'dx-snack-bar-success',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }

    if (type === 'error') {
      this.snackBar.open(message, '', {
        panelClass: 'dx-snack-bar-error',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }
  }
}
