<div
  class="flex flex-col h-full text-light-text dark:text-dark-text p-6 w-full bg-light-background dark:bg-dark-background"
>
  <!-- Header -->
  <div
    class="header pb-3 border-b border-b-light-border-line dark:border-b-dark-border-line flex justify-between items-center bg-light-background dark:bg-dark-background"
  >
    <div
      class="text-2xl font-bold card-title capitalize text-light-text dark:text-dark-text"
    >
      Save FAQ
    </div>
    <div class="cursor-pointer">
      <svg
        (click)="onCloseDialog()"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
          class="fill-light-text dark:fill-dark-text"
        />
      </svg>
    </div>
  </div>

  <!-- Content -->
  <div
    class="content w-full max-h-[60vh] mt-2 text-light-text dark:text-dark-text bg-light-background dark:bg-dark-background overflow-auto"
  >
    @if (faqForm()) {
    <form [formGroup]="faqForm()!" class="flex flex-col space-y-4">
      <div class="form-group">
        <label for="question" class="block text-sm font-medium mb-2">Question:</label>
        <textarea
          id="question"
          formControlName="question"
          [value]="questionText()"
          (input)="onQuestionChange($any($event.target).value)"
          class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-light-primary"
          rows="3"
          placeholder="Enter the question..."
        ></textarea>
      </div>

      <div class="form-group">
        <label for="answer" class="block text-sm font-medium mb-2">Answer:</label>
        <textarea
          id="answer"
          formControlName="answer"
          [value]="answerText()"
          (input)="onAnswerChange($any($event.target).value)"
          class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-light-primary"
          rows="5"
          placeholder="Enter the answer..."
        ></textarea>
      </div>
    </form>
    }
  </div>

  <!-- Actions -->
  <div
    class="footer mt-6 flex justify-end gap-4 bg-light-background dark:bg-dark-background"
  >
    <button
      class="bg-light-secondary-background dark:bg-dark-secondary-background cursor-pointer h-[40px] min-w-[100px] text-light-text dark:text-dark-text px-3 rounded-full"
      (click)="onCloseDialog()"
    >
      <span>Close</span>
    </button>
    
    <button
      class="bg-light-secondary-background dark:bg-dark-secondary-background cursor-pointer h-[40px] min-w-[100px] text-light-text dark:text-dark-text px-3 rounded-full"
      (click)="onResetForm()"
      [disabled]="!hasFormChanges()"
    >
      <span>Reset</span>
    </button>
    
    <app-loading-button
      [id]="'btn-submit-save-faq'"
      [class]="
        'bg-light-primary text-white cursor-pointer h-[40px] min-w-[100px] px-3 rounded-full'
      "
      [loading]="isLoadingSave()"
      [disabled]="!canSave()"
      [color]="'white'"
      (click)="onSaveFAQ()"
    >
      <span>Save</span>
    </app-loading-button>
  </div>
</div> 