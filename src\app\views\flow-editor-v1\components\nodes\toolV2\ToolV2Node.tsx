// @ts-nocheck
import { variableDefDevApi } from "@flow-editor-v1/api";
import GoToBlockHandle from "@flow-editor-v1/components/flow/GoToBlockHandle";
import CheckboxField from "@flow-editor-v1/components/form/CheckboxField";
import InputField from "@flow-editor-v1/components/form/InputField";
import SelectField from "@flow-editor-v1/components/form/SelectField";
import Slider<PERSON>ield from "@flow-editor-v1/components/form/SliderField";
import Icon from "@flow-editor-v1/components/icon/icon";
import NodeHeader from "@flow-editor-v1/components/nodes/NodeHeader";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor-v1/components/styled";
import NodeTooltip from "@flow-editor-v1/components/tooltip/NodeTooltip";
import { STUDIO_STATUS, TOOL_MODEL, TOOL_MODEL_LIST } from "@flow-editor-v1/constant";
import { useFlowInstance } from "@flow-editor-v1/hook";
import { FlowDebugState, VariableDefDev, VariableDefState } from "@flow-editor-v1/model";
import {
  useBuildFlowState,
  useFlowDebugState,
  useFlowInstanceState,
  useLayoutState,
  useStudioState,
  useVariableDefState,
  useVariableLocatedState,
} from "@flow-editor-v1/store";
import { isValidConnection } from "@flow-editor-v1/utils/flow";
import { extractVariableNames, hexToRgb } from "@flow-editor-v1/utils/util";
import { yupResolver } from "@hookform/resolvers/yup";
import { Divider, message, Modal, Tooltip, Typography } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useFieldArray, useForm, useWatch } from "react-hook-form";
import { Handle, Position } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";
import { VARIABLE_POSITION_TYPE, VARIABLE_TYPE, VARIABLE_TYPE_LIST } from "../../../constant/variable";
import { IToolV2NodeData } from "../../../model/tool-node-v2.model";

const StyledHandleSourceAnchor = styled.div<{ $bgColor; $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => (props.$data ? "white" : `rgba(255, 255, 255, 0.5)`)};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => (props.$data ? "normal" : "italic")};
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const variableFormSchema = yup.object().shape({
  id: yup.number(),
  ai_id: yup.string(),
  flow_id: yup.number(),
  var_name: yup.string().required('Variable name required').test('no-whitespace', 'Field cannot be empty or whitespace only', value => value.trim().length > 0),
  type: yup.string().required('Data type required').default(VARIABLE_TYPE.STRING),
  init_value: yup.string().nullable(),
});

const parametersFormControlSchema = yup.object().shape({
  param: yup.string().default(''),
  matching_variable: yup.number().nullable().default(null),
  is_required: yup.boolean().default(false),
});

const toolNodeV2DataFormSchema = yup.object().shape({
  system_prompt: yup.string().required("System prompt is required"),
  custom_prompt: yup.string(),
  parameters: yup
    .array()
    .of(parametersFormControlSchema)
    .required(),
  var_store_id: yup.number().nullable(),
  model: yup.string().required("Model is required").default(TOOL_MODEL.GGPT_4_O_mini),
  temperature: yup.number().required("Temperature is required").default(0.1),
  use_history: yup.boolean().default(false)
});

const ToolV2Node = ({data}: { data: IToolV2NodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [isHoverGoToBlock, setIsHoverGoToBlock] = useState<boolean>(false);
  const [showTooltip, setShowTooltip] = useState<boolean>(false);
  const [modalVariableOpen, setModalVariableOpen] = useState<boolean>(false);
  const [addingMatchingVariableParam, setAddingMatchingVariableParam] = useState<any>(undefined)
  const {flowInstance} = useFlowInstanceState((state) => state);
  const {variablesDev, setVariableDefDev} = useVariableDefState<VariableDefState>((state) => state);
  const {flow, setDirty} = useBuildFlowState((state) => state);
  const {setVariableLocations} = useVariableLocatedState(state => state);
  const {status} = useStudioState((state) => state);
  const {debug} = useFlowDebugState<FlowDebugState>(state => state);
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const [messageApi, contextHolder] = message.useMessage();
  const {theme} = useLayoutState(state => state);

  const {control, setValue, handleSubmit, reset} = useForm({
    resolver: yupResolver(toolNodeV2DataFormSchema),
  });

  const {
    control: controlVariable,
    setValue: setValueVariable,
    handleSubmit: handleSubmitVariable,
    reset: resetVariable,
    formState: {isValid}
  } = useForm({
    resolver: yupResolver(variableFormSchema)
  })

  const {
    fields: paramFields,
    remove: paramRemove,
    replace: paramReplace,
    update: paramUpdate,
  } = useFieldArray({
    control,
    name: "parameters",
  });

  const paramFieldsWatch = useWatch({
    control,
    name: "parameters",
  });

  const resetForm = () => {
    reset();
  };

  const resetVariableForm = () => {
    resetVariable()
    setValueVariable('type', VARIABLE_TYPE.STRING)
  }

  const handleAddVariable = (param: any) => {
    resetVariableForm();
    handleOpenModalVariable();
    setAddingMatchingVariableParam(param)
  }

  const onSubmit = (formValue) => {
    data.system_prompt = formValue?.system_prompt;
    data.custom_prompt = formValue?.custom_prompt;
    data.parameters = formValue?.parameters;
    data.var_store_id = formValue?.var_store_id;
    data.model = formValue?.model;
    data.temperature = formValue?.temperature;
    data.use_history = formValue?.use_history;

    const variableLocationsSystemPrompt = extractVariableNames(data.system_prompt)?.map(name => ({
      position_type: VARIABLE_POSITION_TYPE.FLOW,
      flow_dev_id: flow.id,
      node_id: data.id,
      var_def_dev_name: name,
    }));
    const variableLocationsCustomPrompt = extractVariableNames(data.custom_prompt)?.map(name => ({
      position_type: VARIABLE_POSITION_TYPE.FLOW,
      flow_dev_id: flow.id,
      node_id: data.id,
      var_def_dev_name: name,
    }));
    const variableLocationsMatchingVar = data.parameters.map(param => ({
      position_type: VARIABLE_POSITION_TYPE.FLOW,
      flow_dev_id: flow.id,
      node_id: data.id,
      var_def_dev_id: param.matching_variable,
    }))
    const variableLocationsVarStore = [{
      position_type: VARIABLE_POSITION_TYPE.FLOW,
      flow_dev_id: flow.id,
      node_id: data.id,
      var_def_dev_id: data.var_store_id,
    }]
    const variableLocations = [
      ...variableLocationsSystemPrompt,
      ...variableLocationsCustomPrompt,
      ...variableLocationsMatchingVar,
      ...variableLocationsVarStore
    ];
    setVariableLocations(variableLocations, VARIABLE_POSITION_TYPE.FLOW);

    resetForm();
    setModalOpen(false);
    setDirty();
  };

  // Handle hover state with delay because if it don't have delay it will popup in a milisecond
  useEffect(() => {
    let timeout;
    if (!isHoverGoToBlock) {
      timeout = setTimeout(() => setShowTooltip(true), 1000);
    } else {
      setShowTooltip(false);
    }
    return () => clearTimeout(timeout);
  }, [isHoverGoToBlock]);


  useEffect(() => {
    resetForm();
    if (data.parameters && data.parameters.length) {
      paramReplace(data.parameters.map((p) => ({
        param: p.param,
        matching_variable: p.matching_variable,
        is_required: Boolean(p.is_required)
      })))
    } else {
      paramRemove()
    }
    data.system_prompt && setValue("system_prompt", data.system_prompt);
    data.custom_prompt && setValue("custom_prompt", data.custom_prompt);
    data.var_store_id && setValue("var_store_id", data.var_store_id);
    data.model && setValue("model", data.model);
    data.temperature && setValue("temperature", Number(data.temperature));
    data.use_history && setValue("use_history", Boolean(data.use_history));
  }, [JSON.stringify(data)]);

  const onSubmitVariable = async (formValue) => {
    if (flow.id) {
      const data = {
        ...formValue,
        id: formValue.id === 0 ? NaN : formValue.id,
        flow_dev_id: flow.id,
      }
      try {
        const variable_saved = await variableDefDevApi.createVariableDev(data)
        const res: VariableDefDev[] = await variableDefDevApi.getVariableDev({key_word: ''})
        setVariableDefDev(res)
        messageApi.success('Create successfully')
        setModalVariableOpen(false)
        if (addingMatchingVariableParam) {
          const index = paramFields.findIndex(v => v.id === addingMatchingVariableParam)
          if (index > -1) paramUpdate(index, {...paramFields[index], matching_variable: variable_saved.id});
        }
      } catch (error) {
        messageApi.error(error.message)
      }
      resetVariableForm()
    }
  };

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    if (!data.debug) setModalOpen(true);
  }, [data.debug]);

  const handleOpenModalVariable = useCallback(() => {
    if (!data.debug) setModalVariableOpen(true);

  }, [data.debug]);

  const handleChangeSystemPrompt = (value: string) => {
    const regexVariable = /\[\((\w+)\)\]/g;
    const variableMatches = [...value.matchAll(regexVariable)];
    const variableExtracted = variableMatches.map(match => match[1]);

    const updateParamFields = () => {
      const uniqueVariables = new Set(variableExtracted);
      const mappingIndex = Array.from(uniqueVariables).reduce((obj, item, index) => {
        obj[index] = item;
        return obj;
      }, {});

      let newParamFields = [];
      for (const [key, value] of Object.entries(mappingIndex)) {
        const matchedParam = paramFieldsWatch.find(v => v.param === value);
        newParamFields[key] = matchedParam
          ? matchedParam
          : {param: value, matching_variable: null, is_required: false};
      }
      return newParamFields;
    };

    const newParamFields = updateParamFields();
    if (variableExtracted.length >= paramFieldsWatch.length) {
      paramReplace(newParamFields);
    } else if (variableExtracted.length < paramFieldsWatch.length) {
      paramReplace(newParamFields);
    } else {
      paramRemove();
    }
  }


  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $debug={data.debug} $theme={theme}>
      {contextHolder}
      {
        debug && (
          <div className="absolute left-0 text-[10px]" style={{top: -20, color: '#D4D9E2'}}>{data.id}</div>
        )
      }
      {status && status === STUDIO_STATUS.DEV && (
        <NodeTooltip data={data} selected={data.selected}/>
      )}
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
        <StyledHandleSourceAnchor
          $bgColor={data.node_color}
          $data={data.parameters || data.system_prompt}
          onDoubleClick={handleOpenModal}
        >
          {data.parameters.length > 0 || data.system_prompt
            ? data.system_prompt
            : "Configure"}
        </StyledHandleSourceAnchor>
        {
          !data.goToBlockSource ? (
            <Handle
              type="source"
              position={Position.Right}
              id={`${data.id}_source#1`}
              style={{
                height: 10,
                width: 10,
                backgroundColor: theme === 'dark' ? 'white' : 'black',
              }}
            />
          ) : (() => {
              const key = `${data.id}_source#1`
              const goToBlock = data.goToBlockSource.find(v => v.sourceHandle === key)
              if (goToBlock) {
                return <GoToBlockHandle
                  data={data}
                  sourceHandle={key}
                  style={{
                    height: 14,
                    width: 84,
                    right: -80,
                    backgroundColor: theme === 'dark' ? 'white' : 'black',
                    fontSize: 10,
                    color: theme === 'dark' ? 'black' : 'white',
                    border: 'none',
                    borderRadius: 10,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                />
              }

              return <Handle
                type="source"
                position={Position.Right}
                id={`${data.id}_source#1`}
                style={{
                  height: 10,
                  width: 10,
                  backgroundColor: theme === 'dark' ? 'white' : 'black',
                }}
              />
            }
          )()
        }
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-col space-y-4 mt-6"
        >
          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
              System Prompt <span style={{color: 'red'}}>*</span>
            </Typography.Text>
            <InputField
              type={"textarea"}
              name={`system_prompt`}
              control={control}
              setValue={setValue}
              onChangeInput={handleChangeSystemPrompt}
            />
          </div>

          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
              Custom Prompt
            </Typography.Text>
            <InputField
              type={"textarea"}
              name={`custom_prompt`}
              control={control}
              setValue={setValue}
            />
          </div>

          <div className="w-full grid grid-cols-2 items-center gap-5">
            <div className="col-span-1 w-full flex flex-col space-y-2">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Model</Typography.Text>
              <SelectField
                name={"model"}
                disabled={status && status === STUDIO_STATUS.LIVE}
                control={control}
                options={TOOL_MODEL_LIST}
                defaultValue={TOOL_MODEL.GGPT_4_O_mini}
              />
            </div>

            <div className="col-span-1 w-full flex flex-col space-y-2">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Temperature
              </Typography.Text>
              <div className="w-full grid grid-cols-4 gap-3">
                <div className="col-span-3">
                  <SliderField
                    name={"temperature"}
                    disabled={status && status === STUDIO_STATUS.LIVE}
                    control={control}
                    min={0}
                    max={2}
                    step={0.1}
                    defaultValue={0}
                  />
                </div>
                <div className="col-span-1">
                  <InputField
                    type={"input"}
                    name={`temperature`}
                    control={control}
                    disabled={status && status === STUDIO_STATUS.LIVE}
                    setValue={setValue}
                  />
                </div>
              </div>
            </div>

            <div className="col-span-1 w-full flex space-x-2">
              <CheckboxField
                name={`use_history`}
                control={control}
                disabled={status && status === STUDIO_STATUS.LIVE}
              />
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Use history
              </Typography.Text>
            </div>
          </div>

          <Divider style={{borderColor: "white"}}/>

          <div className="flex flex-col">
            <div className="grid grid-cols-5 gap-5">
              <div className="col-span-2 w-full">
                <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Param</Typography.Text>
              </div>
              <div className="col-span-2 w-full">
                <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Matching Variable</Typography.Text>
              </div>
              <div className="col-span-1 w-full">
                <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content"></Typography.Text>
              </div>
            </div>
            <div className="grid grid-cols-5 gap-4 mt-3 items-center">
              {paramFields.map((field, index) => (
                <React.Fragment key={field.id}>
                  <div className="col-span-2 w-full">
                    <InputField
                      type={"input"}
                      name={`parameters[${index}].param`}
                      control={control}
                      disabled={true}
                      setValue={setValue}
                    />
                  </div>
                  <div className="col-span-2 w-full">
                    <div className="w-full grid grid-cols-4 gap-2">
                      <div className="col-span-3">
                        <SelectField
                          name={`parameters[${index}].matching_variable`}
                          control={control}
                          disabled={status && status === STUDIO_STATUS.LIVE}
                          options={variablesDev.map((v) => ({
                            ...v,
                            key: v.id,
                            value: v.id,
                            label: v.var_name,
                          }))}
                        />
                      </div>
                      <Tooltip title="Add matching variable">
                        <div onClick={() => handleAddVariable(field.id)}
                             className="col-span-1 h-[30px] aspect-square bg-light-primary rounded flex items-center justify-center cursor-pointer">
                          <Icon iconName={'RiAddBoxLine'} size={16} style={{color: 'white'}}/>
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                  <div className="col-span-1 w-full">
                    <div className="w-full flex space-x-2">
                      <CheckboxField
                        name={`parameters[${index}].is_required`}
                        control={control}
                        disabled={status && status === STUDIO_STATUS.LIVE}
                      />
                      <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                        Required
                      </Typography.Text>
                    </div>
                  </div>
                </React.Fragment>
              ))}
            </div>
          </div>

          <Divider style={{borderColor: "white"}}/>

          <div className="w-full flex justify-end">
            <div className="flex justify-end items-center space-x-2">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Store response in:
              </Typography.Text>
              <SelectField
                style={{width: "250px"}}
                name={"var_store_id"}
                control={control}
                disabled={status && status === STUDIO_STATUS.LIVE}
                options={variablesDev.map((v) => ({
                  ...v,
                  key: v.id,
                  value: v.id,
                  label: v.var_name,
                }))}
              />
            </div>
          </div>

          <div
            className="w-full flex justify-end items-center space-x-4"
            style={{marginTop: 24}}
          >
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() => setModalOpen(false)}
            >
              Cancel
            </StyledButtonModal>
            <StyledButtonModal
              type="submit"
              key="save"
              $bgColor={"#7F75CF"}
              $color={"white"}
              disabled={status && status === STUDIO_STATUS.LIVE}
            >
              Save
            </StyledButtonModal>
          </div>
        </form>
      </Modal>

      <Modal
        title={'Add variable'}
        centered
        open={modalVariableOpen}
        onOk={() => setModalVariableOpen(false)}
        onCancel={() => setModalVariableOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <form onSubmit={handleSubmitVariable(onSubmitVariable)} className="flex flex-col space-y-4 mt-6">
          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Variable name <span style={{color: 'red'}}>*</span></Typography.Text>
            <InputField type={'input'} name={`var_name`} control={controlVariable} setValue={setValueVariable}/>
          </div>

          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Data type <span style={{color: 'red'}}>*</span></Typography.Text>
            <SelectField options={VARIABLE_TYPE_LIST} name={'type'} control={controlVariable}/>
          </div>

          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Init value</Typography.Text>
            <InputField type={'textarea'} name={`init_value`} control={controlVariable} setValue={setValueVariable}/>
          </div>

          <div className="w-full flex justify-end items-center space-x-4 mt-3">
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() => setModalVariableOpen(false)}
            >
              Cancel
          </StyledButtonModal>
            <StyledButtonModal
              type="submit"
              key="save"
              $bgColor={"#7F75CF"}
              $color={"white"}
              disabled={(status && status === STUDIO_STATUS.LIVE) || (status && status === STUDIO_STATUS.DEV && !isValid)}
            >
              Save
            </StyledButtonModal>
          </div>
        </form>
      </Modal>
    </StyledNodeFlow>
  );
};

export default ToolV2Node;
