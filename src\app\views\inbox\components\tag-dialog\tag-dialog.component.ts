import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit, inject, signal, computed, input, output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatCheckboxModule } from '@angular/material/checkbox';
import {BaseComponent, LoadingButtonComponent, SvgIconComponent} from '@shared/components';
import { TagService } from '@shared/services';
import {DxButton, DxLoadingButton} from '@dx-ui/ui';

@Component({
  selector: 'app-tag-dialog',
  templateUrl: './tag-dialog.component.html',
  styleUrls: ['./tag-dialog.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatCheckboxModule,
    SvgIconComponent,
    DxButton,
    DxLoadingButton,
  ],
  host: {
    class: 'h-full'
  }
})
export class TagDialogComponent extends BaseComponent implements OnInit {
  // Input signals - not needed since we use MAT_DIALOG_DATA

  // Output signals (replaced @Output)
  tagsUpdated = output<{ conversationId: string, updatedTags: any[] }>();

  // Internal state signals
  conversationId = signal<string>('');
  selectedTags = signal<string[]>([]);
  isLoadingSave = signal(false);

  // Computed signals
  availableTags = computed(() => this.data?.listTagForConversationAvailable || []);

  hasSelectedTags = computed(() => this.selectedTags().length > 0);

  selectedTagsCount = computed(() => this.selectedTags().length);

  availableTagsCount = computed(() => this.availableTags().length);

  hasChanges = computed(() => {
    const current = [...this.selectedTags()].sort();
    const initial = [...(this.data?.tagAddSelected || [])].sort();
    return JSON.stringify(current) !== JSON.stringify(initial);
  });

  canSave = computed(() => this.hasChanges() && !this.isLoadingSave());

  private tagService = inject(TagService);

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: {
      conversationId: string;
      listTagForConversationAvailable: any[];
      tagAddSelected: string[];
    },
    private dialogRef?: MatDialogRef<TagDialogComponent>
  ) {
    super();
    if (data) {
      this.conversationId.set(data.conversationId);
      this.selectedTags.set([...data.tagAddSelected || []]);
    }
  }

  ngOnInit(): void {
    console.log('Initial selected tags:', this.selectedTags());
    console.log('Available tags:', this.availableTags());
  }

  // Handle tag selection locally with signals
  onTagSelectionChange(event: any, tagId: string): void {
    const currentTags = this.selectedTags();

    if (event.checked) {
      if (!currentTags.includes(tagId)) {
        this.selectedTags.set([...currentTags, tagId]);
      }
    } else {
      this.selectedTags.set(currentTags.filter(id => id !== tagId));
    }
  }

  // Check if tag is selected
  isTagSelected(tagId: string): boolean {
    return this.selectedTags().includes(tagId);
  }

  // Handle save operation with signals
  onSaveAddTag(): void {
    if (!this.canSave()) return;

    this.isLoadingSave.set(true);

    const body = {
      conversation_id: this.conversationId(),
      tag_ids: this.selectedTags(),
    };

    this.tagService.assignTag(body).subscribe({
      next: (res) => {
      this.showSnackBar(res.detail, 'success');

        // Get updated tag objects for the selected IDs
        const updatedTags = this.availableTags().filter(tag =>
          this.selectedTags().includes(tag.id)
      );

        // Emit updated tags data
      this.tagsUpdated.emit({
          conversationId: this.conversationId(),
        updatedTags: updatedTags
      });

        this.isLoadingSave.set(false);
      this.onCloseDialog();
      },
      error: (err) => {
        this.showSnackBar('Failed to save tags', 'error');
        this.isLoadingSave.set(false);
      }
    });
  }

  // Reset selections
  onResetSelections(): void {
    this.selectedTags.set([...this.data?.tagAddSelected || []]);
  }

  // Select all tags
  onSelectAll(): void {
    const allTagIds = this.availableTags().map(tag => tag.id);
    this.selectedTags.set([...allTagIds]);
  }

  // Clear all selections
  onClearAll(): void {
    this.selectedTags.set([]);
  }

  onCloseDialog(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    }
  }

  getColor(config: string): string {
    try {
      const parsedConfig = JSON.parse(config);
      return parsedConfig.color || '#000000';
    } catch (error) {
      return config ? `#${config}` : '#000000';
    }
  }

  getTextColor(bgColor: string): string {
    const hex = bgColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }
}
