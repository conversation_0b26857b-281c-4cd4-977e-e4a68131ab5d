<div class="h-full flex flex-col overflow-hidden">
  <div class="flex items-start justify-between">
    <h1
      class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
    >
      API
    </h1>
    <app-flow-env-select></app-flow-env-select>
  </div>
  <div class="grid grid-cols-1 md:grid-cols-7 gap-y-4 md:gap-x-4 h-full mt-6">
    <div class="col-span-2 flex flex-col w-full">
      <div
        class="rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
      >
        <div
          class="p-4 flex flex-wrap 2xl:flex-nowrap gap-2 2xl:gap-0 2xl:space-x-2 items-center justify-between border-b border-primary-border dark:border-dark-primary-border"
        >
          <dx-form-field
            class="w-full"
            id="search"
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <app-svg-icon
              dxPrefix
              type="icSearch"
              class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
            ></app-svg-icon>
            <input
              dxInput
              [(ngModel)]="searchModel.key_word"
              (ngModelChange)="searchSubject.next($event)"
              [type]="'text'"
              placeholder="Search by name"
            />
          </dx-form-field>
          <button
            class="flex-shrink-0 w-full 2xl:w-fit"
            dx-button="filled"
            [disabled]="studioStatus() === STUDIO_STATUS.LIVE"
            (click)="createApi()"
          >
            Add API
          </button>
        </div>

        <div
          class="h-full list-apis flex flex-col justify-start overflow-y-auto"
        >
          @for (data of listApi(); track $index; let first = $first, last =
          $last) {
          <div
            class="p-4 flex flex-col space-y-3 hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover cursor-pointer group"
            [ngClass]="{
              'border-b border-primary-border dark:border-dark-primary-border':
                !last,
              'rounded-b-2xl': last,
              'bg-base-400-hover dark:bg-dark-base-400-hover':
                data.id === apiSelectedId()
            }"
            (click)="selectApi(data?.id)"
          >
            <div class="space-x-3 flex items-end justify-between">
              <div class="flex space-x-3 items-center justify-start">
                <div class="w-5 h-5">
                  <app-svg-icon
                    type="icApi"
                    class="w-5 h-5 !text-neutral-content dark:!text-dark-neutral-content group-hover:!text-primary-hover dark:group-hover:!text-dark-primary-hover"
                    [ngClass]="{
                      '!text-primary-hover dark:!text-dark-primary-hover':
                        data.id === apiSelectedId()
                    }"
                  ></app-svg-icon>
                </div>
                <div
                  class="w-full text-neutral-content dark:text-dark-neutral-content text-ellipsis group-hover:text-base-content group-hover:dark:text-dark-base-content"
                  [ngClass]="{
                    '!text-base-content dark:!text-dark-base-content':
                      data.id === apiSelectedId()
                  }"
                >
                  {{ data.name }}
                </div>
              </div>
              @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
              <div class="flex space-x-3 items-center justify-end">
                <app-svg-icon
                  type="icEdit"
                  class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer hover:text-primary-hover dark:hover:text-dark-primary-hover"
                  (click)="editApi(data, $event)"
                ></app-svg-icon>
                <app-svg-icon
                  type="icTrash"
                  class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                  (click)="deleteApi(data, $event)"
                ></app-svg-icon>
              </div>
              }
            </div>
            @if (data.description) {
            <div
              class="text-sm text-neutral-content dark:text-dark-neutral-content ml-8"
            >
              {{ data.description }}
            </div>
            }
          </div>
          } @empty {
          <div class="flex items-center justify-center py-4">
            <div
              class="text-neutral-content dark:text-dark-neutral-content italic text-[15px]"
            >
              No data available
            </div>
          </div>
          }
        </div>
      </div>
    </div>

    <div
      class="col-span-5 flex flex-col w-full h-full rounded-2xl border border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
    >
      <div
        class="flex flex-row overflow-x-auto items-end bg-base-100 dark:bg-dark-base-100 rounded-t-2xl pr-4"
      >
        @for (tab of listApiTab(); track $index) {
        <div class="flex items-end">
          @if (tab.id === apiSelectedId()) {
          <app-svg-icon
            type="icRightCorner"
            class="w-[11px] h-4 !text-base-200 dark:!text-dark-base-200"
          ></app-svg-icon>
          }
          <div
            class="px-4 py-3 space-x-2 flex items-center text-neutral-content dark:text-dark-neutral-content cursor-pointer"
            [ngClass]="{
              'bg-base-200 dark:bg-dark-base-200 text-base-content dark:text-dark-base-content':
                tab.id === apiSelectedId(),
              'rounded-t-2xl group': tab.id == apiSelectedId(),
              'text-neutral-content dark:text-dark-neutral-content':
                tab.id !== apiSelectedId()
            }"
            (click)="selectApi(tab.id)"
          >
            <app-svg-icon
              type="icApi"
              class="w-5 h-5 group-hover:!text-primary-hover dark:group-hover:!text-dark-primary-hover"
            ></app-svg-icon>
            <div class="text-base font-semibold">
              {{ tab.name }}
            </div>
            @if (tab.isDirty) {
            <div class="w-3 h-3 bg-error bg-dark-error rounded-full"></div>
            } @else { @if (!(listApi().length === 1 || listApiTab().length ===
            1)) {
            <app-svg-icon
              type="icClose"
              class="w-5 h-5 cursor-pointer"
              (click)="removeApiSelectedTab($index, $event)"
            ></app-svg-icon>
            } }
          </div>
          @if (tab.id === apiSelectedId()) {
          <app-svg-icon
            type="icRightCorner"
            class="w-[11px] h-4 scale-x-[-1] !text-base-200 dark:!text-dark-base-200"
          ></app-svg-icon>
          }
        </div>
        <div
          class="items-center justify-center flex h-full text-primary-border dark:text-dark-primary-border text-xl"
        >
          |
        </div>
        } @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
        <div
          class="flex h-12 w-12 items-center justify-center !text-neutral-content dark:!text-dark-neutral-content"
        >
          <app-svg-icon
            type="icPlus"
            class="w-6 h-6 cursor-pointer"
            (click)="createApi()"
          ></app-svg-icon>
        </div>
        }
      </div>

      <div class="flex-1 w-full overflow-y-auto flex flex-col p-6">
        @if (apiSelected() && listApiTab().length > 0) {
        <div class="w-full flex flex-col justify-between">
          <div
            class="flex items-center space-x-3 mb-4 justify-start"
            [formGroup]="formGroupApiDetail"
          >
            <dx-form-field
              [style.margin-bottom]="0"
              [hideRequiredMarker]="true"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <!--                <input dxInput formControlName="method" placeholder="API Method">-->
              <dx-select
                formControlName="method"
                [disabled]="studioStoreStatus() === STUDIO_STATUS.LIVE"
              >
                @for (method of REST_API_METHOD_LIST; track $index) {
                <dx-option [value]="method.value">
                  {{ method.label }}
                </dx-option>
                }
              </dx-select>
            </dx-form-field>
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [hideRequiredMarker]="true"
              [subscriptHidden]="true"
            >
              <input
                dxInput
                formControlName="url"
                placeholder="Request URL"
                [disabled]="studioStoreStatus() === STUDIO_STATUS.LIVE"
              />
            </dx-form-field>
            <button
              dxLoadingButton="elevated"
              class="flex justify-end space-x-2"
              [disabled]="formGroupApiDetail.invalid"
              [loading]="isSendingAPI()"
              (click)="testApi()"
            >
              <div class="flex items-center space-x-2 m-2">
                <app-svg-icon
                  dxPrefix
                  type="icSend"
                  class="w-6 h-6 !text-primary dark:!text-dark-primary"
                ></app-svg-icon>
                <span class="text-sm text-primary dark:text-dark-primary"
                  >Send</span
                >
              </div>
            </button>
            <button
              dxLoadingButton="filled"
              [loading]="isSubmitting()"
              [disabled]="
                formGroupApiDetail.invalid ||
                studioStoreStatus() === STUDIO_STATUS.LIVE
              "
              (click)="saveApi(true)"
            >
              Save
            </button>
          </div>
        </div>
        <div
          class="flex-grow w-full flex flex-col items-stretch"
          style="height: calc(100vh - 500px)"
        >
          <as-split
            direction="vertical"
            [gutterSize]="10"
            [style.--_as-gutter-background-color]="'transparent'"
          >
            <as-split-area
              class="border-b border-primary-border dark:border-dark-primary-border split-area-content"
              size="50"
            >
              <div class="w-full h-full">
                <dx-tab-group
                  [(selectedIndex)]="tabIndex"
                  dx-stretch-tabs="false"
                  dx-align-tabs="start"
                  class="h-full tab-api-detail"
                >
                  <dx-tab label="Params">
                    <div
                      class="w-full h-full overflow-y-auto grid grid-cols-3 gap-x-6 pt-2 px-2"
                    >
                      <div
                        class="col-span-1 text-sm text-neutral-content dark:text-dark-neutral-content"
                      >
                        Key
                      </div>
                      <div
                        class="col-span-2 text-sm text-neutral-content dark:text-dark-neutral-content"
                      >
                        Value
                      </div>
                      @if (parameterData().length > 0) { @for (param of
                      parameterData(); track $index) {
                      <dx-form-field
                        class="col-span-1 mt-2"
                        [style.margin-bottom]="0"
                        [style.--dx-form-field-label-offset-y]="0"
                        [subscriptHidden]="true"
                      >
                        <input
                          dxInput
                          placeholder="Key"
                          [ngModel]="param.key"
                          (ngModelChange)="onChangeParam($event, $index, 'key')"
                          [disabled]="
                            studioStoreStatus() === STUDIO_STATUS.LIVE
                          "
                        />
                      </dx-form-field>
                      <div
                        class="col-span-2 mt-2 flex space-x-4 items-center justify-between"
                      >
                        <dx-form-field
                          class="w-full"
                          [style.margin-bottom]="0"
                          [style.--dx-form-field-label-offset-y]="0"
                          [subscriptHidden]="true"
                        >
                          <input
                            dxInput
                            placeholder="Value"
                            [ngModel]="param.value"
                            type="text"
                            (ngModelChange)="
                              onChangeParam($event, $index, 'value')
                            "
                            [disabled]="
                              studioStoreStatus() === STUDIO_STATUS.LIVE
                            "
                          />
                        </dx-form-field>
                        @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
                        <app-svg-icon
                          type="icTrash"
                          class="w-6 h-6 cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
                          (click)="removeParam($index)"
                        ></app-svg-icon>
                        }
                      </div>
                      }} @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
                      <button
                        id="btn-add-new-param"
                        type="button"
                        (click)="addParam()"
                        class="!text-primary dark:!text-dark-primary text-[14px] flex items-center space-x-2 mt-4"
                      >
                        <app-svg-icon
                          type="icPlus"
                          class="w-6 h-6"
                        ></app-svg-icon>
                        <div>Add params</div>
                      </button>
                      }
                    </div>
                  </dx-tab>
                  <dx-tab label="Headers">
                    <div
                      class="w-full h-full overflow-y-auto grid grid-cols-3 gap-x-6 pt-2 px-2"
                    >
                      <div
                        class="col-span-1 text-sm text-neutral-content dark:text-dark-neutral-content"
                      >
                        Key
                      </div>
                      <div
                        class="col-span-2 text-sm text-neutral-content dark:text-dark-neutral-content"
                      >
                        Value
                      </div>

                      <!--                        <div class="">-->
                      @for (header of headerData(); track $index) {
                      <dx-form-field
                        class="col-span-1 mt-2"
                        [style.margin-bottom]="0"
                        [style.--dx-form-field-label-offset-y]="0"
                        [subscriptHidden]="true"
                      >
                        <input
                          dxInput
                          placeholder="Key"
                          [(ngModel)]="header.key"
                          [ngModelOptions]="{ standalone: true }"
                          [disabled]="
                            studioStoreStatus() === STUDIO_STATUS.LIVE
                          "
                        />
                      </dx-form-field>
                      <div
                        class="col-span-2 mt-2 flex space-x-4 items-center justify-between"
                      >
                        <dx-form-field
                          class="w-full"
                          [style.margin-bottom]="0"
                          [style.--dx-form-field-label-offset-y]="0"
                          [subscriptHidden]="true"
                        >
                          <input
                            dxInput
                            placeholder="Value"
                            [(ngModel)]="header.value"
                            [ngModelOptions]="{ standalone: true }"
                            [disabled]="
                              studioStoreStatus() === STUDIO_STATUS.LIVE
                            "
                          />
                        </dx-form-field>
                        @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
                        <app-svg-icon
                          type="icTrash"
                          class="w-6 h-6 cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
                          (click)="removeHeader($index)"
                        ></app-svg-icon>
                        }
                      </div>
                      } @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
                      <button
                        id="btn-add-new-param"
                        type="button"
                        (click)="addHeader()"
                        class="!text-primary dark:!text-dark-primary text-[14px] flex items-center space-x-2 mt-4"
                      >
                        <app-svg-icon
                          type="icPlus"
                          class="w-6 h-6"
                        ></app-svg-icon>
                        <div>Add headers</div>
                      </button>
                      }
                    </div>
                  </dx-tab>
                  <dx-tab
                    label="Body"
                    [disabled]="
                      formGroupApiDetail.get('method')?.value !== 'POST' &&
                      formGroupApiDetail.get('method')?.value !== 'PUT'
                    "
                  >
                    <div
                      class="min-h-[14dvw] h-full bg-white dark:bg-dark-secondary-background py-6 rounded-xl border border-primary-border dark:border-dark-primary-border"
                    >
                      <ngx-monaco-editor
                        [options]="editorOptionsBody()"
                        [ngModel]="body()"
                        (ngModelChange)="onChangeBody($event)"
                      ></ngx-monaco-editor>
                    </div>
                  </dx-tab>
                  <dx-tab label="Scripts">
                    <div
                      class="min-h-[27.5dvh] w-full flex flex-col overflow-y-auto"
                    >
                      <div class="grid flex-1 grid-cols-6 gap-3">
                        <div
                          class="col-span-1 flex flex-col h-full text-sm text-neutral-content dark:text-dark-neutral-content"
                        >
                          <div
                            class="px-4 py-2 cursor-pointer text-sm"
                            [ngClass]="{
                              'bg-base-400 dark:bg-dark-base-400 rounded-[12px]':
                                selectedTab === 'pre-request'
                            }"
                            (click)="selectTab('pre-request')"
                          >
                            Pre-request
                          </div>
                          <div
                            class="px-4 py-2 cursor-pointer text-sm"
                            [ngClass]="{
                              'bg-base-400 dark:bg-dark-base-400 rounded-[12px]':
                                selectedTab === 'post-response'
                            }"
                            (click)="selectTab('post-response')"
                          >
                            Post-response
                          </div>
                        </div>
                        <div class="col-span-5 flex flex-col">
                          <div
                            class="h-full bg-white dark:bg-dark-secondary-background py-6 rounded-xl border border-primary-border dark:border-dark-primary-border"
                          >
                            @if (selectedTab === 'pre-request') {
                            <ngx-monaco-editor
                              [options]="editorOptionsScripts()"
                              [ngModel]="preRequest()"
                              (ngModelChange)="onChangePreRequest($event)"
                            ></ngx-monaco-editor>
                            } @else if (selectedTab === 'post-response') {
                            <ngx-monaco-editor
                              [options]="editorOptionsScripts()"
                              [ngModel]="postResponse()"
                              (ngModelChange)="onChangePostResponse($event)"
                            ></ngx-monaco-editor>
                            }
                          </div>
                        </div>
                      </div>
                    </div>
                  </dx-tab>
                </dx-tab-group>
              </div>
            </as-split-area>
            <as-split-area size="50" class="split-area-content">
              <div class="h-full w-full flex flex-col items-stretch pt-2">
                <div
                  class="text-[15px] text-neutral-content dark:text-dark-neutral-content"
                >
                  Response
                </div>
                <div
                  class="min-h-[16dvw] mt-2 bg-white dark:bg-dark-secondary-background py-6 rounded-xl border border-primary-border dark:border-dark-primary-border"
                >
                  <ngx-monaco-editor
                    [options]="editorOptionsResponse()"
                    [ngModel]="response"
                  ></ngx-monaco-editor>
                </div>
              </div>
            </as-split-area>
          </as-split>
        </div>
        } @else {
        <div
          class="w-full h-full flex items-center justify-center text-neutral-content dark:text-dark-neutral-content"
        >
          Start adding your first Api to select
        </div>
        }
      </div>
    </div>
  </div>
</div>

<!--
<div class="cs-drawer-wrapper bg-transparent text-light-text dark:text-dark-text rounded-lg flex flex-col">
  @if (studioStatus() === STUDIO_STATUS.DEV) {
    <div class="drawer-header flex justify-between border-b border-b-light-border-line dark:border-b-dark-border-line">
      <div class="api-info">
        <span class="text-[28px] font-bold text-base-content dark:text-dark-base-content">API</span>
      </div>
    </div>
    <div class="flex relative h-full ">
      <div class="list-api border-r border-r-light-border-line dark:border-r-dark-border-line relative">
        <div class="h-full">
          <div class="py-6 px-8">
            <input id="input-search-api" class="w-full p-2 border border-light-border-line dark:border-dark-border-line rounded-md focus:outline-none bg-light-secondary-background dark:bg-dark-secondary-background"
              type="text" placeholder="Search by name" [(ngModel)]="searchModel.key_word"
              (keydown.enter)="doSearch($event)">
          </div>
          <div class="overflow-y-auto pb-3" style="height: calc(100% - 141px); max-height: 66.6vh">
            @if (listApi.length === 0) {
              <div class="api-item !justify-center hover:!cursor-default !bg-transparent">
                <div>No api added</div>
              </div>
            }
            @if (listApi.length > 0) {
              @for (api of listApi; track api) {
                <div class="api-item" (click)="selectApi(api?.id)" [class.activate]="api.id === apiSelectedId">
                  <div class="truncate">{{ api.name }}</div>
                  <div class="flex items-center space-x-2">
                    <app-custom-icon [size]="18" (click)="editApi(api, $event)" class="text-[#fa6505]"  iconName="heroPencilSquareMini"></app-custom-icon>
                    <app-custom-icon [size]="18" (click)="deleteApi(api, $event)" class="text-red-500" iconName="heroTrashMini"></app-custom-icon>
                  </div>
                </div>
              }
            }
          </div>

        </div>
        <div
          class="px-6 py-4 absolute left-0 bottom-0 right-0 bg-transparent rounded-bl flex items-center justify-center">
          <button id="btn-add-new-api-2" type="button"
            class="bg-light-primary px-4 py-2 rounded-lg flex items-center space-x-1 text-white"
            (click)="createApi()">
            <ng-icon name="heroPlusMini" class="text-2xl" color="white"></ng-icon>
            <span>Add new api</span>
          </button>
        </div>
      </div>
      <div class="flex-1 flex flex-col h-full">
        <div class="messages h-full overflow-y-auto">
          <div class="chat-content-container h-full">
            <div class="w-full h-full flex flex-col">
              @if (listApi.length > 0) {
                <div class="h-12 bg-gray-50 ">
                  <ng-scrollbar class="scroll-custom-horizontal" track="horizontal" appearance="compact"
                    visibility="hover">
                    @if (listApi.length > 0) {
                      <div class="flex items-start">
                        @for (apiTab of listApiTab; track apiTab; let index = $index) {
                          <div
                            class="w-48 h-12 flex items-center justify-between space-x-4 px-3 border-r border-r-gray-200 hover:cursor-pointer"
                            [ngClass]="apiTab.id === apiSelectedId ? 'border-t-2 border-t-light-primary bg-light-hover dark:bg-dark-hover' : ''"
                            (click)="apiTab? selectApi(apiTab?.id) : null">
                            <div class="truncate" [class.font-bold]="apiTab.id === apiSelectedId">{{ apiTab.name }}</div>
                            @if (!apiTab?.isDirty) {
                              <div (click)="removeApiSelectedTab(index, $event)">
                                <ng-icon name="heroXMarkSolid"></ng-icon>
                              </div>
                            }
                            @if (apiTab?.isDirty) {
                              <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                            }
                          </div>
                        }
                        <div (click)="createApi()"
                          class="w-12 h-12 border-r bg-light-secondary-background dark:bg-dark-secondary-background border-r-gray-200 flex items-center justify-center hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover">
                          <ng-icon class="text-lg" name="heroPlusSolid"></ng-icon>
                        </div>
                      </div>
                    }
                  </ng-scrollbar>
                </div>
              }
              &lt;!&ndash;              &ndash;&gt;
              @if (apiSelected) {
                <form [formGroup]="formGroupApiDetail" class="w-full">
                  <div class="p-4 w-full flex items-center justify-between space-x-4 border-b border-b-gray-200">
                    <div class="flex-grow w-full flex items-center space-x-3">
                      <app-select [options]="REST_API_METHOD_LIST" formControlName="method"
                      (selectionChange)="formGroupApiDetail.patchValue({ method: $event })"></app-select>
                      &lt;!&ndash;                      <app-search-input&ndash;&gt;
                      &lt;!&ndash;                        class="select-custom"&ndash;&gt;
                      &lt;!&ndash;                        [listOptions]="REST_API_METHOD_LIST"&ndash;&gt;
                      &lt;!&ndash;                        [setOptionValue]="'value'"&ndash;&gt;
                      &lt;!&ndash;                        [setOptionLabel]="'label'"&ndash;&gt;
                      &lt;!&ndash;                        [value]="formGroupApiDetail.get('method').value"&ndash;&gt;
                      &lt;!&ndash;                        (valueChange)="formGroupApiDetail.patchValue({ method: $event })"&ndash;&gt;
                      &lt;!&ndash;                        [setCanBeSearch]="false"&ndash;&gt;
                      &lt;!&ndash;                        [haveTooltip]="false"&ndash;&gt;
                    &lt;!&ndash;                      ></app-search-input>&ndash;&gt;
                    <input class="w-5/6 h-[40px] p-2 border border-gray-200 rounded-md focus:outline-none bg-transparent"
                      type="text" formControlName="url">
                  </div>
                  <div class="flex items-center space-x-3">
                    <app-loading-button [id]="'btn-send-api'"
                      [class]="'border border-gray-200 bg-white px-4 py-2 rounded-lg flex items-center space-x-1 text-light-orange button-disabled h-[40px] hover:cursor-pointer'"
                      [loading]="isSendingAPI" [disabled]="isSendingAPI && !apiSelected.url" [color]="'primary'"
                      (click)="testApi()">
                      &lt;!&ndash;[color]="'#FFA500'"&ndash;&gt;
                      <span class="whitespace-nowrap">Send</span>
                    </app-loading-button>
                    <app-loading-button [id]="'btn-save-api-changed'"
                      [class]="'bg-light-primary px-4 py-2 rounded-lg flex items-center space-x-1 text-white h-[40px] hover:cursor-pointer'"
                      [loading]="isCreateOrUpdateApi" [disabled]="isCreateOrUpdateApi" [color]="'white'"
                      (click)="saveApi(true)">
                      <span class="whitespace-nowrap">Save</span>
                    </app-loading-button>
                  </div>
                </div>
              </form>
              <div class="flex-grow w-full flex flex-col items-stretch" style="height: calc(100vh - 500px)">
                <as-split direction="vertical">
                  <as-split-area size="50">
                    <div class="w-full h-full">
                      <mat-tab-group [(selectedIndex)]="tabIndex" mat-stretch-tabs="false" mat-align-tabs="start"
                        animationDuration="0ms" fitInkBarToContent class="h-full custom-tab-group">
                        <mat-tab label="Params">
                          <div class="tab-body-container h-full overflow-y-auto">
                            <div class="w-full flex gap-6 mb-3 sticky">
                              <div class="w-2/5">
                                <div>Key</div>
                              </div>
                              <div class="w-3/5">
                                <div>Value</div>
                              </div>
                            </div>
                            <div class="">
                              @for (param of parameterData; track param; let i = $index) {
                                <div class="flex gap-6 mb-3">
                                  <div class="w-2/5">
                                    <input
                                      class="w-full h-[40px] p-2 border border-gray-200 rounded-md focus:outline-none bg-transparent"
                                      type="text" [(ngModel)]="param.key" [ngModelOptions]="{standalone: true}"
                                      (ngModelChange)="onChangeParam($event)">
                                  </div>
                                  <div class="w-3/5 flex gap-6">
                                    <input
                                      class="w-full h-[40px] p-2 border border-gray-200 rounded-md focus:outline-none bg-transparent"
                                      type="text" [(ngModel)]="param.value" [ngModelOptions]="{standalone: true}"
                                      (ngModelChange)="onChangeParam($event)">
                                    <button id="btn-add-new-api-1" type="button" (click)="removeParam(i)"
                                      class="flex items-center x">
                                      <ng-icon name="heroXCircleSolid" class="text-2xl text-[#6F767E]" color="#6F767E"></ng-icon>
                                    </button>
                                  </div>
                                </div>
                              }
                            </div>
                            <button id="btn-add-new-param" type="button" (click)="addParam()"
                              class="text-light-primary flex items-center py-2">
                              <ng-icon name="heroPlusCircle" class="text-xl mr-2 "></ng-icon> Add params
                            </button>
                          </div>
                        </mat-tab>
                        <mat-tab label="Headers">
                          <div class="tab-body-container h-full overflow-y-auto">
                            <div class="w-full flex gap-6 mb-3 sticky">
                              <div class="w-2/5">
                                <div>Key</div>
                              </div>
                              <div class="w-3/5">
                                <div>Value</div>
                              </div>
                            </div>
                            <div class="">
                              @for (header of headerData; track header; let i = $index) {
                                <div class="flex gap-6 mb-3">
                                  <div class="w-2/5">
                                    <input
                                      class="w-full h-[40px] p-2 border border-gray-200 rounded-md focus:outline-none bg-transparent"
                                      type="text" [(ngModel)]="header.key" [ngModelOptions]="{standalone: true}">
                                  </div>
                                  <div class="w-3/5 flex gap-6">
                                    <input
                                      class="w-full h-[40px] p-2 border border-gray-200 rounded-md focus:outline-none bg-transparent"
                                      type="text" [(ngModel)]="header.value" [ngModelOptions]="{standalone: true}">
                                    <button type="button" (click)="removeHeader(i)" class="flex items-center x">
                                      <ng-icon name="heroXCircleSolid" class="text-2xl text-[#6F767E]"></ng-icon>
                                    </button>
                                  </div>
                                </div>
                              }
                            </div>
                            <button type="button" (click)="addHeader()"
                              class="text-light-primary flex items-center py-2">
                              <ng-icon name="heroPlusCircle" class="text-xl mr-2 "></ng-icon> Add headers
                            </button>
                          </div>
                        </mat-tab>
                        <mat-tab
                          [disabled]="formGroupApiDetail.get('method')?.value !== 'POST' && formGroupApiDetail.get('method')?.value !== 'PUT'"
                          label="Body">
                          <div class="tab-body-container-body">
                            <div class="editor-container h-full mt-4">
                              <ngx-monaco-editor class="h-full" [options]="editorOptions()" [(ngModel)]="body"
                              (ngModelChange)="onChangeBody($event)"></ngx-monaco-editor>
                            </div>
                          </div>
                        </mat-tab>
                        <mat-tab label="Scripts" class="h-full">
                          <div class="flex h-full">
                            <div class="w-36 border-r border-r-light-border-line dark:border-r-dark-border-line">
                              <div class="px-4 py-2 cursor-pointer text-sm"
                                [ngClass]="{'bg-light-hover dark:bg-dark-hover': selectedTab === 'pre-request'}"
                                (click)="selectTab('pre-request')">
                                Pre-request
                              </div>
                              <div class="px-4 py-2 cursor-pointer text-sm"
                                [ngClass]="{'bg-light-hover dark:bg-dark-hover': selectedTab === 'post-response'}"
                                (click)="selectTab('post-response')">
                                Post-response
                              </div>
                            </div>
                            <div style="width: calc(100% - 144px);">
                              @if (selectedTab === 'pre-request') {
                                <div class="h-full p-4">
                                  <div class="editor-container h-full mt-4">
                                    <ngx-monaco-editor class="h-full"
                                                       [options]="editorOptionsScripts"
                                                       [(ngModel)]="preRequest"
                                                       (ngModelChange)="onChangePreRequest($event)"></ngx-monaco-editor>
                                  </div>
                                </div>
                              }
                              @if (selectedTab === 'post-response') {
                                <div class="h-full p-4">
                                  <div class="editor-container h-full mt-4">
                                    <ngx-monaco-editor class="h-full" [options]="editorOptionsScripts"
                                      [(ngModel)]="postResponse"
                                    (ngModelChange)="onChangePostResponse($event)"></ngx-monaco-editor>
                                  </div>
                                </div>
                              }
                            </div>
                          </div>
                        </mat-tab>
                      </mat-tab-group>
                    </div>
                  </as-split-area>
                  <as-split-area size="50">
                    <div class="h-full w-full flex flex-col items-stretch p-4">
                      <div>Response</div>
                      <div class="editor-container mt-4">
                        <ngx-monaco-editor [options]="editorOptionsBody" [ngModel]="response"></ngx-monaco-editor>
                      </div>
                    </div>
                  </as-split-area>
                </as-split>
              </div>
            }
            @if (!apiSelected) {
              <div class="w-full h-full flex justify-center items-center">
                <div class="mt-16">Start adding your first API</div>
              </div>
            }
          </div>
        </div>
      </div>
    </div>
  </div>
}
@if (studioStatus() === STUDIO_STATUS.LIVE) {
  <div class="w-full h-full flex items-center justify-center">
    <div class="flex flex-col items-center justify-center space-y-3">
      <img src="assets/img/api-production.png" class="w-80 md:w-96" alt="api-production" height="1333" width="2000">
      <div>Adding new APIs is not allowed in production mode</div>
    </div>
  </div>
}
</div>

-->
