import {Ng<PERSON>tyle} from '@angular/common';
import {Component, inject, OnInit} from '@angular/core';
import {Form<PERSON>uilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {
  DIALOG_DATA,
  DxButton,
  DxDialog,
  DxDialogRef,
  DxError,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxSnackBar,
  DxTooltip
} from '@dx-ui/ui';
import {IFaq, IFile} from '@shared/models';
import {FaqService} from '@shared/services';
import {AddMoreSourceComponent} from '@views/faq/add-more-source/add-more-source.component';
import {cloneDeep} from 'lodash';
import {NgIcon, provideIcons} from '@ng-icons/core';
import {heroXMark} from '@ng-icons/heroicons/outline';
import {CustomValidators} from '@shared/validators';
import {AutosizeDirective} from '@shared/directives';

@Component({
  selector: 'app-add-or-edit-faq',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    NgStyle,
    NgIcon,
    DxLoadingButton,
    DxButton,
    DxLabel,
    DxFormField,
    DxError,
    DxInput,
    DxTooltip,
    AutosizeDirective
  ],
  providers: [provideIcons({heroXMark})],
  templateUrl: './add-or-edit-faq.component.html',
  styleUrl: './add-or-edit-faq.component.css',
  host: {
    class: 'h-full'
  }
})
export class AddOrEditFaqComponent implements OnInit {
  colors = [
    '#baf3db',
    '#f8e6a0',
    '#fedec8',
    '#ffd5d2',
    '#dfd8fd',
    '#4bce97',
    '#f5cd47',
    '#fea362',
    '#f87168',
    '#9f8fef',
    '#1f845a',
    '#946f00',
    '#c25100',
    '#c9372c',
    '#6e5dc6',
    '#cce0ff',
    '#c6edfb',
    '#d3f1a7',
    '#fdd0ec',
    '#dcdfe4',
    '#579dff',
    '#6cc3e0',
    '#94c748',
    '#e774bb',
    '#8590a2',
    '#0c66e4',
    '#227d9b',
    '#5b7f24',
    '#ae4787',
    '#626f86',
  ];
  isCreatingFAQ: boolean = false;
  knowledgeBaseSelectedDraft: number[] = [];
  knowledgeBaseSelected: number[] = [];

  faqForm: FormGroup = inject(FormBuilder).group({
    question: [null, [Validators.required,CustomValidators.noWhitespaceValidator]],
    response: [null, [Validators.required,CustomValidators.noWhitespaceValidator]],
    file_ids: [<number[]>[]],
  });

  dialog = inject(DxDialog);
  dialogRef = inject(DxDialogRef<AddOrEditFaqComponent>);
  data: {
    faq: {
      id: number;
      question: string;
      response: string;
      file_ids: number[];
    };
    knowledgeBaseSelected: number[];
    knowledgeBaseSelectedDraft: number[];
    listKnowledgeBase: IFile[];
    isEdit: boolean;
  } = inject(DIALOG_DATA);
  private snackBar = inject(DxSnackBar);
  private FAQService: FaqService = inject(FaqService);

  ngOnInit(): void {
    if (this.data.isEdit) {
      this.knowledgeBaseSelected = this.data.knowledgeBaseSelected;
      this.knowledgeBaseSelectedDraft = this.data.knowledgeBaseSelected;
      this.faqForm.patchValue({
        question: this.data.faq.question,
        response: this.data.faq.response,
        file_ids: this.data.faq.file_ids,
      });
    }
  }

  addSource() {
    this.knowledgeBaseSelectedDraft = cloneDeep(this.knowledgeBaseSelected);
    this.dialog
      .open(AddMoreSourceComponent, {
        data: {
          listKnowledgeBase: this.data.listKnowledgeBase,
          knowledgeBaseSelected: this.knowledgeBaseSelected,
          knowledgeBaseSelectedDraft: this.knowledgeBaseSelectedDraft,
        },
        width: '20vw',
        minWidth: '600px',
      })
      .afterClosed()
      .subscribe((result: any) => {
        if (result && result.length > 0) {
          this.knowledgeBaseSelectedDraft = result;
          this.knowledgeBaseSelected = result;
        }
      });
  }

  saveFAQ(data: number | undefined) {
    this.isCreatingFAQ = true;
    if (this.faqForm.valid) {
      this.faqForm.get('file_ids')?.setValue(this.knowledgeBaseSelected || []);
      const faqData: Partial<IFaq> = {
        id: data,
        question: this.faqForm.value.question,
        response: this.faqForm.value.response,
        faq_status: 'NOT_READY',
        files: this.faqForm.value.file_ids,
        file_ids: this.faqForm.value.file_ids,
      } as any;

      if (data) {
        this.FAQService.updateFAQ(data, faqData).subscribe({
          next: (res) => {
            this.showSnackBar('Update FAQ successful', 'success');
            this.isCreatingFAQ = false;
          },
          error: (err) => {
            this.showSnackBar('Update FAQ failed', 'error');
          },
          complete: () => {
            this.closeDialog();
          },
        });
      } else {
        this.FAQService.createFAQ(faqData).subscribe({
          next: (res) => {
            this.showSnackBar('create FAQ successful', 'success');
            this.isCreatingFAQ = false;
          },
          error: (err) => {
            this.showSnackBar('Create FAQ failed', 'error');
          },
          complete: () => {
            this.closeDialog();
          },
        });
      }
    }
  }

  closeDialog() {
    this.dialogRef.close();
  }

  knowledgeBaseTooltip(knowledgeBase: IFile | null): string {
    if (!knowledgeBase) return 'No information available';
    return `name: ${knowledgeBase.name}\nsource: ${
      knowledgeBase.file_path ?? knowledgeBase.url ?? 'N/A'
    }`;
  }

  getRandomColor(id: number): string {
    return this.colors[id % this.colors.length];
  }

  getTextColor(bgColor: string): string {
    // Convert from hex to RGB
    const r = parseInt(bgColor.slice(1, 3), 16);
    const g = parseInt(bgColor.slice(3, 5), 16);
    const b = parseInt(bgColor.slice(5, 7), 16);

    // Calculate luminance
    const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

    // Decide text color
    return luminance > 128 ? 'black' : 'white';
  }

  getElementById(id: number): IFile | null {
    const found = this.data.listKnowledgeBase.find((item) => item.id === id);
    return found ?? null; // Trả về tên hoặc null nếu không tìm thấy
  }

  private showSnackBar(message: string, type: 'success' | 'error') {
    if (type === 'success') {
      this.snackBar.open(message, '', {
        panelClass: 'dx-snack-bar-success',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }

    if (type === 'error') {
      this.snackBar.open(message, '', {
        panelClass: 'dx-snack-bar-error',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }
  }
}
