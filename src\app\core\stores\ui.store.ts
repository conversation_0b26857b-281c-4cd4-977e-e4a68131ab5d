import { patchState, signalStore, withMethods, withState } from '@ngrx/signals';
import { CommonUtils } from '@shared/utils';
import { IUIStore, Language, Theme } from './models';

const initialState: IUIStore = {
  theme:
    ((CommonUtils.isRemember() ? localStorage : sessionStorage).getItem(
      'theme'
    ) as Theme) || 'light',
  language: 'en',
  isHandset: false,
};

export const UIStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withMethods((store) => ({
    setTheme(theme: Theme): void {
      patchState(store, (state) => ({ ...state, theme }));
      const storage = CommonUtils.isRemember() ? localStorage : sessionStorage;
      if (theme === 'dark') {
        document.documentElement.classList.add('dark');
        storage.setItem('theme', 'dark');
      } else {
        document.documentElement.classList.remove('dark');
        storage.setItem('theme', 'light');
      }
    },
    setLanguage(language: Language): void {
      patchState(store, (state) => ({ ...state, language }));
    },
    setIsHandset(isHandset: boolean): void {
      patchState(store, (state) => ({ ...state, isHandset }));
    },
  }))
);
