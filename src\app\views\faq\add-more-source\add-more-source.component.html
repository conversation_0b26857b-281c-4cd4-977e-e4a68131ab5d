<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      Sources
    </div>
    <div class="flex items-center justify-end">
      <ng-icon
        name="heroXMark"
        class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></ng-icon>
    </div>
  </div>

  <div class="flex-1 overflow-y-auto mt-18 mb-20 px-6">
    <div class="flex flex-col gap-2 w-full py-2">
      @for (knowledgeBase of listKnowledgeBase; track knowledgeBase) {
        <div class="flex w-full">
          @if (knowledgeBase.id) {
            <div class="w-full" [ngStyle]="{
                'background-color': knowledgeBaseSelectedDraft.includes(
                  knowledgeBase.id
                )
                  ? getRandomColor(knowledgeBase.id)
                  : 'transparent',
                color: knowledgeBaseSelectedDraft.includes(knowledgeBase.id)
                  ? getTextColor(getRandomColor(knowledgeBase.id))
                  : 'inherit',
                'border-radius': knowledgeBaseSelectedDraft.includes(
                  knowledgeBase.id
                )
                  ? '0.375rem'
                  : '0',
                padding: knowledgeBaseSelectedDraft.includes(knowledgeBase.id)
                  ? '0.25rem 0.5rem'
                  : '0'
              }">
              <dx-checkbox labelPosition="after"
                           [checked]="knowledgeBaseSelectedDraft.includes(knowledgeBase.id)"
                           (change)="onKnowledgeBaseSelectionChange($event, knowledgeBase.id!)"
              >
                <dx-label
                  class="ml-2 w-full cursor-pointer text-neutral-content dark:text-dark-neutral-content">
                  <div
                    class="tag mx-1 cursor-pointer flex  w-full !text-start"
                    [dxTooltip]="knowledgeBaseTooltip(knowledgeBase)"
                    dxTooltipPosition="below"
                  >
                    {{ knowledgeBase.name }}
                  </div>
                </dx-label>
              </dx-checkbox>
            </div>
            <!--<div class="flex items-center w-full">
              <input
                type="checkbox"
                [id]="'source-dialog-' + knowledgeBase.id"
                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                [checked]="knowledgeBaseSelectedDraft.includes(knowledgeBase.id)"
                (change)="onKnowledgeBaseSelectionChange($event, knowledgeBase.id!)"
              />
              <label [for]="'source-dialog-' + knowledgeBase.id"
                     class="ml-2 w-full cursor-pointer text-neutral-content dark:text-dark-neutral-content">
                <div
                  class="tag mx-1 cursor-pointer flex  w-full !text-start"
                  [ngStyle]="{
                'max-width': 'calc(100% - 0px)',
                'background-color': knowledgeBaseSelectedDraft.includes(
                  knowledgeBase.id
                )
                  ? getRandomColor(knowledgeBase.id)
                  : 'transparent',
                color: knowledgeBaseSelectedDraft.includes(knowledgeBase.id)
                  ? getTextColor(getRandomColor(knowledgeBase.id))
                  : 'inherit',
                'border-radius': knowledgeBaseSelectedDraft.includes(
                  knowledgeBase.id
                )
                  ? '0.375rem'
                  : '0',
                padding: knowledgeBaseSelectedDraft.includes(knowledgeBase.id)
                  ? '0.25rem 0.5rem'
                  : '0'
              }"
                  [dxTooltip]="knowledgeBaseTooltip(knowledgeBase)"
                  dxTooltipPosition="below"
                >
                  {{ knowledgeBase.name }}
                </div>
              </label>
            </div>-->
          }
        </div>
      } @empty {
        <div class="no-tags-message text-light-text dark:text-dark-text">
          No source available at the moment.
        </div>
      }
    </div>


  </div>
  <!-- Actions -->
  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Close</button>
    <button
      dxButton="filled"
      [disabled]="knowledgeBaseSelectedDraft.length <= 0"
      (click)="saveMoreSource()"
    >
      Save
    </button>
  </div>
</div>
