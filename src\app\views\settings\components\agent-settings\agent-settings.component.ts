import { Platform } from '@angular/cdk/platform';
import { CommonModule } from '@angular/common';
import { Component, effect, inject, input } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import {
  DxFormField,
  DxInput,
  DxLabel,
  DxSlideToggle,
  DxSnackBar,
} from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { heroInformationCircle } from '@ng-icons/heroicons/outline';
import { SettingsService } from '@shared/services';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  Subject,
  takeUntil,
} from 'rxjs';

@Component({
  selector: 'app-agent-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DxFormField,
    DxLabel,
    DxInput,
    DxSlideToggle,
    NgIconsModule,
  ],
  templateUrl: './agent-settings.component.html',
  providers: [provideIcons({ heroInformationCircle })],
})
export class AgentSettingsComponent {
  settings = input<any>();

  private destroy$ = new Subject<void>();

  private platform = inject(Platform);
  private snackBar = inject(DxSnackBar);
  private settingsService = inject(SettingsService);

  // FORM CONTROL
  enabled = new FormControl<boolean>(false);
  description = new FormControl<string>('');
  role = new FormControl<string>('');
  rule = new FormControl<string>('');

  constructor() {
    effect(() => {
      const currentSettings = this.settings();
      if (currentSettings) {
        this.updateSettingsFormControls(currentSettings);
      }
    });
  }

  ngOnInit(): void {
    this.initializeFormSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  updateTextAreaHeight(id: string) {
    if (this.platform.isBrowser) {
      const textarea = document.getElementById(id);
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 32 + 'px';
      }
    }
  }

  private updateSettingsFormControls(settings: any) {
    this.enabled.setValue(settings?.settings?.basic?.use_agent?.enabled, {
      emitEvent: false,
    });
    this.description.setValue(
      settings?.settings?.agent_setting?.rag?.description,
      { emitEvent: false }
    );
    this.role.setValue(settings?.settings?.agent_setting?.rag?.role, {
      emitEvent: false,
    });
    this.rule.setValue(settings?.settings?.agent_setting?.rag?.rule, {
      emitEvent: false,
    });
  }

  private subscribeToFormControlWithHandler(
    control: FormControl,
    handler: (value: any) => void,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    let stream = control.valueChanges.pipe(
      takeUntil(this.destroy$),
      distinctUntilChanged(),
      debounceTime(300)
    );

    if (shouldFilter) {
      stream = stream.pipe(filter(shouldFilter));
    }

    stream.subscribe({ next: handler });
  }

  private subscribeToFormControlSettings(
    control: FormControl,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    this.subscribeToFormControlWithHandler(
      control,
      this.buildUpdatedSettings.bind(this),
      shouldFilter
    );
  }

  private initializeFormSubscriptions() {
    this.subscribeToFormControlSettings(this.enabled);
    this.subscribeToFormControlSettings(
      this.description,
      (value: string) => !!value
    );
    this.subscribeToFormControlSettings(this.role);
    this.subscribeToFormControlSettings(this.rule);
  }

  private buildUpdatedSettings(): void {
    const currentSettings = this.settings()?.settings;
    const updatedSettings = {
      ...currentSettings,
      basic: {
        ...currentSettings?.basic,
        use_agent: {
          ...currentSettings?.basic?.use_agent,
          enabled: this.enabled.value,
        },
      },
      agent_setting: {
        ...currentSettings?.agent_setting,
        rag: {
          ...currentSettings?.agent_setting?.rag,
          description: this.description.value,
          role: this.role.value,
          rule: this.rule.value,
        },
      },
    };
    this.updateSettings(updatedSettings);
  }

  private updateSettings(data: any) {
    this.settingsService.updateSetting(data).subscribe({
      next: (res) => {
        if (res) {
          this.snackBar.open(res.message, '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        }
      },
      error: (err) => {
        this.snackBar.open(err.error.detail, '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
    });
  }
}
