export const APP_PATH = {
  DASHBOARD: 'dashboard',
  STUDIO: 'studio',
  INBOX: 'inbox',
  KNOWLEDGE_BASE: 'knowledge-base',
  INTEGRATIONS: 'integrations',
  TAG: 'tag',
  LEADS: 'leads',
  FAQ: 'faq',
  SETTINGS: 'settings',
  SETTINGS_V2: 'settings-v2',
  ADMIN: 'admin',
  AUTH: 'auth',
  PREVIEW: 'preview',
  ERROR: 'error',
} as const;

export const AUTH_PATH = {
  BASE: 'auth',
  LOGIN: 'login',
  REGISTER: 'register',
  RESET_PASSWORD: 'reset-password',
  ACTIVE: 'activate',
} as const;

export const STUDIO_PATH = {
  BASE: 'studio',
  AGENT_FLOW: 'agent-flow',
  BASIC_FLOW: 'basic-flow',
  API: 'api',
  FUNCTION: 'function',
  EVENT: 'event',
} as const;

export const AGENT_FLOW_PATH = {
  TOOL: 'tool',
  AGENT_TOOL_MAPPING: 'assign-tools',
};

export const ADMIN_PATH = {
  BASE: 'admin',
  USER: 'user',
  AI: 'ai',
  PLAN: 'plan',
  PAYMENT_METHOD: 'payment-method',
  SETTINGS: 'settings',
} as const;

export const APP_ROUTES = {
  ...APP_PATH,
  ...AUTH_PATH,
  ...STUDIO_PATH,
  ...ADMIN_PATH,
} as const;
