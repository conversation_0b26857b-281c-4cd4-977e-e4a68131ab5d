<div
  class="h-screen max-w-screen flex items-center bg-base-200 dark:bg-dark-base-200 relative"
>
  <app-svg-icon
    [type]="uiStore.theme() === 'light' ? 'icLogo' : 'icLogoDark'"
    class="w-41 h-10 absolute top-[22px] left-3"
  ></app-svg-icon>
  <div class="w-full h-full flex items-center justify-center px-3 md:px-0">
    <div class="w-full md:w-2/3 lg:w-1/2">
      <div class="w-full h-full flex flex-col items-center justify-center">
        <div
          class="w-full text-center text-[40px] font-bold leading-13 text-base-content dark:text-base-content-dark"
        >
          Welcome to DxConnect
        </div>
        <div
          class="mt-3 w-full text-center text-[15px] font-medium text-neutral-content dark:text-neutral-content-dark"
        >
          Log in to manage and deploy chatbots across your platform
        </div>
      </div>
      <div
        class="mt-10 w-full h-full flex flex-col items-center justify-center"
      >
        <dx-form-field class="w-full">
          <dx-label>Email</dx-label>
          <input
            dxInput
            [(ngModel)]="username"
            type="email"
            name="email"
            placeholder="<EMAIL>"
          />
        </dx-form-field>
        <dx-form-field class="w-full">
          <dx-label>Password</dx-label>
          <input
            dxInput
            [(ngModel)]="password"
            [type]="showPassword() ? 'text' : 'password'"
            placeholder="Password"
          />
          <app-svg-icon
            dxSuffix
            [type]="!showPassword() ? 'icShow' : 'icHide'"
            class="!text-neutral-content dark:!text-neutral-content-dark w-6 h-6 cursor-pointer mr-3"
            (click)="togglePasswordVisibility()"
          >
          </app-svg-icon>
        </dx-form-field>
        <div class="w-full flex justify-between items-center">
          <div class="flex items-center space-x-2">
            <dx-checkbox labelPosition="after" [(ngModel)]="isRemember">
              <div
                class="text-neutral-content dark:text-dark-neutral-content text-[15px]"
              >
                Remember me
              </div>
            </dx-checkbox>
          </div>
          <div class="flex items-center space-x-2">
            <div
              class="text-primary text-[15px]"
              routerLink="/auth/forgot-password"
            >
              Forgot password?
            </div>
          </div>
        </div>
        <div class="mt-3 w-full flex justify-center items-center">
          <button dxButton (click)="login()" class="w-full">Sign In</button>
        </div>
        @if(deniedAccess()) {
        <div class="text-error text-[14px] font-medium dark:text-dark-error">
          Wrong account or password. Try again!
        </div>
        }
        <div
          class="mt-4 w-full text-left font-medium text-[14px] text-neutral-content dark:text-neutral-content-dark"
        >
          By Signing in, you agree to DxConnect
          <span
            class="underline underline-offset-2 text-primary dark:text-dark-primary cursor-pointer"
            >term of service privacy policy</span
          >
          and
          <span
            class="underline underline-offset-2 text-primary dark:text-dark-primary cursor-pointer"
            >data usage properties</span
          >.
        </div>
      </div>
    </div>
  </div>

  <div class="h-full w-fit hidden lg:block">
    <img
      src="../../../../../assets/img/new-login-pic.png"
      class="h-full w-300 object-cover"
      alt="login-pic"
    />
  </div>
</div>

<!--
<div class="bg-light-background h-screen max-w-screen flex justify-center items-center px-12">
  <div class="relative px-12 md:px-0 w-full md:w-[700px] xl:w-[1050px] h-[75vh] box-shadow-gradient flex">
    <span class="absolute hidden xl:block top-[59px] left-[53px]">
      <svg width="158" height="41">
        <use xlink:href="#logo_cx_gpt"></use>
      </svg>
    </span>
    <div class="h-full hidden xl:block">
      <img src="../../../../../assets/img/login-pic.png" class="h-full ">
    </div>
    <div class="flex justify-center items-center text-light-text w-full xl:w-2/3 ">
      <div class="w-[396px]">
        <div class="mb-10">
          &lt;!&ndash; <h1 class="text-5xl	font-semibold mb-3">Sign In</h1>
          <h5 class="text-xl font-normal">
            Don't have an account?
            <span class="cursor-pointer underline underline-offset-2 text-[#7241FF]">Sign
              up</span>
          </h5> &ndash;&gt;
          <h1 class="text-5xl	font-semibold">Sign In</h1>
        </div>
        <div>


          <form class="max-w-sm mx-auto">
            <div class="mb-5">
              <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                  <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 16">
                    <path
                      d="m10.036 8.278 9.258-7.79A1.979 1.979 0 0 0 18 0H2A1.987 1.987 0 0 0 .641.541l9.395 7.737Z" />
                    <path
                      d="M11.241 9.817c-.36.275-.801.425-1.255.427-.428 0-.845-.138-1.187-.395L0 2.6V14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2.5l-8.759 7.317Z" />
                  </svg>
                </div>
                <input type="text" id="username" name="username" [(ngModel)]="username"
                  [ngModelOptions]="{standalone: true}" (keyup.enter)="login()"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-light-primary focus:border-light-primary block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-light-primary dark:focus:border-light-primary"
                  placeholder="<EMAIL>" required>
              </div>
            </div>

            <div class="mb-5">
              <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                  <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd"
                      d="M8 10V7a4 4 0 1 1 8 0v3h1a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h1Zm2-3a2 2 0 1 1 4 0v3h-4V7Zm2 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1Z"
                      clip-rule="evenodd" />
                  </svg>
                </div>
                <input [type]="showPassword() ? 'text' : 'password'" id="password" name="password"
                  [(ngModel)]="password" [ngModelOptions]="{standalone: true}" (keyup.enter)="login()"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-light-primary focus:border-light-primary block w-full ps-10 p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-light-primary dark:focus:border-light-primary"
                  placeholder="••••••••" required>

                <div class="absolute inset-y-0 end-0 flex items-center pe-3">
                  @if(!showPassword()) {
                  <svg (click)="togglePasswordVisibility()" class="w-6 h-6 text-gray-800 dark:text-white cursor-pointer"
                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                    viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-width="2"
                      d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z" />
                    <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                  </svg>
                  } @else {
                  <svg (click)="togglePasswordVisibility()" class="w-6 h-6 text-gray-800 dark:text-white cursor-pointer"
                    aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      d="m4 15.6 3.055-3.056A4.913 4.913 0 0 1 7 12.012a5.006 5.006 0 0 1 5-5c.178.009.356.027.532.054l1.744-1.744A8.973 8.973 0 0 0 12 5.012c-5.388 0-10 5.336-10 7A6.49 6.49 0 0 0 4 15.6Z" />
                    <path
                      d="m14.7 10.726 4.995-5.007A.998.998 0 0 0 18.99 4a1 1 0 0 0-.71.305l-4.995 5.007a2.98 2.98 0 0 0-.588-.21l-.035-.01a2.981 2.981 0 0 0-3.584 3.583c0 .012.008.022.01.033.05.204.12.402.211.59l-4.995 4.983a1 1 0 1 0 1.414 1.414l4.995-4.983c.189.091.386.162.59.211.011 0 .021.007.033.01a2.982 2.982 0 0 0 3.584-3.584c0-.012-.008-.023-.011-.035a3.05 3.05 0 0 0-.21-.588Z" />
                    <path
                      d="m19.821 8.605-2.857 2.857a4.952 4.952 0 0 1-5.514 5.514l-1.785 1.785c.767.166 1.55.25 2.335.251 6.453 0 10-5.258 10-7 0-1.166-1.637-2.874-2.179-3.407Z" />
                  </svg>
                  }
                </div>
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-start">
                <div class="flex items-center h-5">
                  <input id="remember" name="remember" [(ngModel)]="isRemember" aria-describedby="remember"
                    type="checkbox"
                    class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800"
                    required>
                </div>
                <div class="ml-3 text-sm">
                  <label for="remember" class="text-gray-500 dark:text-gray-300">Remember me</label>
                </div>
              </div>
              <div class="text-sm font-medium text-light-primary cursor-pointer hover:underline"
                routerLink="/auth/forget-password">
                Forgot
                password?</div>
            </div>

            <button type="submit" (click)="login()"
              class="mt-5 w-full focus:outline-none text-white bg-light-primary cursor-pointer hover:bg-light-primary-hover focus:ring-4 focus:ring-purple-300 font-medium rounded-lg text-sm px-5 py-2.5 mb-2 dark:bg-purple-600 dark:hover:bg-purple-700 dark:focus:ring-purple-900">Sign
              in</button>
          </form>

          @if(deniedAccess()) {
          <div class="text-red-600">Wrong account or password. Try again!</div>
          }

          <p id="helper-text-explanation" class="mt-2 text-xs text-gray-500 dark:text-gray-400">By Signing in, you agree
            to queryGPT <a href="#" class="font-medium text-light-primary hover:underline">term of service
              privacy
              policy</a> and <a href="#" class="font-medium text-light-primary hover:underline">data usage
              properties</a>.</p>
        </div>
      </div>
    </div>
  </div>
</div>-->
