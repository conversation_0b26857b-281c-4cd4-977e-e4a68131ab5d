/* Conversation list container styles */
.list-conversation {
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  height: calc(100vh - 236px);
}

@media screen and (max-width: 768px) {
  .list-conversation {
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
    transform: translateX(-130%);
    opacity: 0;
    position: absolute;
    z-index: 2 !important;
    background-color: var(--color-light-hover) !important;
    width: 100%;
    resize: vertical;
  }

  .dark .list-conversation {
    background-color: var(--color-dark-hover) !important;
  }

  .list-conversation.active {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Conversation item styles */
.list-conversation .conversation-item {
  padding: 16px;
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s ease;
}

.dark .list-conversation .conversation-item {
  border-bottom-color: var(--color-dark-primary-border);
}

::ng-deep .list-conversation .conversation-item.selected,
::ng-deep .list-conversation .conversation-item:hover {
  background-color: var(--color-base-400-hover);
}

::ng-deep .dark .list-conversation .conversation-item.selected,
::ng-deep .dark .list-conversation .conversation-item:hover {
  background-color: var(--color-dark-base-400-hover);
}

/* Platform icon styles */
.conversation-item .platform-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.conversation-item .platform-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

/* Conversation name and edit input styles */
.conversation-item .conversation-name {
  font-weight: 500;
  color: var(--color-light-text);
  max-width: 154px;
}

.dark .conversation-item .conversation-name {
  color: var(--color-dark-text);
}

.conversation-item .name-edit-input {
  max-width: 12rem;
  padding: 6px;
  border: 1px solid var(--color-light-border-line);
  border-radius: 4px;
  background-color: var(--color-light-background);
  color: var(--color-light-text);
}

.dark .conversation-item .name-edit-input {
  border-color: var(--color-dark-border-line);
  background-color: var(--color-dark-background);
  color: var(--color-dark-text);
}

/* Action buttons styles */
.conversation-item .action-button {
  padding: 4px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.conversation-item .action-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark .conversation-item .action-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Last message timestamp styles */
.conversation-item .last-message-time {
  font-size: 10px;
  color: var(--color-light-text-secondary);
  line-height: 1;
}

.dark .conversation-item .last-message-time {
  color: var(--color-dark-text-secondary);
}

/* Assignment status styles */
.conversation-item .assignment-badge {
  padding: 2px 8px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
}

.conversation-item .assignment-badge.assigned {
  background-color: var(--color-light-green);
  color: var(--color-light-text);
}

.conversation-item .assignment-badge.unassigned {
  background-color: var(--color-light-orange);
  color: var(--color-light-text);
}

.dark .conversation-item .assignment-badge {
  color: var(--color-dark-text);
}

/* Tag styles */
.listTag {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}


.listTag .tag {
  display: inline-block;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: background-color 0.2s ease;
}

.listTag .tag-mini {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 10px;
  line-height: 1.3;
  border-radius: 99px;
  min-height: 22px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.listTag .tag-hover:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark .listTag .tag-hover:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Block placeholder for tags */
.block-placeholder {
  width: 21px;
  height: 5px;
  border-radius: 99px;
}

/* Unread message indicator */
.conversation-item .unread-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 8px;
  height: 8px;
  background-color: var(--color-light-primary);
  border-radius: 50%;
}

/* Control buttons positioned absolutely */
.conversation-item .control-buttons {
  position: absolute;
  top: 16px;
  right: 16px;
}

.conversation-item .control-button {
  padding: 4px;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.conversation-item .control-button:hover {
  background-color: #ccc;
}

.dark .conversation-item .control-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Loading spinner container */
.loading-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Responsive adjustments */
@media screen and (max-width: 1200px) {
  .list-conversation {
    min-width: 280px;
  }
}

/* Tag checkbox styles */
::ng-deep .tag-checkbox .mdc-form-field {
  align-items: center;
}

::ng-deep .tag-checkbox .mdc-form-field .mdc-label {
  color: var(--color-light-text);
}

::ng-deep .tag-checkbox .mdc-form-field .mdc-checkbox {
  padding: 4px;
}

::ng-deep .tag-checkbox .mdc-form-field .mdc-checkbox .mat-mdc-checkbox-touch-target {
  width: 24px;
  height: 24px;
}

::ng-deep .tag-checkbox .mdc-form-field .mdc-checkbox .mdc-checkbox__native-control {
  width: 16px;
  height: 16px;
}

::ng-deep .tag-checkbox .mdc-form-field .mdc-checkbox .mdc-checkbox__background {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}
