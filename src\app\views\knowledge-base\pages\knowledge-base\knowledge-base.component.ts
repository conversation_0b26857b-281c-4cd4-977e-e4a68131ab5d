import { SelectionModel } from '@angular/cdk/collections';
import { OverlayModule } from '@angular/cdk/overlay';
import {
  CdkVirtualScrollViewport,
  ScrollingModule,
} from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  CUSTOM_ELEMENTS_SCHEMA,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  QueryList,
  Renderer2,
  ViewChild,
  ViewChildren,
  inject,
  signal,
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioButton, MatRadioGroup } from '@angular/material/radio';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute, Router } from '@angular/router';
import { UserAiStore } from '@core/stores';
import { DxCard, DxCardContent, DxDialog, DxTooltip } from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  faSolidCopy,
  faSolidFile,
  faSolidFileCsv,
  faSolidFileLines,
  faSolidFilePen,
  faSolidFolder,
  faSolidFolderClosed,
  faSolidLink,
} from '@ng-icons/font-awesome/solid';
import {
  heroArrowDown,
  heroArrowDownOnSquare,
  heroArrowPath,
  heroArrowUp,
  heroCheckCircle,
  heroChevronRight,
  heroEllipsisHorizontal,
  heroEllipsisVertical,
  heroFolder,
  heroFolderPlus,
  heroInformationCircle,
  heroPencilSquare,
  heroTrash,
  heroUserPlus,
  heroXMark,
} from '@ng-icons/heroicons/outline';
import {
  BaseComponent,
  ConfirmDialogComponent,
  CustomIconComponent,
  ErrorMessageComponent,
  LoadingButtonComponent,
  SelectComponent,
} from '@shared/components';
import {
  ClickOutsideDirective,
  HasPermissionDirective,
  MatContextMenuTrigger,
  TrimStringDirective,
} from '@shared/directives';
import {
  IFile,
  IFolder,
  IFolderFilter,
  IIntentDev,
  IViewFile,
} from '@shared/models';
import { CharFirstPipe } from '@shared/pipes';
import {
  CollectionsService,
  FileFolderService,
  KnowledgeBaseService,
  SettingsService,
  SocketService,
} from '@shared/services';
import { cloneDeep } from 'lodash';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-knowledge-base',
  standalone: true,
  imports: [
    MatButtonToggleModule,
    MatChipsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatMenuModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ScrollingModule,
    OverlayModule,
    LoadingButtonComponent,
    NgIconsModule,
    CharFirstPipe,
    HasPermissionDirective,
    MatContextMenuTrigger,
    SelectComponent,
    ClickOutsideDirective,
    MatRadioGroup,
    MatRadioButton,
    ErrorMessageComponent,
    CustomIconComponent,
    TrimStringDirective,
    DxCard,
    DxCardContent,
    DxTooltip,
  ],
  providers: [
    provideIcons({
      faSolidFileLines,
      faSolidFileCsv,
      faSolidFilePen,
      faSolidLink,
      faSolidFile,
      faSolidFolder,
      faSolidFolderClosed,
      faSolidCopy,
      heroArrowDown,
      heroArrowUp,
      heroInformationCircle,
      heroChevronRight,
      heroXMark,
      heroTrash,
      heroPencilSquare,
      heroFolder,
      heroFolderPlus,
      heroArrowDownOnSquare,
      heroEllipsisVertical,
      heroEllipsisHorizontal,
      heroArrowPath,
      heroUserPlus,
      heroCheckCircle,
    }),
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './knowledge-base.component.html',
  styleUrl: './knowledge-base.component.css',
  styles: [
    `
      ::ng-deep .cdk-virtual-scroll-viewport {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
      }

      ::ng-deep .cdk-virtual-scroll-content-wrapper {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
      }
    `,
  ],
})
export class KnowledgeBaseComponent
  extends BaseComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  @ViewChild('createFolderDialog') createFolderDialog: any;
  @ViewChild('renameFileDialog') renameFileDialog: any;
  @ViewChild('assignPermissionFolderDialog') assignPermissionFolderDialog: any;
  @ViewChild('upFileDialog') upFileDialog: any;
  @ViewChild('importUrlDialog') importUrlDialog: any;
  @ViewChild('viewContentDialog') viewContentDialog: any;
  @ViewChild('viewFileInfoDialog') viewFileInfoDialog: any;
  @ViewChild('contentDiv', { static: false }) contentDiv!: ElementRef;
  @ViewChild('editTextarea', { static: false }) editTextarea!: ElementRef;
  @ViewChildren(CdkVirtualScrollViewport)
  virtualScrollViewports!: QueryList<CdkVirtualScrollViewport>;
  @ViewChild('folderViewport', { static: false })
  folderViewport!: CdkVirtualScrollViewport;
  @ViewChild('fileViewport', { static: false })
  fileViewport!: CdkVirtualScrollViewport;
  @ViewChild('listViewport', { static: false })
  listViewport!: CdkVirtualScrollViewport;
  @ViewChild('moveFileDialog') moveFileDialog: any;
  listFileType = [
    { label: 'all', value: '' },
    { label: 'PDF', value: 'PDF' },
    { label: 'CSV', value: 'CSV' },
    { label: 'TXT', value: 'TXT' },
    { label: 'URL', value: 'URL' },
    { label: 'Markdown', value: 'MD' },
  ];
  listStatus = [
    { label: 'all', value: '' },
    { label: 'Pending', value: 'PENDING' },
    { label: 'In progress', value: 'IN_PROGRESS' },
    { label: 'Completed', value: 'COMPLETED' },
  ];
  listPermissions = [
    { label: 'Read only', value: 1 },
    { label: 'Write', value: 2 },
    { label: 'Delete', value: 4 },
  ];
  override searchModel: IFolderFilter = {
    id: null,
    name: '',
    file_type: null,
    file_status: null,
    sort_by: 'created_at',
    order: 'DESC' as 'ASC' | 'DESC',
    page: 1,
    limit: 20,
  };

  searchTimeout: any = null;
  isLoadingMore: boolean = false;
  hasMoreData: boolean = true;
  folderSelection = new SelectionModel<any>(true, []);
  fileSelection = new SelectionModel<any>(true, []);
  userSelection = new SelectionModel<any>(true, []);
  isShiftPressed: boolean = false;
  isCtrlPressed: boolean = false;

  roleUserInAI: any = '';
  parentId: number | null = null;
  folderList: IFolder[] = [];
  fileList: any[] = [];
  combinedList: any[] = [];
  permissions: number = 0;
  parent_folder_permissions: number = 0;
  listUserInAI: Array<{ email: string; id: number }> = [];
  listUserInAIAllowed: Array<{ email: string; id: number }> = [];

  collectionData: any;
  collectionOfFile: any;
  fileUpload: any;
  documentName: any;
  fileURL: any;
  metadata: any;
  rootURL: string = '';
  modeGetUrl: string = 'getOne';
  breadcrumbs: IFolder[] = [];

  isCreatingFolder = false;
  isImportingFile = false;
  isImportingUrl = false;
  isSavingPermission = false;

  createFolderForm!: FormGroup;
  uploadFileForm!: FormGroup;
  assignFolderForm!: FormGroup;

  timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  hideSingleSelectionIndicator = signal(false);

  private subscription: Subscription = new Subscription();

  private renderer2: Renderer2 = inject(Renderer2);
  private fileFolderService: FileFolderService = inject(FileFolderService);
  private KnowledgeBaseService: KnowledgeBaseService =
    inject(KnowledgeBaseService);
  private collectionsService: CollectionsService = inject(CollectionsService);
  private settingsService: SettingsService = inject(SettingsService);
  private socketService: SocketService = inject(SocketService);
  override fb: FormBuilder = inject(FormBuilder);
  private userAiStore = inject(UserAiStore);

  nameOrder: string = 'DESC';
  updateOrder: string = 'DESC';
  createdOrder: string = 'DESC';
  private _editMode: boolean = false;
  draftContent: any;
  protected selectedFile: any;
  private openRetrainDialog: boolean = false;
  fileRename: any;
  searchingMode: boolean = false;
  foldersCanMoveTo: any;
  loadingMoveDialog: boolean = false;
  private fileNeedMove: any;
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private dialogService = inject(DxDialog);

  constructor() {
    super();
  }

  ngOnInit(): void {
    this.roleUserInAI = this.userAiStore.currentAi()?.role;

    this.createFolderForm = this.fb.group({
      id: [null],
      ai_id: [null],
      parent_id: [null],
      name: [null, [Validators.required]],
    });

    this.uploadFileForm = this.fb.group({
      timeKeepImport: [],
      file: [],
    });

    this.assignFolderForm = this.fb.group({
      folder_ids: [[], [Validators.required]],
      assign_permissions: this.fb.array([], Validators.required),
    });

    this.createFolderForm.get('name')?.setValidators([Validators.required]);
    this.createFolderForm.get('name')?.updateValueAndValidity();

    this.getListUserInAi();
    this.getCollections();
    this.listenFileUpdateSocket();

    let isDirectNavigation = false;
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        const numericId = parseInt(id, 10);
        const folder: IFolder = {
          id: numericId,
          ai_id: null as any,
          parent_id: null,
          name: null as any,
          users: [],
          isDeleting: null as any,
        };
        this.toFolder(folder, 'auto');
        this.fileFolderService.getBreadCrumb({ id: numericId }).subscribe({
          next: (res) => {
            this.breadcrumbs = res.reverse();
          },
          error: (err) => {
            this.breadcrumbs = [];
          },
        });
      } else {
        this.viewFolder({
          id: null,
          name: '',
          file_type: null,
          file_status: null,
          sort_by: 'updated_at',
          order: 'DESC',
        });
      }
    });
  }

  ngAfterViewInit(): void {
    this.listenKeyboardEvent();
    this.listenMouseEvent();
  }

  public refreshVirtualScrollViewports(): void {
    if (this.showStyle === 'grid') {
      this.refreshGridViewports();
    } else if (this.showStyle === 'list') {
      this.refreshListViewport();
    }

    this.cdr.detectChanges();
  }

  private refreshGridViewports(): void {
    if (this.folderViewport) {
      this.folderViewport.checkViewportSize();
      this.folderViewport.scrollToIndex(0);
    }

    if (this.fileViewport) {
      this.fileViewport.checkViewportSize();
      this.fileViewport.scrollToIndex(0);
    }
  }

  private refreshListViewport(): void {
    if (this.listViewport) {
      this.listViewport.checkViewportSize();
      this.listViewport.scrollToIndex(0);
    }
  }

  onSearchChange(value: string): void {
    this.searchModel.name = value.trim();
    this.searchingMode = value.trim().length > 0;
    this.searchModel.page = 1;
    this.hasMoreData = true;

    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    this.searchTimeout = setTimeout(() => {
      if (this.searchingMode) {
        this.searchFeature();
      } else {
        this.viewFolder({ ...this.searchModel, id: this.parentId });
      }
    }, 300);
  }

  onInputChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.searchModel.name = input.value;
    this.searchingMode = input.value.trim().length > 0;
    this.searchModel.page = 1;
    this.hasMoreData = true;

    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    this.searchTimeout = setTimeout(() => {
      if (this.searchingMode) {
        this.searchFeature();
      } else {
        this.viewFolder({ ...this.searchModel, id: this.parentId });
      }
    }, 300);
  }

  applyFilter() {
    this.searchModel.page = 1;
    this.hasMoreData = true;

    this.searchingMode = !!(
      this.searchModel.name?.trim() ||
      this.searchModel.file_type ||
      this.searchModel.file_status
    );

    if (this.searchingMode) {
      this.searchFeature();
    } else {
      this.viewFolder({ ...this.searchModel, id: this.parentId });
    }
  }

  onFolderScrollIndexChange(index: number): void {
    // No longer needed with full virtual scrolling
  }

  onFileScrollIndexChange(index: number): void {
    // No longer needed with full virtual scrolling
  }

  onListScrollIndexChange(index: number): void {
    // No longer needed with full virtual scrolling
  }

  onFileTypeChange(value: any) {
    this.searchModel.file_type = value === '' ? null : value;
    this.applyFilter();
  }

  onFileStatusChange(value: any) {
    this.searchModel.file_status = value === '' ? null : value;
    this.applyFilter();
  }

  prepareFolder(folder: IFolder): IFolder {
    const folderWithFlag = { ...folder };
    (folderWithFlag as any).isFolder = true;
    return folderWithFlag;
  }

  private isNavigatingToFolder = false;

  toFolder(
    folder: IFolder | null | undefined,
    auto?: string,
    type?: string,
    data?: any
  ): void {
    if (folder && folder.id && this.parentId === folder.id) {
      if (type === 'folder' || data) {
        setTimeout(() => {
          if (type === 'folder') {
            this.selectFolder(data);
          } else if (data) {
            this.selectFile(data);
          }
        }, 300);
      }
      return;
    }

    if (this.isNavigatingToFolder) {
      return;
    }

    this.isNavigatingToFolder = true;

    const currentViewStyle = this.showStyle;

    this.folderSelection.clear();
    this.fileSelection.clear();

    this.searchingMode = false;

    this.searchModel.page = 1;
    this.hasMoreData = true;

    this.folderList = [];
    this.fileList = [];
    this.combinedList = [];

    try {
      if (folder && folder.id) {
        this.parentId = folder.id;

        const newSearchModel = {
          id: folder.id,
          name: this.searchModel.name || '',
          file_type: this.searchModel.file_type || null,
          file_status: this.searchModel.file_status || null,
          sort_by: this.searchModel.sort_by || 'created_at',
          order: this.searchModel.order || 'DESC',
          page: 1,
          limit: this.searchModel.limit || 20,
        };

        this.updateBreadcrumb(folder);

        if (!auto) {
          this.router.navigate(['/knowledge-base', folder.id]);
        }

        this.viewFolder(newSearchModel);
      } else {
        this.parentId = null;

        const newSearchModel = {
          id: null,
          name: this.searchModel.name || '',
          file_type: this.searchModel.file_type || null,
          file_status: this.searchModel.file_status || null,
          sort_by: this.searchModel.sort_by || 'created_at',
          order: this.searchModel.order || 'DESC',
          page: 1,
          limit: this.searchModel.limit || 20,
        };

        this.updateBreadcrumb(null);

        if (!auto) {
          this.router.navigate(['/knowledge-base']);
        }

        this.viewFolder(newSearchModel);
      }

      if (this.showStyle !== currentViewStyle) {
        this.showStyle = currentViewStyle;

        setTimeout(() => {
          this.refreshVirtualScrollViewports();
        }, 300);
      }

      setTimeout(() => {
        if (type == 'folder') {
          this.selectFolder(data);
        } else if (data) {
          this.selectFile(data);
        }
      }, 1000);
    } finally {
      setTimeout(() => {
        this.isNavigatingToFolder = false;
      }, 1500);
    }
  }

  viewContentFile(file: IFile | undefined): void {
    this.folderSelection.clear();
    if (file && file.id) {
      this.selectedFile = file;
      this.openRetrainDialog = true;
      this.showDialog(this.viewContentDialog, {
        data: {
          content: file.text_content ? file.text_content : 'No content',
        },
        width: '60vw',
        maxHeight: '80vh',
      });
    }
  }

  viewInfoFile(file: IFile | undefined): void {
    this.folderSelection.clear();
    if (file && file.id) {
      this.selectedFile = file;
      this.isEditing = false;
      this.showDialog(this.viewFileInfoDialog, {
        data: {
          id: file.id,
          name: file.name ? file.name : '',
          url: file.url ? file.url : file.file_path,
          created_at: file.created_at ? new Date(file.created_at + 'Z') : '',
          updated_at: file.updated_at ? new Date(file.updated_at + 'Z') : '',
        },
        width: '30vw',
        maxHeight: '80vh',
      });
    }
  }

  selectItem<T extends IFolder | IFile>(
    item: T | null,
    selection: SelectionModel<T>,
    itemList: T[]
  ) {
    if (!item) {
      selection.clear();
      return;
    }
    if (this.isCtrlPressed) {
      selection.isSelected(item)
        ? selection.deselect(item)
        : selection.select(item);
      return;
    }
    if (this.isShiftPressed) {
      this.handleShiftSelection(item, selection, itemList);
      return;
    }
    if (!selection.isSelected(item) || selection.selected.length > 1) {
      selection.clear();
      selection.select(item);
    }
  }

  handleShiftSelection<T extends IFolder | IFile>(
    item: T,
    selection: SelectionModel<T>,
    itemList: T[]
  ) {
    if (selection.selected.length === 0) {
      selection.select(item);
      return;
    }
    const lastSelectedIndex = itemList.findIndex(
      (i) => i.id === selection.selected[selection.selected.length - 1].id
    );
    const currentIndex = itemList.findIndex((i) => i.id === item.id);
    if (lastSelectedIndex === -1 || currentIndex === -1) {
      selection.select(item);
      return;
    }
    const start = Math.min(lastSelectedIndex, currentIndex);
    const end = Math.max(lastSelectedIndex, currentIndex);
    for (let i = start; i <= end; i++) {
      selection.select(itemList[i]);
    }
  }

  handleShiftSelectionCombined(item: any) {
    const isItemFolder = this.isFolder(item);
    const selection = isItemFolder ? this.folderSelection : this.fileSelection;
    if (selection.selected.length === 0) {
      selection.select(item);
      return;
    }
    const filteredList = this.combinedList.filter(
      (i) => this.isFolder(i) === isItemFolder
    );
    const lastSelectedItem = selection.selected[selection.selected.length - 1];
    const lastSelectedIndex = filteredList.findIndex(
      (i) => i.id === lastSelectedItem.id
    );
    const currentIndex = filteredList.findIndex((i) => i.id === item.id);
    if (lastSelectedIndex === -1 || currentIndex === -1) {
      selection.select(item);
      return;
    }
    const start = Math.min(lastSelectedIndex, currentIndex);
    const end = Math.max(lastSelectedIndex, currentIndex);
    for (let i = start; i <= end; i++) {
      selection.select(filteredList[i]);
    }
  }

  selectFolder(item: IFolder | null) {
    if (item === null) {
      this.folderSelection.clear();
      return;
    }
    if (this.isShiftPressed) {
      if (this.showStyle === 'list') {
        this.handleShiftSelectionCombined(item);
      } else {
        this.handleShiftSelection(item, this.folderSelection, this.folderList);
      }
    } else if (this.isCtrlPressed) {
      this.selectItem(item, this.folderSelection, this.folderList);
    } else {
      this.fileSelection.clear();
      this.folderSelection.clear();
      this.folderSelection.select(item);
    }
  }

  selectFile(item: IFile | null) {
    if (item === null) {
      this.fileSelection.clear();
      return;
    }
    if (this.isShiftPressed) {
      if (this.showStyle === 'list') {
        this.handleShiftSelectionCombined(item);
      } else {
        this.handleShiftSelection(item, this.fileSelection, this.fileList);
      }
    } else if (this.isCtrlPressed) {
      this.selectItem(item, this.fileSelection, this.fileList);
    } else {
      this.folderSelection.clear();
      this.fileSelection.clear();
      this.fileSelection.select(item);
    }
  }

  openCreateFolderDialog(folder?: IFolder) {
    let isCreate: boolean = true;
    this.createFolderForm.patchValue({
      id: null,
      ai_id: '',
      parent_id: this.parentId,
      name: null,
    });
    if (folder) {
      isCreate = false;
      this.createFolderForm.patchValue({
        id: folder.id,
        ai_id: folder.ai_id,
        parent_id: folder.parent_id,
        name: folder.name,
      });
    }
    this.showDialog(this.createFolderDialog, {
      data: {
        isCreate: isCreate,
      },
      width: '25vw',
    });
  }

  openRenameFileDialog(file: IFile) {
    this.fileRename = {
      file_id: file.id,
      new_name: file.name,
    };
    this.showDialog(this.renameFileDialog, {
      data: {},
      width: '25vw',
    });
  }

  onKeyDownFolderDialog(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      this.saveCreatingFolder();
    }
  }

  saveCreatingFolder() {
    const body: IIntentDev = {
      ...this.createFolderForm.value,
    };
    if (body.id) {
      this.isCreatingFolder = true;
      this.fileFolderService.updateFolder(body).subscribe({
        next: () => {
          this.isCreatingFolder = false;
          this.snackBar.open('Update successfully', '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
          this.viewFolder({ ...this.searchModel, id: this.parentId });
          this.closeDialog();
        },
        error: () => {
          this.isCreatingFolder = false;
          this.closeDialog();
        },
      });
    } else {
      this.isCreatingFolder = true;
      this.fileFolderService.createFolder(body).subscribe({
        next: () => {
          this.isCreatingFolder = false;
          this.snackBar.open('Create successfully', '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
          this.viewFolder({ ...this.searchModel, id: this.parentId });
          this.closeDialog();
        },
        error: () => {
          this.isCreatingFolder = false;
          this.closeDialog();
        },
      });
    }
  }

  deleteFolder(folder: IFolder) {
    this.dialogService
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Delete this folder',
          content: 'Are you sure delete this folder?',
          isDelete: true,
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (value && folder && folder.id) {
          this.clearAllSelectedFolderAndFile();
          this.confirmDeleteFolder(folder.id);
        }
      });
  }

  deleteAllFolder() {
    if (this.folderSelection.selected.length > 0) {
      const body = {
        folder_ids: this.folderSelection.selected.map(
          (item) => item.id
        ) as number[],
      };
      this.folderList.forEach((file) => {
        if (
          this.folderSelection.selected.map((item) => item.id).includes(file.id)
        ) {
          file.isDeleting = true;
        }
      });
      this.folderList = cloneDeep(this.folderList);
      this.folderSelection.clear();
      this.fileFolderService.deleteAllFolder(body).subscribe({
        next: (res) => {
          this.snackBar.open('Delete successfully', '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
          this.viewFolder({ ...this.searchModel, id: this.parentId });
          this.clearAllSelectedFolderAndFile();
        },
        error: (error) => {
          this.snackBar.open(error.error.detail, '', {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        },
      });
    }
  }

  confirmDeleteFolder(id: number) {
    const index = this.folderList.findIndex((item) => item.id === id);
    this.folderList[index] = {
      ...this.folderList[index],
      isDeleting: true,
    };
    this.folderList = cloneDeep(this.folderList);
    this.fileFolderService.deleteFolder(id).subscribe(
      (res) => {
        this.snackBar.open('Delete successfully', '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        this.viewFolder({ ...this.searchModel, id: this.parentId });
      },
      (error) => {
        this.folderList[index] = {
          ...this.folderList[index],
          isDeleting: false,
        };
        this.folderList = cloneDeep(this.folderList);
      }
    );
  }

  showImportFileDialog() {
    this.collectionOfFile = this.collectionData[0].id;
    this.showDialog(this.upFileDialog, {
      data: {},
      width: '30vw',
    });
  }

  uploadFile(event: any): void {
    const reader = new FileReader();
    const file = event.target.files[0];
    if (file) {
      const allowedFileTypes = ['csv', 'pdf', 'txt', 'md', 'docx'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (allowedFileTypes.includes(fileExtension)) {
        reader.readAsDataURL(file);
        this.fileUpload = file;
        this.documentName = file.name;
        reader.onload = () => {
          this.fileURL = reader.result;
        };
      } else {
        this.snackBar.open(
          'Invalid file format. Please select a CSV, PDF, TXT or Markdown file.',
          '',
          {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          }
        );
      }
    }
  }

  saveFileUpload(template: any) {
    const formData = new FormData();
    formData.append('file', this.fileUpload);
    const params: any = {
      metadata_columns:
        this.checkFileCsv(this.documentName) &&
        this.metadata &&
        this.metadata.trim() !== ''
          ? this.metadata.trim()
          : '',
    };
    if (this.parentId) {
      params.folder_id = this.parentId;
    }
    const dialogLoadingCallApi = this.showDialog(template, {
      data: {},
      maxWidth: 'fit-content',
    });
    this.isImportingFile = true;
    this.KnowledgeBaseService.createFAQ(formData, params).subscribe(
      (res) => {
        dialogLoadingCallApi.close();
        this.viewFolder({ ...this.searchModel, id: this.parentId });
        this.snackBar.open('Upload file successfully', '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        this.isImportingFile = false;
        this.closeDialog();
      },
      (error) => {
        dialogLoadingCallApi.close();
        this.snackBar.open(error.error.detail, '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        this.isImportingFile = false;
      }
    );
  }

  showImportUrlDialog() {
    this.collectionOfFile = this.collectionData[0].id;
    this.showDialog(this.importUrlDialog, {
      data: {},
      width: '40vw',
      minWidth: '350px',
    });
  }

  importUrl() {
    this.isImportingUrl = true;
    this.KnowledgeBaseService.importUrl(
      this.rootURL,
      this.collectionOfFile,
      this.modeGetUrl,
      this.parentId || undefined
    ).subscribe({
      next: (res) => {
        this.snackBar.open(res.message, '', {
          panelClass: 'dx-snack-bar-info',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        this.viewFolder({ ...this.searchModel, id: this.parentId });
        this.isImportingUrl = false;
        this.closeDialog();
      },
      error: (error) => {
        this.snackBar.open('Failed to import data from url', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        this.isImportingUrl = false;
      },
    });
  }

  deleteFile(file: IFile) {
    this.dialogService
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Delete this file',
          content: 'Are you sure delete this file?',
          isDelete: true,
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (!!value) {
          this.confirmDeleteFile(file.id);
          this.clearAllSelectedFolderAndFile();
        }
      });
  }

  confirmDeleteFile(id: number | undefined) {
    if (!id) return;
    const index = this.fileList.findIndex((item) => item.id === id);
    if (index === -1) return;
    this.fileList[index] = {
      ...this.fileList[index],
      isDeleting: true,
    };
    this.fileList = cloneDeep(this.fileList);
    this.KnowledgeBaseService.deleteFAQ(id).subscribe(
      (res) => {
        this.snackBar.open('Delete successfully', '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        this.viewFolder({ ...this.searchModel, id: this.parentId });
      },
      (error) => {
        this.fileList[index] = {
          ...this.fileList[index],
          isDeleting: false,
        };
        this.fileList = cloneDeep(this.fileList);
      }
    );
  }

  deleteAllFile() {
    if (this.fileSelection.selected.length > 0) {
      const body = {
        list_id: this.fileSelection.selected.map((item) => item.id),
      };
      this.fileList.forEach((file) => {
        if (
          this.fileSelection.selected.map((item) => item.id).includes(file.id)
        ) {
          file.isDeleting = true;
        }
      });
      this.fileList = cloneDeep(this.fileList);
      this.fileSelection.clear();
      this.KnowledgeBaseService.deleteListFAQ(body).subscribe({
        next: (res) => {
          if (res) {
            this.snackBar.open('Delete successfully', '', {
              panelClass: 'dx-snack-bar-success',
              duration: 5000,
              verticalPosition: 'top',
              horizontalPosition: 'right',
            });
            this.viewFolder({ ...this.searchModel, id: this.parentId });
            this.clearAllSelectedFolderAndFile();
          }
        },
      });
    }
  }

  retrainFile(id: number | undefined) {
    if (!id) return;
    const index = this.fileList.findIndex((item) => item.id === id);
    if (index === -1) return;
    this.fileList[index] = {
      ...this.fileList[index],
      isRetraining: true,
    };
    this.fileList = cloneDeep(this.fileList);
    this.KnowledgeBaseService.retrainFAQ(id).subscribe({
      next: (res) => {
        this.snackBar.open('Retraining file', '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
      error: () => {
        this.fileList[index] = {
          ...this.fileList[index],
          isRetraining: false,
        };
        this.fileList = cloneDeep(this.fileList);
      },
    });
  }

  openAssignPermissionDialog() {
    if (this.folderSelection.selected.length == 0) return;
    const body = {
      folder_ids: this.folderSelection.selected.map((item) => item.id),
    };
    this.fileFolderService.getPermissionAssigned(body).subscribe({
      next: (res) => {
        if (res) {
          res.assign_permissions = res.assign_permissions.map((v: any) => {
            return {
              ...v,
              username: v?.user?.email,
              permissions: this.numberToBinaryComponents(v.permission),
            };
          });
          this.assignFolderForm.patchValue(res);
          if (res.assign_permissions.length > 0) {
            while (
              this.assignPermissionsFormArray.length <
              res.assign_permissions.length
            ) {
              this.assignPermissionsFormArray.push(
                this.fb.group({
                  user_id: [null, [Validators.required]],
                  username: [''],
                  folder_id: [null],
                  permissions: [[], [Validators.required]],
                })
              );
            }
            res.assign_permissions.forEach((val: any, index: number) => {
              (
                this.assignPermissionsFormArray.at(index) as FormGroup
              ).patchValue(val);
            });
            this.listUserInAIAllowed = this.listUserInAI.filter((user) =>
              this.assignPermissionsFormArray
                .getRawValue()
                .some((v) => v.user_id != user.id)
            );
          }
        }
      },
    });
    this.userSelection.clear();
    this.assignFolderForm.patchValue({
      folder_ids: this.folderSelection.selected.map((v) => v.id),
    });
    while (this.assignPermissionsFormArray.length !== 0) {
      this.assignPermissionsFormArray.removeAt(0);
    }
    this.showDialog(this.assignPermissionFolderDialog, {
      data: {
        countFolder: this.folderSelection.selected.length,
      },
      width: '35vw',
    });
  }

  get assignPermissionsFormArray() {
    return this.assignFolderForm.get('assign_permissions') as FormArray;
  }

  getAssignPermissionsFormArrayValue(name?: string) {
    if (!name) {
      return this.assignPermissionsFormArray.value;
    } else {
      return this.assignPermissionsFormArray.controls.map(
        (v) => (v as any).controls[name].value
      )[0];
    }
  }

  getAssignPermissionsFormArrayValueIndex(name: string, index: number) {
    if (name) {
      return (this.assignPermissionsFormArray.at(index) as FormGroup).controls[
        name
      ].value;
    }
  }

  addAssignPermissions(event: any) {
    const user_id = typeof event === 'number' ? event : event?.value || null;
    if (!user_id || this.userSelection.isSelected(user_id)) return;
    this.userSelection.select(user_id);
    this.assignPermissionsFormArray.push(
      this.fb.group({
        user_id: [user_id, [Validators.required]],
        folder_id: [null],
        username: [
          user_id && this.listUserInAI
            ? this.listUserInAI.find((v) => v.id === user_id)?.email || ''
            : '',
        ],
        permissions: [[], [Validators.required]],
      })
    );
  }

  selectPermissions(permission: any, index: number) {
    const oldValue = { ...this.assignPermissionsFormArray.at(index).value };
    if (permission.includes(2) || permission.includes(4)) {
      permission.push(1);
    }
    permission = [...new Set(permission)];
    this.assignPermissionsFormArray.at(index).patchValue({
      ...oldValue,
      permissions: permission,
    });
  }

  savePermissionAssign() {
    if (this.assignFolderForm.invalid) return;
    this.isSavingPermission = true;
    const body = {
      ...this.assignFolderForm.value,
    };
    body.assign_permissions = body.assign_permissions.map((v: any) => {
      const biPermission = v.permissions.reduce(
        (a: number, b: number) => a | b,
        0
      );
      return {
        ...v,
        permissions: biPermission,
      };
    });
    this.fileFolderService.assignUserFolderPermissions(body).subscribe({
      next: (res) => {
        this.snackBar.open('Assign permissions successfully', '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        this.viewFolder({ ...this.searchModel });
        this.isSavingPermission = false;
        this.userSelection.clear();
        this.folderSelection.clear();
        this.closeDialog();
      },
      error: (error) => {
        this.isSavingPermission = false;
        this.viewFolder({ ...this.searchModel });
        this.snackBar.open(error.error.detail, '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        this.userSelection.clear();
        this.folderSelection.clear();
        this.closeDialog();
      },
    });
  }

  override closeDialog() {
    this.documentName = '';
    this.metadata = '';
    this.fileUpload = null;
    this.fileURL = '';
    this.rootURL = '';
    this.modeGetUrl = 'getOne';
    this.listUserInAIAllowed = this.listUserInAI;
    this.openRetrainDialog = false;
    this.editMode = false;
    this.draftContent = '';
    super.closeDialog();
  }

  toBreadcrumbItem(folder?: IFolder | undefined) {
    if (this.breadcrumbs.length > 0) {
      this.toFolder(folder);
    }
  }

  showStyle: string = 'list';

  checkFileCsv(fileName: string): boolean {
    if (!fileName) return false;
    const extension = fileName.split('.').pop();
    return extension?.toLowerCase() === 'csv' || false;
  }

  getFolderById(folder_id: number) {
    return this.folderList.find((folder) => folder.id === folder_id);
  }

  get isAllowAssign() {
    return Boolean(
      this.roleUserInAI &&
        (this.roleUserInAI === 'OWNER' || this.roleUserInAI === 'ADMIN')
    );
  }

  deleteAll() {
    let title = '';
    let content = '';
    if (this.fileSelection.selected.length > 0) {
      title = 'Delete all these files';
      content = 'Are you sure delete all these files?';
    }
    if (this.folderSelection.selected.length > 0) {
      title = 'Delete all these folders';
      content = 'Are you sure delete all these folders?';
    }
    if (
      this.fileSelection.selected.length > 0 &&
      this.folderSelection.selected.length > 0
    ) {
      title = 'Delete all these files and folders';
      content = 'Are you sure delete all these files and folders?';
    }
    this.dialogService
      .open(ConfirmDialogComponent, {
        data: {
          title,
          content,
          isDelete: true,
        },
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (!!value) {
          this.deleteAllFile();
          this.deleteAllFolder();
        }
      });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  private viewFolder(searchModel: IFolderFilter, loadMore: boolean = false) {
    if (!loadMore) {
    } else {
      this.isLoadingMore = true;
    }
    if (
      this.parentId !== null &&
      (!searchModel.id || searchModel.id !== this.parentId)
    ) {
      searchModel = { ...searchModel, id: this.parentId };
    }
    this.fileFolderService.viewFolder(searchModel).subscribe({
      next: (data: IViewFile): void => {
        this.isLoadingMore = false;
        if (data.pagination) {
          this.hasMoreData = data.pagination.has_more;
        } else {
          this.hasMoreData = false;
        }
        this.folderList = [];
        this.fileList = [];
        this.combinedList = [];
        if (data && data?.folders) {
          const newFolders = data.folders.map((folder: IFolder) => ({
            ...folder,
          }));
          this.folderList = newFolders;
        } else {
          this.folderList = [];
        }
        if (data && data?.files) {
          const newFiles = data.files.map((file: any) => ({ ...file }));
          this.fileList = newFiles;
        } else {
          this.fileList = [];
        }
        if (data && data?.permissions) {
          this.permissions = data?.permissions;
          this.parent_folder_permissions = data?.permissions;
        }
        this.updateCombinedList();
        this.cdr.detectChanges();
        if (!this.isNavigatingToFolder) {
          setTimeout(() => {
            this.refreshVirtualScrollViewports();
            this.cdr.detectChanges();
          }, 300);
        }
      },
      error: (error): void => {
        this.isLoadingMore = false;
      },
    });
  }

  private updateBreadcrumb(folder: IFolder | null): void {
    if (folder) {
      const indexBreadcrumbDup: number = this.breadcrumbs.findIndex(
        (v) => v.id === folder.id
      );
      if (indexBreadcrumbDup >= 0) {
        this.breadcrumbs.splice(indexBreadcrumbDup + 1);
      } else {
        this.breadcrumbs.push(folder);
      }
    } else {
      this.breadcrumbs = [];
    }
  }

  private getCollections() {
    this.collectionsService.getListCollection().subscribe((res) => {
      if (res) {
        this.collectionData = res;
        this.collectionOfFile = this.collectionData[0].id;
      }
    });
  }

  private getListUserInAi() {
    this.settingsService.getUsersInAI().subscribe({
      next: (res) => {
        if (res) {
          this.listUserInAI = res;
          this.listUserInAIAllowed = res;
        }
      },
    });
  }

  private listenFileUpdateSocket() {
    this.subscription.add(
      this.socketService.listenEventCs('message').subscribe({
        next: (res: any) => {
          if (res) {
            if (res.type === 'FILES_UPDATE')
              this.viewFolder({ ...this.searchModel, id: this.parentId });
            if (res.type === 'FILES_DELETE')
              this.viewFolder({ ...this.searchModel, id: this.parentId });
            if (res.type === 'FILES_FAILED')
              this.snackBar.open(res.content, '', {
                panelClass: 'dx-snack-bar-error',
                duration: 5000,
                verticalPosition: 'top',
                horizontalPosition: 'right',
              });
          }
        },
      })
    );
  }

  private listenKeyboardEvent() {
    this.renderer2.listen('document', 'keydown', (event: KeyboardEvent) => {
      if (event.key === 'Shift') this.isShiftPressed = true;
      if (event.key === 'Control') this.isCtrlPressed = true;
    });
    this.renderer2.listen('document', 'keyup', (event: KeyboardEvent) => {
      if (event.key === 'Shift') this.isShiftPressed = false;
      if (event.key === 'Control') this.isCtrlPressed = false;
    });
  }

  private listenMouseEvent() {
    const knowledgeBaseElement = document.querySelector('app-knowledge-base');
    this.renderer2.listen(
      knowledgeBaseElement,
      'mousedown',
      (event: MouseEvent) => {
        // Chỉ ngăn chặn sự kiện mặc định khi cần thiết
        // Không ngăn chặn sự kiện trên các phần tử input, select, button
        const target = event.target as HTMLElement;
        if (
          target.tagName !== 'INPUT' &&
          target.tagName !== 'SELECT' &&
          target.tagName !== 'BUTTON' &&
          !target.closest('input') &&
          !target.closest('select') &&
          !target.closest('button') &&
          !target.closest('app-select') &&
          !target.closest('mat-menu-item')
        ) {
          // Chỉ ngăn chặn sự kiện mặc định cho các phần tử không phải là input, select, button
          // event.preventDefault();
        }
      }
    );
    this.renderer2.listen(
      knowledgeBaseElement,
      'mouseup',
      (event: MouseEvent) => {
        const target = event.target as HTMLElement;
        if (
          target.classList.contains('knowledge-base-wrapper') ||
          target.classList.contains('file-folder-list-wrapper')
        ) {
          this.folderSelection.clear();
          this.fileSelection.clear();
        }
      }
    );
  }

  private numberToBinaryComponents(permission: number): number[] {
    const result: number[] = [];
    if (permission & 1) result.push(1);
    if (permission & 2) result.push(2);
    if (permission & 4) result.push(4);
    return result;
  }

  getFormattedDate(file: any, type: string): string {
    if (type == 'create') {
      const date = file.created_at;
      return date
        ? new Date(date).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
          })
        : '-';
    } else {
      const date = file.updated_at;
      return date
        ? new Date(date).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
          })
        : '-';
    }
  }

  getFormattedDateTime(file: any, type: string): string {
    let date: Date | null;
    if (type === 'create') {
      date = file.created_at ? new Date(file.created_at) : null;
    } else {
      date = file.updated_at ? new Date(file.updated_at) : null;
    }
    if (!date) return '';
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const month = date.toLocaleString('en-GB', { month: 'short' });
    const year = date.getFullYear();
    return `${hours}:${minutes}:${seconds}, ${day} ${month}, ${year}`;
  }

  changeFilter(order: string, name: string) {
    switch (name) {
      case 'name':
        this.nameOrder = order;
        break;
      case 'updated_at':
        this.updateOrder = order;
        break;
      case 'created_at':
        this.createdOrder = order;
        break;
      default:
        break;
    }
    this.searchModel.order = order as 'ASC' | 'DESC';
    this.searchModel.sort_by = name;
    this.applyFilter();
  }

  get editMode(): boolean {
    return this._editMode;
  }

  set editMode(value: boolean) {
    this.onEditModeChange(value);
    this._editMode = value;
  }

  handleEditModeChange(event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;
    this.editMode = isChecked;
  }

  cancelSaveRetrain() {
    this.openRetrainDialog = false;
    this.editMode = false;
    this.draftContent = '';
  }

  onEditModeChange(isEditMode: boolean) {
    let contentHeight = 100;
    if (isEditMode) {
      if (this.contentDiv && this.contentDiv.nativeElement) {
        contentHeight = this.contentDiv.nativeElement.offsetHeight;
      }
      this.openRetrainDialog = true;
      this.draftContent = this.selectedFile.text_content;
      setTimeout(() => {
        this.adjustTextareaHeight(contentHeight);
      }, 0);
    } else if (this.openRetrainDialog) {
      this.confirmRetrain();
    }
  }

  adjustTextareaHeight(contentHeight: number) {
    if (this.editTextarea) {
      const maxHeight = window.innerHeight * 0.6 - 10;
      if (contentHeight > maxHeight) {
        this.editTextarea.nativeElement.style.height = `${maxHeight}px`;
      } else {
        this.editTextarea.nativeElement.style.height = `${contentHeight}px`;
      }
    }
  }

  confirmRetrain(changeEdit: boolean = false) {
    this.dialogService
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Save and retrain',
          content: 'Are you sure save and retrain this folder?',
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (!!value) {
          const body = {
            file_id: this.selectedFile.id,
            content: this.draftContent,
          };
          this.selectedFile.text_content = this.draftContent;
          if (!changeEdit) {
            this.draftContent = '';
            this.openRetrainDialog = false;
            this.editMode = false;
          }
          this.KnowledgeBaseService.retrainDocument(body).subscribe({
            next: (res) => {
              this.snackBar.open('Retraining file', '', {
                panelClass: 'dx-snack-bar-success',
                duration: 5000,
                verticalPosition: 'top',
                horizontalPosition: 'right',
              });
              this.editMode = false;
            },
            error: () => {
              this.snackBar.open('Retraining failed', '', {
                panelClass: 'dx-snack-bar-error',
                duration: 5000,
                verticalPosition: 'top',
                horizontalPosition: 'right',
              });
            },
          });
        }
      });
  }

  removeSelectedFolderOrFile() {}

  clearAllSelectedFolderAndFile() {
    this.folderSelection.clear();
    this.fileSelection.clear();
  }

  copyText(content: string) {
    const tempTextarea = document.createElement('textarea');
    tempTextarea.value = content;
    document.body.appendChild(tempTextarea);
    tempTextarea.select();
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        this.snackBar.open('Copied successfully', '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      } else {
        this.snackBar.open('Copy failed', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      }
    } catch (err) {
      this.snackBar.open('Copy failed', '', {
        panelClass: 'dx-snack-bar-error',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }
    document.body.removeChild(tempTextarea);
  }

  isEditing: boolean = false;
  newName: string = '';
  selectedFolderMoveId: any;
  breadcrumbsMoveDialog: IFolder[] = [];
  parentIdMoveDialog: any;

  toggleEdit(file: any) {
    this.isEditing = !this.isEditing;
    this.fileRename = {
      file_id: file.id,
      new_name: file.name,
    };
  }

  cancelEdit() {
    this.isEditing = false;
    if (this.selectedFile) {
      this.fileRename.new_name = this.selectedFile.name;
    }
  }

  saveRenameFile() {
    if (this.isEditing) {
      this.isEditing = false;
    }
    if (this.fileRename.new_name === this.selectedFile?.name) {
      return;
    }
    const fileIndex = this.fileList.findIndex(
      (file) => file.id === this.fileRename.file_id
    );
    this.KnowledgeBaseService.renameFile(this.fileRename).subscribe(
      (res) => {
        if (fileIndex !== -1) {
          this.fileList[fileIndex].name = this.fileRename.new_name;
        }
        if (
          this.selectedFile &&
          this.selectedFile.id === this.fileRename.file_id
        ) {
          this.selectedFile.name = this.fileRename.new_name;
        }
        this.snackBar.open('Rename successfully', '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
      (error) => {
        this.snackBar.open('Rename failed', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      }
    );
  }

  searchFeature(loadMore: boolean = false) {
    if (!loadMore) {
      this.searchingMode = !!(
        this.searchModel.name?.trim() ||
        this.searchModel.file_type ||
        this.searchModel.file_status
      );
      this.folderList = [];
      this.fileList = [];
      this.combinedList = [];
    } else {
      this.isLoadingMore = true;
    }
    this.KnowledgeBaseService.searchingAllFileFolder(
      this.searchModel
    ).subscribe({
      next: (res) => {
        this.isLoadingMore = false;
        const data = res;
        if (data.pagination) {
          this.hasMoreData = data.pagination.has_more;
        } else {
          this.hasMoreData = false;
        }
        if (data && data?.folders) {
          const newFolders = data.folders.map((folder: IFolder) => ({
            ...folder,
          }));
          this.folderList = loadMore
            ? [...this.folderList, ...newFolders]
            : newFolders;
        } else if (!loadMore) {
          this.folderList = [];
        }
        if (data && data?.files) {
          const newFiles = data.files.map((file: any) => ({ ...file }));
          this.fileList = loadMore ? [...this.fileList, ...newFiles] : newFiles;
        } else if (!loadMore) {
          this.fileList = [];
        }
        if (data && data?.permissions) {
          this.permissions = data?.permissions;
          this.parent_folder_permissions = data?.permissions;
        }
        this.updateCombinedList();
        this.cdr.detectChanges();
        if (!this.isNavigatingToFolder) {
          setTimeout(() => {
            this.refreshVirtualScrollViewports();
            this.cdr.detectChanges();
          }, 300);
        }
      },
      error: (error) => {
        this.isLoadingMore = false;
      },
    });
  }

  goToFileLocation(file: any) {
    if (file.folder_id) {
      this.KnowledgeBaseService.getFolderById(file.folder_id).subscribe(
        (res) => {
          this.toFolder(res, undefined, 'file', file);
        }
      );
    } else {
      this.toFolder(undefined);
    }
  }

  goToFolderLocation(folder: IFolder) {
    if (folder.parent_id) {
      this.KnowledgeBaseService.getFolderById(folder.parent_id).subscribe(
        (res) => {
          this.toFolder(res, undefined, 'folder', folder);
        }
      );
    } else {
      this.toFolder(undefined);
    }
  }

  moveFile(file: any) {
    this.fileNeedMove = file;
    const searchModel = {
      name: '',
      file_type: null,
      file_status: null,
      sort_by: 'created_at',
      order: 'DESC' as 'ASC' | 'DESC',
    };
    this.showDialog(this.moveFileDialog, {
      data: { name: file.name },
      width: '40vw',
      minWidth: '350px',
    });
    this.loadingMoveDialog = true;
    this.fileFolderService.viewFolder(searchModel).subscribe((res) => {
      this.loadingMoveDialog = false;
      this.foldersCanMoveTo = res.folders;
    });
  }

  onFolderMoveClick(folderId: number) {
    this.selectedFolderMoveId = folderId;
  }

  goToNextFolder(folder: IFolder | null) {
    this.foldersCanMoveTo = [];
    this.loadingMoveDialog = true;
    const searchModel: IFolderFilter = {
      id: null,
      name: '',
      file_type: null,
      file_status: null,
      sort_by: 'created_at',
      order: 'DESC',
      page: 1,
      limit: 100,
    };
    if (folder && folder?.id) {
      this.parentIdMoveDialog = folder.id;
      searchModel.id = folder.id;
      this.breadcrumbsMoveDialog.push(folder);
    } else {
      this.parentIdMoveDialog = null;
    }
    this.fileFolderService.viewFolder(searchModel).subscribe((res) => {
      this.foldersCanMoveTo = res.folders || [];
      this.loadingMoveDialog = false;
      this.selectedFolderMoveId = -1;
    });
  }

  toBreadcrumbItemMoveDialog(param: any) {
    this.breadcrumbsMoveDialog = this.modifyArray(
      this.breadcrumbsMoveDialog,
      param
    );
    this.goToNextFolder(param);
  }

  modifyArray(array: any[], target: any | undefined): any[] {
    if (target === undefined) {
      return [];
    }
    const index = array.findIndex((item) => item.id == target.id);
    if (index === -1) {
      return array;
    }
    return array.slice(0, index);
  }

  moveFileToFolder() {
    let body = {
      file_id: this.fileNeedMove.id,
      folder_id: 0,
    };
    if (this.selectedFolderMoveId > 0) {
      body.folder_id = this.selectedFolderMoveId;
    } else if (this.foldersCanMoveTo.length === 0) {
      const lastBreadcrumb =
        this.breadcrumbsMoveDialog?.[this.breadcrumbsMoveDialog.length - 1];
      body.folder_id = lastBreadcrumb?.id ?? 0;
    } else {
      body.folder_id = 0;
    }
    this.fileFolderService.moveFileLocation(body).subscribe(
      (res) => {
        this.closeDialog();
        this.snackBar.open('Move file successfully', '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        window.location.reload();
      },
      (error) => {
        this.snackBar.open('Failed to move file', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      }
    );
  }

  onToggleChange(event: any): void {
    this.folderSelection.clear();
    this.fileSelection.clear();
    const oldStyle = this.showStyle;
    this.showStyle = event.value;
    if (oldStyle !== this.showStyle) {
      setTimeout(() => {
        this.refreshVirtualScrollViewports();
      }, 300);
    }
  }

  private updateCombinedList(): void {
    this.combinedList = [];
    const folderItems = this.folderList.map((folder: IFolder) => ({
      ...folder,
      isFolder: true,
    }));
    const fileItems = this.fileList.map((file: any) => ({
      ...file,
      isFolder: false,
    }));
    this.combinedList = [...folderItems, ...fileItems];
  }

  public isFolder(item: { isFolder?: boolean } | any): boolean {
    return item && item.isFolder === true;
  }

  public isFile(item: { isFolder?: boolean } | any): boolean {
    return item && item.isFolder === false;
  }

  public trackByItemId(
    index: number,
    item: { isFolder?: boolean; id?: number } | any
  ): string | number {
    if (!item) return index;
    return item.isFolder ? `folder_${item.id}` : `file_${item.id}`;
  }

  public trackByFolderId(
    index: number,
    folder: IFolder | any
  ): string | number {
    return folder && folder.id ? `folder_${folder.id}` : index;
  }

  public trackByFileId(index: number, file: IFile | any): string | number {
    return file && file.id ? `file_${file.id}` : index;
  }
}
