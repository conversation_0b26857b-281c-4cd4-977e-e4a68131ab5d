<div class="pr-3 h-full flex flex-col overflow-hidden">
  <div class="text-base-content dark:text-dark-base-content pl-4 w-full h-full mt-5 flex flex-col"
    [ngStyle]="{ height: 'calc(100% - 20px)', overflow: 'hidden' }" (clickOutside)="removeSelectedFolderOrFile()">
    <div class="flex flex-col flex-grow overflow-hidden" [ngStyle]="{ height: 'calc(100% - 52px)' }">
      <!-- List Header -->
      <div class="list-header">
        <div class="list-cell name">
          <div class="flex items-center justify-center">
            <p>Name</p>
            <div class="ml-2">
              <div
                class="flex items-center justify-center p-1 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl">
                @if (nameOrder() === 'DESC') {
                <ng-icon (click)="changeFilter('ASC', 'name')" (mouseup)="$event.stopPropagation()" name="heroArrowDown"
                  class="text-blue-500"></ng-icon>
                } @if (nameOrder() === 'ASC') {
                <ng-icon (click)="changeFilter('DESC', 'name')" (mouseup)="$event.stopPropagation()" name="heroArrowUp"
                  class="text-blue-500"></ng-icon>
                }
              </div>
            </div>
          </div>
        </div>
        <div class="list-cell date">
          <div class="flex items-center justify-center">
            <p>Created</p>
            <div class="ml-2">
              <div
                class="flex items-center justify-center p-1 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl">
                @if (createOrder() === 'DESC') {
                <ng-icon (click)="changeFilter('ASC', 'created_at')" (mouseup)="$event.stopPropagation()"
                  name="heroArrowDown" class="text-blue-500"></ng-icon>
                } @if (createOrder() === 'ASC') {
                <ng-icon (click)="changeFilter('DESC', 'created_at')" (mouseup)="$event.stopPropagation()"
                  name="heroArrowUp" class="text-blue-500"></ng-icon>
                }
              </div>
            </div>
          </div>
        </div>
        <div class="list-cell date">
          <div class="flex items-center justify-center">
            <p>Last update</p>
            <div class="ml-2">
              <div
                class="flex items-center justify-center p-1 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl">
                @if (updateOrder() === 'DESC') {
                <ng-icon (click)="changeFilter('ASC', 'updated_at')" (mouseup)="$event.stopPropagation()"
                  name="heroArrowDown" class="text-blue-500"></ng-icon>
                } @if (updateOrder() === 'ASC') {
                <ng-icon (click)="changeFilter('DESC', 'updated_at')" (mouseup)="$event.stopPropagation()"
                  name="heroArrowUp" class="text-blue-500"></ng-icon>
                }
              </div>
            </div>
          </div>
        </div>
        <div class="list-cell status">
          <div class="flex items-center justify-center">Status</div>
        </div>
        <div class="list-cell actions"></div>
      </div>

      <!-- List Content -->
      <cdk-virtual-scroll-viewport #listViewport id="listViewport" itemSize="50" minBufferPx="200" maxBufferPx="400"
        style="
          height: calc(100% - 56px);
          overflow: auto;
          width: 100%;
          flex-grow: 1;
        " class="h-full">
        <ng-container *cdkVirtualFor="
        let item of combinedList();
        let i = index;
        trackBy: trackByItemId
      ">
          <!-- Folder Row -->
          @if (isFolder(item)) {
          <div (dblclick)="onFolderDoubleClick(item)" (click)="onFolderClick(item)" [ngClass]="
          folderSelection().isSelected($any(item))
            ? 'bg-light-primary dark:bg-dark-primary text-base-content dark:text-dark-base-content'
            : 'hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover'
        " [attr.data-folder-id]="item.id" class="list-row">
            <div class="list-cell name">
              <ng-icon name="faSolidFolder" class="text-yellow-400 text-xl inline-table mr-3 folder-icon"
                (dblclick)="$event.stopPropagation(); onFolderDoubleClick(item)"></ng-icon>
              <p class="truncate folder-name text-base-content dark:text-dark-base-content" [dxTooltip]="item.name"
                dxTooltipPosition="above" (dblclick)="$event.stopPropagation(); onFolderDoubleClick(item)">
                {{ item.name }}
              </p>
            </div>
            <div class="list-cell date text-base-content dark:text-dark-base-content"
              [dxTooltip]="getFormattedDateTime(item, 'create')" [dxTooltipPosition]="'left'">
              {{ getFormattedDate(item, "create") }}
            </div>
            <div class="list-cell date text-base-content dark:text-dark-base-content"
              [dxTooltip]="getFormattedDateTime(item, 'update')" [dxTooltipPosition]="'left'">
              {{ getFormattedDate(item, "update") }}
            </div>
            <div class="list-cell status"></div>
            <div class="list-cell actions">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                (click)="$event.stopPropagation()" [matMenuTriggerFor]="folderMenu"
                class="hover:cursor-pointer menu-trigger">
                <path
                  d="M8 12C8 13.1046 7.10457 14 6 14C4.89543 14 4 13.1046 4 12C4 10.8954 4.89543 10 6 10C7.10457 10 8 10.8954 8 12Z"
                  fill="#6F767E" />
                <path
                  d="M14 12C14 13.1046 13.1046 14 12 14C10.8954 14 10 13.1046 10 12C10 10.8954 10.8954 10 12 10C13.1046 10 14 10.8954 14 12Z"
                  fill="#6F767E" />
                <path
                  d="M20 12C20 13.1046 19.1046 14 18 14C16.8954 14 16 13.1046 16 12C16 10.8954 16.8954 10 18 10C19.1046 10 20 10.8954 20 12Z"
                  fill="#6F767E" />
              </svg>
              <mat-menu #folderMenu="matMenu" xPosition="after" class="my-menu">
                <button mat-menu-item (click)="onFolderRename(item)"
                  class="!h-10 text-base-content dark:text-dark-base-content">
                  <div class="flex items-center">
                    <ng-icon name="heroPencilSquare" class="text-orange-500 text-2xl mr-3"></ng-icon>
                    <span class="text-base-content dark:text-dark-base-content">Rename</span>
                  </div>
                </button>
                <button mat-menu-item (click)="onFolderDelete(item)"
                  class="!h-10 text-base-content dark:text-dark-base-content">
                  <div class="flex items-center">
                    <ng-icon name="heroTrash" class="text-red-500 text-2xl mr-3"></ng-icon>
                    <span class="text-base-content dark:text-dark-base-content">Delete</span>
                  </div>
                </button>
              </mat-menu>
            </div>
          </div>
          }

          <!-- File Row -->
          @if (isFile(item)) {
          <div class="list-row border-b border-b-light-border-line dark:border-b-dark-border-line"
            (dblclick)="onFileDoubleClick(item)" (click)="onFileClick(item)" [ngClass]="
          fileSelection().isSelected($any(item))
            ? 'bg-light-primary dark:bg-dark-primary text-base-content dark:text-dark-base-content'
            : 'hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover'
        " draggable="true" [attr.data-file-id]="item.id">
            <div class="list-cell name">
              <!-- File Icon -->
              @switch($any(item).ext) {
              @case ('PDF') {
              <svg class="!inline-table mr-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"
                style="width: 20px; height: 20px" fill="#f28b82">
                <path
                  d="M0 64C0 28.7 28.7 0 64 0L224 0l0 128c0 17.7 14.3 32 32 32l128 0 0 144-208 0c-35.3 0-64 28.7-64 64l0 144-48 0c-35.3 0-64-28.7-64-64L0 64zm384 64l-128 0L256 0 384 128zM176 352l32 0c30.9 0 56 25.1 56 56s-25.1 56-56 56l-16 0 0 32c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-48 0-80c0-8.8 7.2-16 16-16zm32 80c13.3 0 24-10.7 24-24s-10.7-24-24-24l-16 0 0 48 16 0zm96-80l32 0c26.5 0 48 21.5 48 48l0 64c0 26.5-21.5 48-48 48l-32 0c-8.8 0-16-7.2-16-16l0-128c0-8.8 7.2-16 16-16zm32 128c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-16 0 0 96 16 0zm80-112c0-8.8 7.2-16 16-16l48 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 32 32 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 48c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-64 0-64z" />
              </svg>
              }
              @case ('CSV') {
              <ng-icon name="faSolidFileCsv" class="!text-[#81c995] text-xl !inline-table mr-3"></ng-icon>
              }
              @case ('MD') {
              <ng-icon name="faSolidFilePen" class="!text-[#8ab4f8] !inline-table text-xl mr-3"></ng-icon>
              }
              @case ('TXT') {
              <ng-icon name="faSolidFileLines" class="!text-[#8ab4f8] text-xl !inline-table mr-3"></ng-icon>
              }
              @case ('URL') {
              <ng-icon name="faSolidLink" class="!text-[#c58af9] text-xl !inline-table mr-3"></ng-icon>
              }
              @default {
              <ng-icon name="faSolidFile" class="!text-[#6F767E] text-xl !inline-table mr-3"></ng-icon>
              }
              }
              <p class="truncate text-base-content dark:text-dark-base-content" [dxTooltip]="item.name"
                dxTooltipPosition="above">
                {{ item.name }}
              </p>
            </div>
            <div class="list-cell date text-base-content dark:text-dark-base-content"
              [dxTooltip]="getFormattedDateTime(item, 'create')" [dxTooltipPosition]="'left'">
              {{ getFormattedDate(item, "create") }}
            </div>
            <div class="list-cell date text-base-content dark:text-dark-base-content"
              [dxTooltip]="getFormattedDateTime(item, 'update')" [dxTooltipPosition]="'left'">
              {{ getFormattedDate(item, "update") }}
            </div>
            <div class="list-cell status">
              @switch ($any(item).status) {
              @case ('COMPLETED') {
              <mat-chip class="status-chip green">Completed</mat-chip>
              }
              @case ('IN_PROGRESS') {
              <mat-chip class="status-chip blue">
                <div class="flex gap-2 items-center">
                  <mat-spinner diameter="16"></mat-spinner>
                  <p>Processing</p>
                </div>
              </mat-chip>
              }
              @case ('ERROR') {
              <mat-chip class="status-chip red">Error</mat-chip>
              }
              @case ('PENDING') {
              <mat-chip class="status-chip yellow">Pending</mat-chip>
              }
              @default {
              <mat-chip class="status-chip">-</mat-chip>
              }
              }
            </div>
            <div class="list-cell actions">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                (click)="$event.stopPropagation()" [matMenuTriggerFor]="fileMenu"
                class="hover:cursor-pointer menu-trigger">
                <path
                  d="M8 12C8 13.1046 7.10457 14 6 14C4.89543 14 4 13.1046 4 12C4 10.8954 4.89543 10 6 10C7.10457 10 8 10.8954 8 12Z"
                  fill="#6F767E" />
                <path
                  d="M14 12C14 13.1046 13.1046 14 12 14C10.8954 14 10 13.1046 10 12C10 10.8954 10.8954 10 12 10C13.1046 10 14 10.8954 14 12Z"
                  fill="#6F767E" />
                <path
                  d="M20 12C20 13.1046 19.1046 14 18 14C16.8954 14 16 13.1046 16 12C16 10.8954 16.8954 10 18 10C19.1046 10 20 10.8954 20 12Z"
                  fill="#6F767E" />
              </svg>
              <mat-menu #fileMenu="matMenu" xPosition="after"
                class="my-menu bg-light-background dark:bg-dark-background">
                <button mat-menu-item (click)="onFileInfo(item)"
                  class="!h-10 text-base-content dark:text-dark-base-content">
                  <div class="flex items-center">
                    <ng-icon name="heroInformationCircle" class="text-blue-500 text-2xl mr-3"></ng-icon>
                    <span class="text-base-content dark:text-dark-base-content">Info</span>
                  </div>
                </button>
                <button mat-menu-item (click)="onFileRename(item)"
                  class="!h-10 text-base-content dark:text-dark-base-content">
                  <div class="flex items-center">
                    <ng-icon name="heroPencilSquare" class="text-orange-500 text-2xl mr-3"></ng-icon>
                    <span class="text-base-content dark:text-dark-base-content">Rename</span>
                  </div>
                </button>
                <button mat-menu-item (click)="onFileMove(item)"
                  class="!h-10 text-base-content dark:text-dark-base-content">
                  <div class="flex items-center">
                    <svg class="text-blue-500 mr-3" width="24px" height="24px" viewBox="0 0 24 24" focusable="false"
                      fill="#3B82F6">
                      <path fill="none" d="M0 0h24v24H0V0z"></path>
                      <path
                        d="M20 6h-8l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 12H4V8h16v10zm-8.01-9l-1.41 1.41L12.16 12H8v2h4.16l-1.59 1.59L11.99 17 16 13.01 11.99 9z">
                      </path>
                    </svg>
                    <span class="text-base-content dark:text-dark-base-content">Move</span>
                  </div>
                </button>
                <button mat-menu-item (click)="onFileDelete(item)"
                  class="!h-10 text-base-content dark:text-dark-base-content">
                  <div class="flex items-center">
                    <ng-icon name="heroTrash" class="text-red-500 text-2xl mr-3"></ng-icon>
                    <span class="text-base-content dark:text-dark-base-content">Delete</span>
                  </div>
                </button>
              </mat-menu>
            </div>
          </div>
          }
        </ng-container>
      </cdk-virtual-scroll-viewport>
    </div>
  </div>
</div>