<div class="pr-3 h-full flex flex-col overflow-hidden">
  <div class="text-base-content dark:text-dark-base-content w-full h-full mt-5 flex flex-col"
    [ngStyle]="{ height: 'calc(100% - 20px)', overflow: 'hidden' }">
    <div class="flex flex-col flex-grow overflow-hidden" [ngStyle]="{ height: 'calc(100% - 52px)' }">
      <!-- List Header -->
      <div class="list-header">
        <div class="list-cell name">
          <div class="flex items-center justify-center">
            <p>Name</p>
            <div class="ml-2">
              <div
                class="flex items-center justify-center p-1 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl">
                @if (nameOrder() === 'DESC') {
                <ng-icon (click)="changeFilter('ASC', 'name')" (mouseup)="$event.stopPropagation()" name="heroArrowDown"
                  class="text-blue-500"></ng-icon>
                } @if (nameOrder() === 'ASC') {
                <ng-icon (click)="changeFilter('DESC', 'name')" (mouseup)="$event.stopPropagation()" name="heroArrowUp"
                  class="text-blue-500"></ng-icon>
                }
              </div>
            </div>
          </div>
        </div>
        <div class="list-cell date">
          <div class="flex items-center justify-center">
            <p>Created</p>
            <div class="ml-2">
              <div
                class="flex items-center justify-center p-1 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl">
                @if (createOrder() === 'DESC') {
                <ng-icon (click)="changeFilter('ASC', 'created_at')" (mouseup)="$event.stopPropagation()"
                  name="heroArrowDown" class="text-blue-500"></ng-icon>
                } @if (createOrder() === 'ASC') {
                <ng-icon (click)="changeFilter('DESC', 'created_at')" (mouseup)="$event.stopPropagation()"
                  name="heroArrowUp" class="text-blue-500"></ng-icon>
                }
              </div>
            </div>
          </div>
        </div>
        <div class="list-cell date">
          <div class="flex items-center justify-center">
            <p>Last update</p>
            <div class="ml-2">
              <div
                class="flex items-center justify-center p-1 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl">
                @if (updateOrder() === 'DESC') {
                <ng-icon (click)="changeFilter('ASC', 'updated_at')" (mouseup)="$event.stopPropagation()"
                  name="heroArrowDown" class="text-blue-500"></ng-icon>
                } @if (updateOrder() === 'ASC') {
                <ng-icon (click)="changeFilter('DESC', 'updated_at')" (mouseup)="$event.stopPropagation()"
                  name="heroArrowUp" class="text-blue-500"></ng-icon>
                }
              </div>
            </div>
          </div>
        </div>
        <div class="list-cell status">
          <div class="flex items-center justify-center">Status</div>
        </div>
        <div class="list-cell actions"></div>
      </div>

      <!-- List Content -->
      <cdk-virtual-scroll-viewport #listViewport id="listViewport" itemSize="50" minBufferPx="200" maxBufferPx="400"
        style="
          height: calc(100% - 56px);
          overflow: auto;
          width: 100%;
          flex-grow: 1;
        " class="h-full">
        <ng-container *cdkVirtualFor="
        let item of combinedList();
        let i = index;
        trackBy: trackByItemId
      ">
          <!-- Folder Row -->
          @if (isFolder(item)) {
          <div (dblclick)="onFolderDoubleClick(item)" (click)="onFolderClick(item)" [ngClass]="
          folderSelection().isSelected($any(item))
            ? 'folder-selected'
            : 'border-b border-primary-border dark:border-dark-primary-border hover:cursor-pointer hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover'
        "
               [attr.data-folder-id]="item.id" class="list-row">
            <div class="list-cell name" (dblclick)="$event.stopPropagation(); onFolderDoubleClick(item)">
              <app-svg-icon type="icFolderFilled" class="w-6 h-6 flex items-center justify-center mr-3"
              ></app-svg-icon>
              <p class="truncate folder-name text-base-content dark:text-dark-base-content"
                 [dxTooltip]="item.name"
                 dxTooltipPosition="above">
                {{ item.name }}
              </p>
            </div>
            <div class="list-cell date text-base-content dark:text-dark-base-content"
              [dxTooltip]="getFormattedDateTime(item, 'create')" [dxTooltipPosition]="'left'">
              {{ getFormattedDate(item, "create") }}
            </div>
            <div class="list-cell date text-base-content dark:text-dark-base-content"
              [dxTooltip]="getFormattedDateTime(item, 'update')" [dxTooltipPosition]="'left'">
              {{ getFormattedDate(item, "update") }}
            </div>
            <div class="list-cell status"></div>

            <div class="list-cell actions">
              <app-svg-icon type="icMoreHorizontal"
                            cdkOverlayOrigin
                            #trigger="cdkOverlayOrigin"
                            (click)="$event.stopPropagation(); openMenuActions(item)"
                            class="cursor-pointer w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content">
              </app-svg-icon>
              <ng-template
                cdkConnectedOverlay
                [cdkConnectedOverlayOrigin]="trigger"
                [cdkConnectedOverlayOpen]="itemSelected()?.id === item.id && itemSelected()?.type === getType(item)"
                [cdkConnectedOverlayPush]="true"
                [cdkConnectedOverlayPositions]="[
                  {
                    originX: 'start',
                    originY: 'center',
                    overlayX: 'end',
                    overlayY: 'top',
                    offsetY: 10
                  },
                  {
                    originX: 'start',
                    originY: 'center',
                    overlayX: 'end',
                    overlayY: 'bottom',
                    offsetY: 10,
                  },
                ]"
              >
                <ul class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                  (clickOutside)="itemSelected.set(undefined)"
                >
                  <li>
                    <button
                      (click)="$event.stopPropagation(); onFolderRename(item)"
                      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                    >
                      <app-svg-icon
                        type="icEdit"
                        class="w-6 h-6 flex items-center justify-center"
                      ></app-svg-icon>
                      <div class="flex items-center justify-between text-[16px] font-medium">
                        Rename
                      </div>
                    </button>
                  </li>
                  <li>
                    <button
                      (click)="$event.stopPropagation(); onFolderDelete(item)"
                      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                    >
                      <app-svg-icon type="icTrash" class="w-6 h-6 flex items-center justify-center"></app-svg-icon>
                      <div class="flex items-center justify-between text-[16px] font-medium">
                        Delete
                      </div>
                    </button>
                  </li>
                </ul>
              </ng-template>
            </div>
          </div>
          }

          <!-- File Row -->
          @if (isFile(item)) {
          <div class="list-row"
               (dblclick)="onFileDoubleClick(item); $event.stopPropagation()"
               (click)="onFileClick(item)"
               [ngClass]="
               fileSelection().isSelected($any(item))
                  ? 'file-selected'
                  : 'border-b border-primary-border dark:border-dark-primary-border hover:cursor-pointer hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover'
        " draggable="true" [attr.data-file-id]="item.id">
            <div class="list-cell name">
              <!-- File Icon -->
              @switch($any(item).ext) {
              @case ('PDF') {
              <svg class="!inline-table mr-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"
                style="width: 20px; height: 20px" fill="#f28b82">
                <path
                  d="M0 64C0 28.7 28.7 0 64 0L224 0l0 128c0 17.7 14.3 32 32 32l128 0 0 144-208 0c-35.3 0-64 28.7-64 64l0 144-48 0c-35.3 0-64-28.7-64-64L0 64zm384 64l-128 0L256 0 384 128zM176 352l32 0c30.9 0 56 25.1 56 56s-25.1 56-56 56l-16 0 0 32c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-48 0-80c0-8.8 7.2-16 16-16zm32 80c13.3 0 24-10.7 24-24s-10.7-24-24-24l-16 0 0 48 16 0zm96-80l32 0c26.5 0 48 21.5 48 48l0 64c0 26.5-21.5 48-48 48l-32 0c-8.8 0-16-7.2-16-16l0-128c0-8.8 7.2-16 16-16zm32 128c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-16 0 0 96 16 0zm80-112c0-8.8 7.2-16 16-16l48 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 32 32 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 48c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-64 0-64z" />
              </svg>
              }
              @case ('CSV') {
              <ng-icon name="faSolidFileCsv" class="!text-[#81c995] text-xl !inline-table mr-3"></ng-icon>
              }
              @case ('MD') {
              <ng-icon name="faSolidFilePen" class="!text-[#8ab4f8] !inline-table text-xl mr-3"></ng-icon>
              }
              @case ('TXT') {
              <ng-icon name="faSolidFileLines" class="!text-[#8ab4f8] text-xl !inline-table mr-3"></ng-icon>
              }
              @case ('URL') {
              <ng-icon name="faSolidLink" class="!text-[#c58af9] text-xl !inline-table mr-3"></ng-icon>
              }
              @default {
              <ng-icon name="faSolidFile" class="!text-[#6F767E] text-xl !inline-table mr-3"></ng-icon>
              }
              }
              <p class="truncate text-base-content dark:text-dark-base-content" [dxTooltip]="item.name"
                dxTooltipPosition="above">
                {{ item.name }}
              </p>
            </div>
            <div class="list-cell date text-base-content dark:text-dark-base-content"
              [dxTooltip]="getFormattedDateTime(item, 'create')" [dxTooltipPosition]="'left'">
              {{ getFormattedDate(item, "create") }}
            </div>
            <div class="list-cell date text-base-content dark:text-dark-base-content"
              [dxTooltip]="getFormattedDateTime(item, 'update')" [dxTooltipPosition]="'left'">
              {{ getFormattedDate(item, "update") }}
            </div>
            <div class="list-cell status">
              @switch ($any(item).status) {
              @case ('COMPLETED') {
              <div
                class="px-[10px] py-[6px] rounded-full bg-success dark:bg-success text-success-content dark:text-success-content text-[13px] font-semibold">
                Completed</div>
              }
              @case ('IN_PROGRESS') {
              <div
                class="px-[10px] py-[6px] rounded-full bg-info dark:bg-info text-info-content dark:text-info-content text-[13px] font-semibold">
                <div class="flex gap-2 items-center">
                  <mat-spinner diameter="16"></mat-spinner>
                  <p>Processing</p>
                </div>
              </div>
              }
              @case ('ERROR') {
              <div
                class="px-[10px] py-[6px] rounded-full bg-error dark:bg-error text-error-content dark:text-error-content text-[13px] font-semibold">
                Failed</div>
              }
              @case ('PENDING') {
              <div
                class="px-[10px] py-[6px] rounded-full bg-warning dark:bg-warning text-warning-content dark:text-warning-content text-[13px] font-semibold">
                Pending</div>
              }
              @default {
              <div
                class="px-[10px] py-[6px] rounded-full bg-base-300 dark:bg-dark-base-300 text-base-content dark:text-dark-base-content text-[13px] font-semibold">
                -</div>
              }
              }
            </div>
            <div class="list-cell actions">
              <app-svg-icon type="icMoreHorizontal"
                            cdkOverlayOrigin
                            #trigger="cdkOverlayOrigin"
                            (click)="$event.stopPropagation(); openMenuActions(item)"
                            class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer">
              </app-svg-icon>
              <ng-template
                cdkConnectedOverlay
                [cdkConnectedOverlayOrigin]="trigger"
                [cdkConnectedOverlayOpen]="itemSelected()?.id === item.id && itemSelected()?.type === getType(item)"
                [cdkConnectedOverlayPush]="true"
                [cdkConnectedOverlayPositions]="[
                  {
                    originX: 'start',
                    originY: 'center',
                    overlayX: 'end',
                    overlayY: 'top',
                    offsetY: 10
                  },
                  {
                    originX: 'start',
                    originY: 'center',
                    overlayX: 'end',
                    overlayY: 'bottom',
                    offsetY: 10,
                  },
                ]"
              >
                <ul class="w-[166px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                    (clickOutside)="itemSelected.set(undefined)"
                >
                  <li>
                    <button
                      (click)="$event.stopPropagation();onFileInfo(item)"
                      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                    >
                      <app-svg-icon type="icInfo" class="w-6 h-6 flex items-center justify-center"></app-svg-icon>
                      <div class="flex items-center justify-between text-[16px] font-medium">
                        Info
                      </div>
                    </button>
                  </li>
                  <li>
                    <button
                      (click)="$event.stopPropagation();onFileRename(item)"
                      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                    >
                      <app-svg-icon type="icEdit" class="w-6 h-6 flex items-center justify-center"></app-svg-icon>
                      <div class="flex items-center justify-between text-[16px] font-medium">
                        Rename
                      </div>
                    </button>
                  </li>
                  <li>
                    <button
                      (click)="$event.stopPropagation();onFileMove(item)"
                      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                    >
                      <app-svg-icon type="icFolderMove" class="w-6 h-6 flex items-center justify-center"></app-svg-icon>
                      <div class="flex items-center justify-between text-[16px] font-medium">
                        Move
                      </div>
                    </button>
                  </li>
                  <li>
                    <button
                      (click)="$event.stopPropagation();onFileDelete(item)"
                      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                    >
                      <app-svg-icon type="icTrash" class="w-6 h-6 flex items-center justify-center"></app-svg-icon>
                      <div class="flex items-center justify-between text-[16px] font-medium">
                        Delete
                      </div>
                    </button>
                  </li>

                </ul>
              </ng-template>
            </div>
          </div>
          }
        </ng-container>
      </cdk-virtual-scroll-viewport>
    </div>
  </div>
</div>
