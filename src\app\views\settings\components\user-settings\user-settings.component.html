<div class="settings-tab-content w-full p-6 flex flex-col space-y-6">
  <div class="flex items-center space-x-2">
    <div
      class="text-xl font-bold text-base-content dark:text-dark-base-content"
    >
      User settings
    </div>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div class="flex flex-col lg:w-3/4">
    <div
      class="w-full px-6 py-5 rounded-[16px] flex flex-col space-y-5"
      [ngClass]="
        uiStore.theme() === 'light' ? 'bg-trans-user' : 'bg-dark-trans-user'
      "
    >
      <div class="flex items-center space-x-3">
        <svg
          width="52"
          height="52"
          viewBox="0 0 52 52"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect width="52" height="52" rx="26" fill="#7F75CF" />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M26 24C27.6569 24 29 22.6569 29 21C29 19.3431 27.6569 18 26 18C24.3431 18 23 19.3431 23 21C23 22.6569 24.3431 24 26 24ZM26 26C28.7614 26 31 23.7614 31 21C31 18.2386 28.7614 16 26 16C23.2386 16 21 18.2386 21 21C21 23.7614 23.2386 26 26 26Z"
            fill="white"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M23 30C20.7909 30 19 31.7909 19 34V35C19 35.5523 18.5523 36 18 36C17.4477 36 17 35.5523 17 35V34C17 30.6863 19.6863 28 23 28H29C32.3137 28 35 30.6863 35 34V35C35 35.5523 34.5523 36 34 36C33.4477 36 33 35.5523 33 35V34C33 31.7909 31.2091 30 29 30H23Z"
            fill="white"
          />
        </svg>

        <div class="h-full flex-grow flex flex-col items-start justify-between">
          <div
            class="text-[18px] font-semibold leading-6 text-base-content dark:text-dark-base-content"
          >
            Invite team members by email
          </div>
          <div
            class="text-[14px] font-normal leading-6 text-neutral-content dark:text-dark-neutral-content"
          >
            Give your team members management access to your AI Assistant by
            assigning user roles
          </div>
        </div>
      </div>

      <div class="flex items-center space-x-3">
        <div class="w-full grid grid-cols-7 gap-3">
          <dx-form-field
            class="col-span-4"
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <input
              dxInput
              type="text"
              placeholder="<EMAIL>"
              [formControl]="email"
            />
          </dx-form-field>
          <dx-form-field
            class="col-span-3"
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <dx-select
              placeholder="Assign the role for this user"
              [formControl]="role"
            >
              @for (item of listRoleInvite; track item.value) {
              <dx-option [value]="item.value">
                {{ item.label }}
              </dx-option>
              }
            </dx-select>
          </dx-form-field>
        </div>
        <button
          dxButton="filled"
          [disabled]="!email.value || !role.value"
          [style.--dx-button-filled-container-height]="'46.5px'"
          [style.--dx-button-filled-horizontal-padding]="'24px'"
          (click)="sendMailInvite()"
        >
          Invite
        </button>
      </div>
    </div>
  </div>

  <div class="text-lg font-bold text-base-content dark:text-dark-base-content">
    User list
  </div>

  <div class="w-full lg:w-3/4 h-full flex flex-col items-stretch space-y-5">
    @for (userAI of listUserInAI(); track $index; let index = $index) {
    <div class="h-10 w-full flex items-center justify-between">
      <div class="w-full flex items-center space-x-4">
        @if(index % 2) {
        <div class="h-10 w-10 bg-u-1 rounded-full"></div>
        } @else {
        <div class="h-10 w-10 bg-u-2 rounded-full"></div>
        }
        <div
          class="text-[16px] font-semibold text-base-content dark:text-dark-base-content"
          [ngClass]="{ italic: userAI.status === STATUS.PENDING }"
        >
          {{ userAI.email ?? "-" }}
        </div>
        @if (userAI.status === STATUS.PENDING) {
        <div
          class="bg-base-100 dark:bg-dark-base-100 px-4 py-1 rounded-full text-neutral-content dark:text-dark-neutral-content"
        >
          Pending
        </div>
        }
      </div>

      <div class="h-full flex items-center justify-end space-x-3">
        <div
          class="h-10 px-2 flex items-center rounded-[6px] bg-base-300 dark:bg-dark-base-300 border border-primary-border dark:border-dark-primary-border"
        >
          @if (userAI.role === ROLE.OWNER) {
          <app-svg-icon
            type="icOwner"
            class="w-6 h-6 !text-base-content dark:!text-dark-base-content mr-2"
          ></app-svg-icon>
          <span class="text-base-content dark:text-dark-base-content"
            >Owner</span
          >
          } @if (userAI.role === ROLE.ADMIN) {
          <app-svg-icon
            type="icAdminUser"
            class="w-6 h-6 !text-base-content dark:!text-dark-base-content mr-2"
          ></app-svg-icon>
          <span class="text-base-content dark:text-dark-base-content"
            >Admin</span
          >
          } @if (userAI.role === ROLE.EDITOR) {
          <app-svg-icon
            type="icEditor"
            class="w-6 h-6 !text-base-content dark:!text-dark-base-content mr-2"
          ></app-svg-icon>
          <span class="text-base-content dark:text-dark-base-content"
            >Editor</span
          >
          } @if (userAI.role === ROLE.SUPPORT) {
          <app-svg-icon
            type="icSupport"
            class="w-6 h-6 !text-base-content dark:!text-dark-base-content mr-2"
          ></app-svg-icon>
          <span class="text-base-content dark:text-dark-base-content"
            >Support</span
          >
          }
        </div>

        @if (userAI.role !== ROLE.OWNER && userAI.email !==
        userAiStore.currentUser()?.email) {
        <div
          class="h-10 w-10 flex items-center justify-center cursor-pointer"
          (click)="deleteUserInAI(userAI)"
        >
          <ng-icon
            name="heroXMark"
            class="text-2xl !text-neutral-content dark:!text-dark-neutral-content"
          ></ng-icon>
        </div>
        } @if ( userAI.role === ROLE.OWNER || userAI.email ===
        userAiStore.currentUser()?.email) {
        <div class="w-10 h-10"></div>
        }
      </div>
    </div>
    }
  </div>
</div>
