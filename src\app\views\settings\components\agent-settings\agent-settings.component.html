<div class="settings-tab-content w-full p-6 flex flex-col space-y-6">
  <div class="flex items-center space-x-2">
    <dx-slide-toggle [formControl]="enabled"></dx-slide-toggle>
    <div
      class="text-xl font-bold text-base-content dark:text-dark-base-content"
    >
      Agent settings
    </div>
  </div>
  <hr class="text-primary-border dark:text-dark-primary-border" />
  <div class="flex flex-col lg:w-3/4">
    <dx-form-field>
      <dx-label>RAG's description</dx-label>
      <textarea
        dxInput
        rows="5"
        placeholder="Briefly describe what this RAG agent does"
        [formControl]="description"
      ></textarea>
    </dx-form-field>
    <dx-form-field>
      <dx-label>RAG's role</dx-label>
      <textarea
        dxInput
        rows="5"
        placeholder="Specify the role this RAG agent will play in your workflow or conversation"
        [formControl]="role"
      ></textarea>
    </dx-form-field>
    <dx-form-field>
      <dx-label>RAG's rule</dx-label>
      <textarea
        dxInput
        rows="5"
        placeholder="Define the specific rules or behavior guidelines that this RAG agent should follow"
        [formControl]="rule"
      ></textarea>
    </dx-form-field>
  </div>
</div>
