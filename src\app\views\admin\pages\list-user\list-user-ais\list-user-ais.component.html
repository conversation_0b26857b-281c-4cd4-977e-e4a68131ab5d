<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="text-2xl text-base-content dark:text-dark-base-content font-bold"
    >
      AI list
    </div>
    <ng-icon
      name="heroXMark"
      class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
      (click)="dialogRef.close()"
    ></ng-icon>
  </div>

  <div class="flex-1 mt-18 mb-20 flex flex-col p-6 overflow-y-auto">
    <dx-form-field class="w-full" id="search">
      <dx-label class="text-sm">Search</dx-label>
      <input
        dxInput
        [(ngModel)]="filterAI.name"
        (ngModelChange)="filterAIList()"
        [type]="'text'"
        placeholder="Search by Name"
      />
    </dx-form-field>
    <app-data-table
      class="min-h-[450px]"
      [rows]="listAI()"
      [columns]="columnsAI"
      [rowTemplate]="rowTemplate"
      [hiddenPaginator]="true"
    >
      <ng-template #rowTemplate let-row="row" let-column="column">
        <ng-container [ngSwitch]="column.columnDef">
          <ng-container *ngSwitchCase="'created_at'">
            <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
              {{ row[column.columnDef] | date : "dd/MM/yyyy HH:mm:ss" }}
            </div>
          </ng-container>
          <ng-container *ngSwitchCase="'action_edit'">
            <div
              class="flex gap-2"
              [ngStyle]="{ 'justify-content': column.align }"
            >
              <div class="cursor-pointer">
                <ng-icon
                  name="heroPencilSquare"
                  size="24"
                  class="flex items-center justify-center text-light-orange"
                  (click)="editAI(row)"
                ></ng-icon>
              </div>
            </div>
          </ng-container>
          <ng-container *ngSwitchDefault>
            <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
              {{ row[column.columnDef] }}
            </div>
          </ng-container>
        </ng-container>
      </ng-template>
    </app-data-table>
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Close</button>
  </div>
</div>
