import {Component, inject} from '@angular/core';
import {DIALOG_DATA, DxButton, DxDialogRef, DxError, DxFormField, DxInput, DxLabel, DxSnackBar} from '@dx-ui/ui';
import {SvgIconComponent} from '@shared/components';
import {FormBuilder, FormGroup, ReactiveFormsModule} from '@angular/forms';
import {CustomValidators} from '@shared/validators';
import {ROLE} from '@shared/app.constant';
import {UserAiStore} from '@core/stores';
import {SettingsService} from '@shared/services';

@Component({
  selector: 'app-delete-ai-dialog',
  imports: [
    SvgIconComponent,
    DxLabel,
    DxInput,
    ReactiveFormsModule,
    DxFormField,
    DxButton,
    DxError
  ],
  templateUrl: './delete-ai-dialog.component.html',
  styleUrl: './delete-ai-dialog.component.css'
})
export class DeleteAiDialogComponent {
  dialogRef = inject(DxDialogRef<DeleteAiDialogComponent>);
  data: {
    ai_id: string;
    name_ai: string;
    content: string;
    canDelete: boolean;
  } = inject(DIALOG_DATA);

  formGroupConfirmDeleteAI: FormGroup = inject(FormBuilder).group(
    {
      nameAI: [null],
      nameConfirm: [null],
    },
    {
      validators: [CustomValidators.mustMatch('nameAI', 'nameConfirm')],
    }
  );

  private userAiStore = inject(UserAiStore);
  private settingsService = inject(SettingsService);
  private snackBar = inject(DxSnackBar);

  ngOnInit() {
    if (this.data.canDelete) {
      this.formGroupConfirmDeleteAI.get('nameAI')?.setValue(this.data.name_ai);
    }
  }

  deleteAi() {
    if (this.userAiStore.currentAi()?.role === ROLE.OWNER) {
      this.settingsService.deleteAI().subscribe({
        next: () => {
          this.dialogRef.close(true);
          this.snackBar.open('Deleted successfully!', '', {
            panelClass: 'dx-snack-bar-success',
            horizontalPosition: 'right',
            verticalPosition: 'top',
            duration: 5000,
          });
        },
        error: (error) => {
          this.snackBar.open(error.error.detail, '', {
            panelClass: 'dx-snack-bar-error',
            horizontalPosition: 'right',
            verticalPosition: 'top',
            duration: 5000,
          });
          this.dialogRef.close();
        },
      });
    }
  }
}
