<div class="settings-tab-content w-full p-6 flex flex-col">
  <div class="flex flex-col space-y-2">
    <div
      class="text-xl font-bold text-base-content dark:text-dark-base-content"
    >
      Export settings
    </div>
    <div
      class="text-[14px] text-neutral-content dark:text-dark-neutral-content"
    >
      Configure how messages in your inbox are exported using S3 or SFTP
    </div>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border mt-6" />

  <div class="flex flex-col lg:w-3/4">
    <dx-tab-group
      class="mt-6 tab-export-settings"
      dx-stretch-tabs="false"
      [selectedIndex]="selectedTab()"
      (selectedIndexChange)="selectedTab.set($event)"
    >
      <dx-tab>
        <ng-template dx-tab-label>
          <div class="flex items-center space-x-3">
            <img src="assets/img/awss3.png" class="w-5 h-6 object-cover" />
            <div
              [ngClass]="
                selectedTab() === 0
                  ? 'text-base-content dark:text-dark-base-content'
                  : 'text-neutral-content dark:text-dark-neutral-content'
              "
            >
              AWS S3
            </div>
          </div>
        </ng-template>
        <dx-form-field>
          <dx-label>Access key ID</dx-label>
          <input
            dxInput
            placeholder="Enter access key ID"
            [formControl]="aws_access_key_id"
          />
        </dx-form-field>
        <dx-form-field>
          <dx-label>Secret access key</dx-label>
          <input
            dxInput
            placeholder="Enter secret access key"
            [formControl]="aws_secret_access_key"
          />
        </dx-form-field>
        <dx-form-field>
          <dx-label>Region name</dx-label>
          <input
            dxInput
            placeholder="Enter region name"
            [formControl]="region_name"
          />
        </dx-form-field>
        <dx-form-field>
          <dx-label>Remote path</dx-label>
          <input
            dxInput
            placeholder="Enter your remote path"
            [formControl]="remote_path"
          />
        </dx-form-field>
        <dx-form-field>
          <dx-label>Bucket's name</dx-label>
          <input
            dxInput
            placeholder="Enter your bucket's name"
            [formControl]="bucket_name"
          />
        </dx-form-field>
      </dx-tab>
      <dx-tab>
        <ng-template dx-tab-label>
          <div class="flex items-center space-x-3">
            <img src="assets/img/sftp.png" class="w-6 h-6 object-cover" />
            <div
              [ngClass]="
                selectedTab() === 1
                  ? 'text-base-content dark:text-dark-base-content'
                  : 'text-neutral-content dark:text-dark-neutral-content'
              "
            >
              SFTP
            </div>
          </div>
        </ng-template>
        <dx-form-field>
          <dx-label>Server host</dx-label>
          <input
            dxInput
            placeholder="Enter server host"
            [formControl]="server"
          />
        </dx-form-field>
        <dx-form-field>
          <dx-label>Server port</dx-label>
          <input dxInput placeholder="Enter server port" [formControl]="port" />
        </dx-form-field>
        <dx-form-field>
          <dx-label>Username</dx-label>
          <input
            dxInput
            placeholder="Enter username"
            [formControl]="username"
          />
        </dx-form-field>
        <dx-form-field>
          <dx-label>Password</dx-label>
          <input
            dxInput
            [formControl]="password"
            [type]="showPassword() ? 'text' : 'password'"
            placeholder="New Password"
          />
          <ng-icon
            dxSuffix
            [name]="showPassword() ? 'heroEyeSlash' : 'heroEye'"
            class="text-xl !text-neutral-content dark:!text-dark-neutral-content m-3 cursor-pointer"
            (click)="toggleVisibility()"
          ></ng-icon>
        </dx-form-field>
      </dx-tab>
    </dx-tab-group>
  </div>
</div>
