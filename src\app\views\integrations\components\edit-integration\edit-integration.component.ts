import { CommonModule, NgOptimizedImage } from '@angular/common';
import { Component, effect, inject, OnInit, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import {
  DIALOG_DATA,
  DxButton,
  DxDialogRef,
  DxLoadingButton,
  DxSlideToggle,
  DxSnackBar,
} from '@dx-ui/ui';
import { environment } from '@env/environment';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroDocumentDuplicate,
  heroXCircle,
  heroXMark,
} from '@ng-icons/heroicons/outline';
import { TYPE_INTEGRATION } from '@shared/app.constant';
import { IIntegration } from '@shared/models/integration.model';
import { IntegrationService, SettingsService } from '@shared/services';
// edit-integration.component.ts
import { EmbedFormComponent } from './forms/embed-form/embed-form.component';
import { FreshchatFormComponent } from './forms/freshchat-form/freshchat-form.component';
import { MessengerFormComponent } from './forms/messenger-form/messenger-form.component';
import { SlackFormComponent } from './forms/slack-form/slack-form.component';
import { WhatsappFormComponent } from './forms/whatsapp-form/whatsapp-form.component';
import { ZaloFormComponent } from './forms/zalo-form/zalo-form.component';

@Component({
  selector: 'app-edit-integration',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DxButton,
    DxLoadingButton,
    DxSlideToggle,
    NgOptimizedImage,
    NgIconsModule,
    FreshchatFormComponent,
    MessengerFormComponent,
    WhatsappFormComponent,
    SlackFormComponent,
    ZaloFormComponent,
    EmbedFormComponent,
  ],
  providers: [
    provideIcons({
      heroDocumentDuplicate,
      heroXCircle,
      heroXMark,
    }),
  ],
  templateUrl: './edit-integration.component.html',
  styleUrl: './edit-integration.component.css',
  host: {
    class: 'h-full',
  },
})
export class EditIntegrationComponent implements OnInit {
  linkWebHook = signal<string>('');
  enableForm = false;
  embedConfig: any = {};

  regexUrl =
    '^https?://(www.)?[-a-zA-Z0-9@:%._+~#=]{1,256}.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&/=]*)$';

  isAllIntegration = false;
  prefixUrl = 'https://';
  embedUrl = environment.EMBED_URL;
  baseUrl = window.location.origin;
  aiId = '';

  integrationService = inject(IntegrationService);
  data = inject<{ integration: IIntegration; apiKey: string }>(DIALOG_DATA);
  dialogRef = inject(
    DxDialogRef<EditIntegrationComponent, { confirmed: boolean }>
  );
  fb = inject(FormBuilder);
  private settingsService = inject(SettingsService);
  private snackBar = inject(DxSnackBar);
  // State mới
  formGroupControl!: FormGroup;

  toggleEnableForm = signal(false);

  constructor() {
    effect(() => {
      this.toggleEnableForm()
        ? this.formGroupControl?.enable()
        : this.formGroupControl?.disable();
    });
  }

  ngOnInit(): void {
    this.toggleEnableForm.set(this.data.integration.isEnabled ?? false);
    this.enableForm = this.data.integration.isEnabled ?? false;
    this.aiId =
      (this.data.integration as any).ai_id ??
      localStorage.getItem('current-ai-id')!;
    this.setupWebhookUrl(String(this.data.integration.type));
    this.getDetailSetting();
  }

  setupWebhookUrl(type: string) {
    const baseUrl = window.location.origin;
    const apiKey = this.data.apiKey;
    switch (type) {
      case TYPE_INTEGRATION.FRESHCHAT:
        this.linkWebHook.set(
          `${baseUrl}/api/webhook/fresh-chat?api_key=${apiKey}`
        );
        break;
      case TYPE_INTEGRATION.MESSENGER:
        this.linkWebHook.set(
          `${baseUrl}/api/webhook/facebook?api_key=${apiKey}`
        );
        break;
      case TYPE_INTEGRATION.WHATSAPP:
        this.linkWebHook.set(
          `${baseUrl}/api/webhook/whatsapp?api_key=${apiKey}`
        );
        break;
      case TYPE_INTEGRATION.SLACK:
        this.linkWebHook.set(`${baseUrl}/api/webhook/slack?api_key=${apiKey}`);
        break;
      case TYPE_INTEGRATION.ZALO:
        this.linkWebHook.set(`${baseUrl}/api/webhook/zalo?api_key=${apiKey}`);
        break;
      default:
        this.linkWebHook.set('');
        break;
    }
  }

  onFormChange(formGroup: FormGroup) {
    this.formGroupControl = formGroup;
  }

  onEmbedConfigChange(config: any) {
    this.embedConfig = config;
  }

  saveConfig() {
    let config: any;
    if (this.data.integration.type === TYPE_INTEGRATION.EMBED) {
      config = this.embedConfig;
    } else {
      if (!this.formGroupControl || this.formGroupControl.invalid) {
        this.showSnackBar('Form invalid!', 'error');
        return;
      }
      config = this.formGroupControl.value;
    }
    this.createOrUpdateIntegration(this.data.integration, config);
  }

  getDetailSetting(): void {
    this.settingsService.getDetailSetting().subscribe((res) => {
      if (res) {
        this.aiId = res.id;
        const plan_features: [{ name: string; value: string }] =
          res.plan_features;
        this.isAllIntegration = plan_features.some(
          (component) =>
            component.name === 'All Integrations' && component.value === '1'
        );
      }
    });
  }

  close(confirmed?: boolean) {
    this.dialogRef.close({ confirmed: confirmed });
  }

  async copyText(content: string) {
    try {
      await navigator.clipboard.writeText(content);
      this.showSnackBar('Copied successfully!', 'success');
    } catch {
      this.showSnackBar('Copy failed!', 'error');
    }
  }

  toggleEnable() {
    this.data.integration.isEnabled = !this.data.integration.isEnabled;
    this.toggleEnableForm.update((value) => !value);
    this.formGroupControl.markAsDirty();
    /*this.enableIntegration(
      this.data.integration.isEnabled,
      this.data.integration
    );*/
  }

  createOrUpdateIntegration(data: any, config: any) {
    let body: any = {
      ai_id: data.ai_id ?? localStorage.getItem('current-ai-id'),
      platform_name: data.platform_name ?? data.type,
      config: JSON.stringify(config),
    };
    if (data.id) {
      body.id = data.id;
      this.integrationService.updateIntegration(body).subscribe({
        next: (res) => {
          this.showSnackBar('Updated successfully', 'success');
          this.close(true);
        },
      });
    } else {
      this.integrationService.createIntegration(body).subscribe({
        next: (res) => {
          this.showSnackBar('Created successfully', 'success');
          this.close(true);
        },
      });
    }
  }

  private enableIntegration(isEnabled: boolean, integration: IIntegration) {
    const value = isEnabled;
    const data = integration;
    if (data.id) {
      this.integrationService
        .updateIntegration({
          id: data.id,
          ai_id: this.aiId,
          platform_name: data.type,
          config: JSON.stringify({
            ...data.config,
            isEnabled: value,
          }),
        })
        .subscribe({
          next: (res) => {
            if (res) {
              this.showSnackBar(
                'Changed status integration successfully!',
                'success'
              );
              this.close(true);
            }
          },
          error: (err) => {
            this.showSnackBar('Change status integration failed!', 'error');
            // console.error(err);
          },
        });
    } else {
      this.integrationService
        .createIntegration({
          ai_id: this.aiId,
          platform_name: data.type,
          config: JSON.stringify({
            ...data.config,
            isEnabled: value,
          }),
        })
        .subscribe({
          next: (res) => {
            data.id = res.id;
            if (res) {
              this.showSnackBar(
                'Initialized integration successfully!',
                'success'
              );
            }
          },
          error: (err) => {
            this.showSnackBar('Initialize integration failed!', 'error');
            // console.error(err);
          },
        });
    }
  }

  showSnackBar(
    message: string,
    type: 'success' | 'error' | 'info' = 'error',
    duration: number = 3000
  ): void {
    const panelClass = {
      success: ['dx-snack-bar-success'],
      error: ['dx-snack-bar-error'],
      info: ['dx-snack-bar-info'],
    };

    this.snackBar.open(message, '', {
      duration: duration,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: panelClass[type],
    });
  }
}
