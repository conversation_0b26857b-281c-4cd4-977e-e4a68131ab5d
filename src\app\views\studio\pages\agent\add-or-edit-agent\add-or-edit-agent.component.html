<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      {{ data.isEdit ? "Edit Agent" : "Create Agent" }}
    </div>
    <div class="flex items-center justify-end space-x-4">
      <ng-icon
        name="heroXMark"
        class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></ng-icon>
    </div>
  </div>

  <!-- Dialog Content -->
  <div class="flex-1 overflow-y-auto mt-18 mb-20">
    <div
      [formGroup]="formGroup"
      class="px-6 pt-6 pb-[3px] flex flex-col space-x-4"
    >
      <dx-form-field class="w-full" id="name">
        <dx-label class="text-sm">Name</dx-label>
        <input
          dxInput
          formControlName="name"
          [type]="'text'"
          placeholder="Agent name"
        />
        @if (formGroup.get('name')?.errors && (formGroup.get('name')?.touched ||
        formGroup.get('name')?.dirty)) {
        <dx-error>Name is required.</dx-error>
        }
      </dx-form-field>
      <dx-form-field class="w-full" id="description">
        <dx-label class="text-sm">Description</dx-label>
        <textarea
          dxInput
          rows="3"
          autoResize
          formControlName="description"
          [type]="'text'"
          placeholder="Description"
        >
        </textarea>
        @if (formGroup.get('description')?.errors &&
        (formGroup.get('description')?.touched ||
        formGroup.get('description')?.dirty)) {
        <dx-error>Description is required.</dx-error>
        }
      </dx-form-field>
      <dx-form-field class="w-full" id="instruction">
        <dx-label class="text-sm">Instruction</dx-label>
        <textarea
          dxInput
          rows="3"
          autoResize
          formControlName="instruction"
          [type]="'text'"
          placeholder="Instruction"
        >
        </textarea>
        @if (formGroup.get('instruction')?.errors &&
        (formGroup.get('instruction')?.touched ||
        formGroup.get('instruction')?.dirty)) {
        <dx-error>Instruction is required.</dx-error>
        }
      </dx-form-field>
      <dx-form-field class="w-full" id="rule">
        <dx-label class="text-sm">Rule</dx-label>
        <textarea
          dxInput
          rows="3"
          autoResize
          formControlName="rule"
          [type]="'text'"
          placeholder="Rule"
        >
        </textarea>
        @if (formGroup.get('rule')?.errors && (formGroup.get('rule')?.touched ||
        formGroup.get('rule')?.dirty)) {
        <dx-error>Rule is required.</dx-error>
        }
      </dx-form-field>
      <dx-form-field class="w-full" id="selectedModel">
        <dx-label class="text-sm">AI Model</dx-label>
        <dx-select formControlName="selectedModel">
          @for (model of aiModels(); track $index) {
          <dx-option [value]="model.value">
            {{ model.label }}
          </dx-option>
          }
        </dx-select>
      </dx-form-field>
      <dx-form-field class="w-full" id="temperature">
        <dx-label class="text-sm">Temperature</dx-label>
        <input
          dxInput
          formControlName="temperature"
          [type]="'number'"
          placeholder="Temperature"
          min="0"
          max="1"
          step="0.1"
        />
        @if (formGroup.get('temperature')?.errors &&
        (formGroup.get('temperature')?.touched ||
        formGroup.get('temperature')?.dirty)) {
        <dx-error>Temperature is required.</dx-error>
        }
      </dx-form-field>
    </div>
  </div>

  <!-- Dialog Footer -->
  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Cancel</button>
    <button
      dxLoadingButton="filled"
      [loading]="isSubmitting()"
      [disabled]="formGroup.invalid"
      (click)="onSave(data.isEdit)"
    >
      {{ data.isEdit ? "Update" : "Create" }}
    </button>
  </div>
</div>
