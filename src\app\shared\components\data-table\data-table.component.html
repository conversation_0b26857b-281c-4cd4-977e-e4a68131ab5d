<div
  class="tb-hover h-full relative bg-transparent data-table-container overflow-hidden"
>
  <div class="absolute inset-0 overflow-x-auto overflow-y-auto"
       [ngClass]="{'mb-20': !hiddenPaginator}"
  >
    <table class="table-custom w-full bg-transparent border-collapse">
      <thead>
        <tr
          class="bg-transparent border-b border-primary-border dark:border-dark-primary-border"
        >
          @for (column of columns; track $index){
          <th
            class="overflow-auto text-light-text dark:text-dark-text bg-transparent py-4 px-4 font-medium"
            [style.width]="calcColumnWidth(column)"
            [ngStyle]="{
              'max-width': column.maxWidth,
              'min-width': column.minWidth
            }"
          >
            <ng-container [ngSwitch]="column.columnDef">
              <ng-container *ngSwitchCase="'index'">
                <div class="flex items-center justify-center">
                  <span
                    class="font-bold text-light-text dark:text-dark-text text-sm"
                    >{{ column.headerName }}</span
                  >
                </div>
              </ng-container>

              <ng-container *ngSwitchDefault>
                <div
                  class="flex items-center"
                  [ngStyle]="{ 'justify-content': column.alignHeader }"
                >
                  <span
                    class="font-bold text-light-text dark:text-dark-text text-sm"
                    >{{ column.headerName }}</span
                  >
                </div>
              </ng-container>
            </ng-container>
          </th>
          }
        </tr>
      </thead>
      <tbody>
        @for (row of rows; track $index){
        <tr
          class="bg-transparent hover:bg-light-hover dark:hover:bg-dark-hover transition-colors duration-150 border-b border-primary-border dark:border-dark-primary-border"
        >
          <ng-container *ngFor="let column of columns">
            <td
              class="text-light-text dark:text-dark-text bg-transparent py-3.5 px-4 border-b-0"
            >
              <ng-container [ngSwitch]="column.columnDef">
                <ng-container *ngSwitchCase="'index'">
                  <div class="flex items-center justify-center">
                    <span
                      class="text-light-text/70 dark:text-dark-text/70 text-sm"
                      >{{ getRowIndex(row) + 1 }}</span
                    >
                  </div>
                </ng-container>

                <ng-container *ngSwitchCase="'action'">
                  <ng-container
                    [ngTemplateOutlet]="actionTemplate || defaultActions"
                    [ngTemplateOutletContext]="{ $implicit: row }"
                  ></ng-container>
                  <ng-template #defaultActions>
                    <div class="flex justify-center items-center">
                      <button
                        class="flex items-center justify-center"
                        (click)="row.isActions = !row.isActions"
                        cdkOverlayOrigin
                        #trigger="cdkOverlayOrigin"
                        dxTooltip="Actions"
                        dxTooltipPosition="above"
                      >
                        <ng-icon name="heroEllipsisHorizontalMini" class="text-2xl !text-neutral-content dark:!text-dark-neutral-content"></ng-icon>
                      </button>
                      <ng-template
                        cdkConnectedOverlay
                        [cdkConnectedOverlayOrigin]="trigger"
                        [cdkConnectedOverlayOpen]="row.isActions"
                        [cdkConnectedOverlayPush]="true"
                        [cdkConnectedOverlayPositions]="[
                          {
                            originX: 'start',
                            originY: 'center',
                            overlayX: 'end',
                            overlayY: 'top',
                            offsetX: -5
                          },
                          {
                            originX: 'start',
                            originY: 'center',
                            overlayX: 'end',
                            overlayY: 'bottom',
                            offsetX: -5
                          }
                        ]"
                      >
                        <ul
                          class="w-[245px] p-2 rounded-xl !text-base-content dark:!text-dark-base-content shadow-md flex flex-col gap-y-1 action-dropdown border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                          (clickOutside)="
                            row.isActions = false; row.isContextMenu = false
                          "
                        >
                          <ng-container
                            *ngTemplateOutlet="
                              action;
                              context: { $implicit: row }
                            "
                          ></ng-container>
                        </ul>
                      </ng-template>
                    </div>

                    <ng-template #action let-element>
                      <ng-container *ngFor="let actionConfig of column.actions">
                        <ng-container
                          *ngTemplateOutlet="
                            actionBtn;
                            context: {
                              $implicit: {
                                case: actionConfig.case,
                                condition: isActionConditionMet(
                                  actionConfig,
                                  element
                                ),
                                name: actionConfig.name,
                                title: actionConfig.title,
                                class: actionConfig.class
                              }
                            }
                          "
                        ></ng-container>
                      </ng-container>
                    </ng-template>

                    <ng-template #actionBtn let-attr>
                      <li *ngIf="attr.condition" class="w-full"
                          [dxTooltip]="attr.title"
                          dxTooltipPosition="above">
                        <div
                          class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                          (click)="handleAction(attr.case, row)"
                           [class]="[attr.class?? '']"
                        >
                          <ng-icon *ngIf="attr.name.includes('hero')"
                                   [name]="attr.name"
                                   [class]=" 'text-2xl ' + attr.class"
                          ></ng-icon>
                          <div class="flex items-center justify-between text-[16px] font-medium">{{ attr.title }}</div>
                        </div>
                      </li>

                      <li *ngIf="!attr.condition" class="w-full">
                        <div
                          class="flex items-center gap-x-2 p-2.5 w-full rounded opacity-50 cursor-not-allowed"
                          [dxTooltip]="attr.title"
                          dxTooltipPosition="above"
                        >
                          <ng-icon
                            [name]="attr.name"
                            class="text-xl text-[#d1d1d1]"
                          ></ng-icon>
                          <span
                            class="text-sm text-light-text dark:text-dark-text"
                            >{{ attr.title }}</span
                          >
                        </div>
                      </li>
                    </ng-template>
                  </ng-template>
                </ng-container>

                <ng-container *ngSwitchDefault>
                  <ng-container
                    [ngTemplateOutlet]="rowTemplate || defaultRowTemplate"
                    [ngTemplateOutletContext]="{
                      row: row,
                      column: column,
                      index: getRowIndex(row)
                    }"
                  >
                  </ng-container>
                  <ng-template #defaultRowTemplate>
                    <ng-container [ngSwitch]="column.columnDef">
                      <ng-container *ngSwitchCase="'index'">
                        <div class="flex items-center justify-center">
                          <span>{{ getRowIndex(row) + 1 }}</span>
                        </div>
                      </ng-container>
                      <!-- Default rendering for columns -->
                      <ng-container
                        *ngIf="
                          column.columnDef &&
                          column.columnDef !== 'index' &&
                          column.columnDef !== 'action'
                        "
                      >
                        <div
                          class="text-light-text dark:text-dark-text px-0 overflow-hidden text-ellipsis whitespace-nowrap"
                          [dxTooltip]="getOriginalText(row, column)"
                          dxTooltipPosition="below"
                          [innerHTML]="
                            column.cellRenderer
                              ? column.cellRenderer(row)
                              : row && column.columnDef
                              ? row[column.columnDef]
                              : ''
                          "
                        ></div>
                      </ng-container>
                    </ng-container>
                  </ng-template>
                </ng-container>
              </ng-container>
            </td>
          </ng-container>
        </tr>
        } @empty {
        <tr
          class="border-b border-primary-border dark:border-dark-primary-border"
        >
          <td
            [attr.colspan]="columns.length"
            class="text-light-text/60 dark:text-dark-text/60 text-center bg-transparent py-8 italic text-sm"
          >
            No data available
          </td>
        </tr>
        }
      </tbody>
    </table>
  </div>

  <!-- Custom Pagination - Always enabled -->
  <div
    class="absolute left-0 bottom-0 right-0 z-2 w-full bg-transparent text-light-text dark:text-dark-text"
  >
    <nav
      class="flex items-center flex-col flex-wrap md:flex-row justify-between pt-4"
      aria-label="Table navigation"
    >
      @if (!hiddenPaginator){

        <div class="flex items-center mb-4 md:mb-0 space-x-2">
        <span
          class="text-sm font-normal text-light-text/70 dark:text-dark-text/70"
          >Rows per page:</span
        >
        <div class="relative">
          <select
            class="bg-transparent text-light-text dark:text-dark-text border border-primary-border dark:border-dark-primary-border rounded-md py-1.5 pl-3 pr-8 focus:ring-1 focus:ring-light-primary dark:focus:ring-dark-primary text-sm appearance-none"
            [ngModel]="limit"
            (ngModelChange)="onPageSizeChange($event)"
          >
            <option *ngFor="let size of pageSizeOptions" [value]="size">
              {{ size }}
            </option>
          </select>
          <div
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-light-text/50 dark:text-dark-text/50"
          >
            <ng-icon name="heroChevronDownMini" class="text-sm"></ng-icon>
          </div>
        </div>
      </div>

      <span
        class="text-sm font-normal text-light-text/70 dark:text-dark-text/70 mb-4 md:mb-0 block w-full md:inline md:w-auto"
      >
        Showing
        <span class="font-medium text-light-text dark:text-dark-text"
          >{{ pageIndex * limit + 1 }} -{{
            Math.min(pageIndex * limit + rows.length, count)
          }}</span
        >
        of
        <span class="font-medium text-light-text dark:text-dark-text">{{
          count
        }}</span>
      </span>

      <div class="pagination-container">
        <div class="flex items-center justify-between space-x-2">
          <button
            class="flex items-center justify-between justify-center rounded-xl p-2 bg-base-400 dark:bg-dark-base-400 hover:bg-base-500 dark:hover:bg-dark-base-500 border border-primary-border dark:border-dark-primary-border"
            [disabled]="pageIndex === 0"
            [ngClass]="{ 'opacity-50 cursor-not-allowed': pageIndex === 0 }"
            (click)="onPageChange(0)"
          >
            <ng-icon
              name="heroChevronDoubleLeftMini"
              class="text-2xl"
              dxTooltip="First page"
              dxTooltipPosition="above"
            ></ng-icon>
          </button>

          <button
            class="flex items-center justify-between justify-center rounded-xl p-2 bg-base-400 dark:bg-dark-base-400 hover:bg-base-500 dark:hover:bg-dark-base-500 border border-primary-border dark:border-dark-primary-border"
            [disabled]="pageIndex === 0"
            [ngClass]="{ 'opacity-50 cursor-not-allowed': pageIndex === 0 }"
            (click)="onPageChange(pageIndex - 1)"
          >
            <ng-icon
              name="heroChevronLeftMini"
              class="text-2xl"
              dxTooltip="Next page"
              dxTooltipPosition="above"
            ></ng-icon>
          </button>
          <div class="relative">
            <select
              class="bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border rounded-xl rounded-xl py-2.5 pl-3 pr-8 text-sm appearance-none"
              [ngModel]="pageIndex"
              (ngModelChange)="onPageChange($event - 1)"
            >
              <option [ngValue]="pageIndex" hidden>
                {{ pageIndex + 1 }}/{{ Math.ceil(count / limit) }}
              </option>
              <option *ngFor="let page of getPageNumbers()" [value]="page">
                {{ page }}
              </option>
            </select>
            <div
              class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-light-text/50 dark:text-dark-text/50"
            >
              <ng-icon
                name="heroChevronDownMini"
                class="text-2xl"
                [dxTooltip]="'Go to page'"
                dxTooltipPosition="above"
              ></ng-icon>
            </div>
          </div>
          <button
            class="flex items-center justify-between justify-center rounded-xl p-2 bg-base-400 dark:bg-dark-base-400 hover:bg-base-500 dark:hover:bg-dark-base-500 border border-primary-border dark:border-dark-primary-border"
            [disabled]="(pageIndex + 1) * limit >= count"
            [ngClass]="{
              'opacity-50 cursor-not-allowed': (pageIndex + 1) * limit >= count
            }"
            (click)="onPageChange(pageIndex + 1)"
          >
            <ng-icon
              name="heroChevronRightMini"
              class="text-2xl"
              dxTooltip="Previous page"
              dxTooltipPosition="above"
            ></ng-icon>
          </button>
          <button
            class="flex items-center justify-between justify-center rounded-xl p-2 bg-base-400 dark:bg-dark-base-400 hover:bg-base-500 dark:hover:bg-dark-base-500 border border-primary-border dark:border-dark-primary-border"
            [disabled]="(pageIndex + 1) * limit >= count"
            [ngClass]="{
              'opacity-50 cursor-not-allowed': (pageIndex + 1) * limit >= count
            }"
            (click)="onPageChange(Math.ceil(count / limit) - 1)"
          >
            <ng-icon
              name="heroChevronDoubleRightMini"
              class="text-2xl"
              dxTooltip="Last page"
              dxTooltipPosition="above"
            ></ng-icon>
          </button>
        </div>
      </div>
      }
    </nav>
  </div>
</div>
