import { Routes } from '@angular/router';
import { AUTH_PATH } from '@core/constants';
import { LoginComponent } from './pages/login/login.component';

export const AUTH_ROUTES: Routes = [
  {
    path: '',
    redirectTo: AUTH_PATH.LOGIN,
    pathMatch: 'full',
  },
  {
    path: AUTH_PATH.LOGIN,
    component: LoginComponent,
  },
  {
    path: AUTH_PATH.REGISTER,
    loadComponent: () =>
      import('./pages/register/register.component').then(
        (m) => m.RegisterComponent
      ),
  },
  {
    path: `${AUTH_PATH.ACTIVE}/:token`,
    loadComponent: () =>
      import('./pages/active-account/active-account.component').then((m) => m.ActiveAccountComponent),
  },
  {
    path: 'invite-join/:invite_token',
    loadComponent: () =>
      import('../invite-join/invite-join.component').then(
        (m) => m.InviteJoinComponent
      ),
  },
  {
    path: '**',
    redirectTo: AUTH_PATH.LOGIN,
    pathMatch: 'full',
  },
];
