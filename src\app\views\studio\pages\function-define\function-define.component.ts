import { NgClass } from '@angular/common';
import {
  Component,
  computed,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { StudioStore } from '@core/stores';
import {
  DxButton,
  DxDialog,
  DxFormField,
  DxInput,
  DxLoadingButton,
  DxPrefix,
  DxSnackBar,
} from '@dx-ui/ui';
import { provideIcons } from '@ng-icons/core';
import {
  heroPencilSquareMini,
  heroPlusMini,
  heroTrashMini,
} from '@ng-icons/heroicons/mini';
import { heroDocumentDuplicate } from '@ng-icons/heroicons/outline';
import { heroPlusSolid, heroXMarkSolid } from '@ng-icons/heroicons/solid';
import { STUDIO_STATUS } from '@shared/app.constant';
import {
  ConfirmDialogComponent,
  FlowEnvSelectComponent,
  SvgIconComponent,
} from '@shared/components';
import { IFunction, IFunctionFilter } from '@shared/models';
import { FunctionService } from '@shared/services';
import { ThemeService } from '@shared/services/theme.service';
import { AddOrEditFunctionComponent } from '@views/studio/pages/function-define/add-or-edit-function/add-or-edit-function.component';
import { EditorComponent } from 'ngx-monaco-editor-v2';
import {Subscription, Subject, debounceTime} from 'rxjs';

@Component({
  selector: 'app-function-define',
  standalone: true,
  imports: [
    EditorComponent,
    FormsModule,
    NgClass,
    ReactiveFormsModule,
    DxButton,
    DxFormField,
    DxInput,
    DxPrefix,
    SvgIconComponent,
    DxLoadingButton,
    FlowEnvSelectComponent,
  ],
  templateUrl: './function-define.component.html',
  styleUrl: './function-define.component.css',
  providers: [
    provideIcons({
      heroDocumentDuplicate,
      heroPencilSquareMini,
      heroTrashMini,
      heroPlusMini,
      heroXMarkSolid,
      heroPlusSolid,
    }),
  ],
})
export class FunctionDefineComponent implements OnInit, OnDestroy {
  studioStore = inject(StudioStore);

  formGroup: FormGroup = new FormGroup({});
  functionSelectedId = signal<number | undefined>(undefined);
  functionSelected = signal<IFunction | undefined>(undefined);
  listFunction = signal<IFunction[]>([]);
  listFunctionTab = signal<IFunction[]>([]);
  function = signal<IFunction | undefined>(undefined);
  isSubmitting = signal(false);
  searchModel: IFunctionFilter = {
    key_word: '',
  };
  createOrUpdateFunctionDialogRef: any;
  formGroupFunctionDetail!: FormGroup;
  tabIndex: number = 0;
  code: string = 'def function_action:\n  pass';

  private subcription?: Subscription;

  themeService = inject(ThemeService);

  studioStoreStatus = computed(() => this.studioStore.status());
  readonly editorOptions = computed(() => ({
    theme: this.themeService.theme() === 'dark' ? 'vs-dark' : 'vs-light',
    language: 'python',
    automaticLayout: true,
    readOnly: this.studioStore.status() === STUDIO_STATUS.LIVE,
  }));
  searchSubject = new Subject<string>();

  private functionService: FunctionService = inject(FunctionService);
  private dialogService = inject(DxDialog);
  private fb = inject(FormBuilder);
  private snackBar = inject(DxSnackBar);
  readonly STUDIO_STATUS = STUDIO_STATUS;

  ngOnInit(): void {
    this.getListFunction();
    this.formGroupFunctionDetail = this.fb.group({
      id: [null],
      ai_id: [null],
      name: [null, Validators.required],
      code: [null],
    });
    this.searchSubject.pipe(debounceTime(300),).subscribe(() => this.doSearch());

    /*this.subcription = this.formGroupFunctionDetail.valueChanges.subscribe({
      next: (formValue) => {
        this.setDirtyState(formValue.id, true);
      },
    });*/
  }

  ngOnDestroy(): void {
    if (this.subcription) this.subcription.unsubscribe();
  }

  doSearch(event?: any) {
    this.getListFunction();
  }

  onChangeCode() {
    if (this.functionSelected()?.code !== this.code) {
      this.setDirtyState(this.functionSelectedId(), true);
    } else {
      this.clearDirtyState();
    }
  }

  createFunction() {
    const createFunctionAction = () => {
      this.dialogService
        .open(AddOrEditFunctionComponent, {
          data: {
            function: {
              ai_id: '',
            },
            isCreate: true,
          },
          width: '40vw',
        })
        .afterClosed()
        .subscribe((value) => {
          if (value && typeof value === 'number') {
            this.getListFunction(false, value);
          }
        });
    };

    if (this.isOneFunctionDirty) {
      this.handleConfirmDirtyFunction((value: any) => {
        if (!!value) {
          this.clearDirtyState();
          createFunctionAction();
        }
      }, false);
      return;
    }
    createFunctionAction();
  }

  editFunction(func: IFunction, evt?: any) {
    evt && evt.stopPropagation();

    const editFunctionAction = () => {
      this.dialogService
        .open(AddOrEditFunctionComponent, {
          data: {
            function: {
              id: func.id,
              ai_id: '',
              name: func.name,
              code: func.code,
            },
            isCreate: false,
          },
          width: '40vw',
        })
        .afterClosed()
        .subscribe((value) => {
          if (value && typeof value === 'number') {
            this.getListFunction(true, value);
          }
        });
    };

    if (this.isOneFunctionDirty) {
      this.handleConfirmDirtyFunction((value: any) => {
        if (!!value) {
          this.clearDirtyState();
          editFunctionAction();
        }
      }, false);
      return;
    }
    editFunctionAction();
  }

  saveFunction(fromDetail: boolean = false) {
    this.isSubmitting.set(true);
    let body: IFunction;

    body = {
      ...this.formGroupFunctionDetail.value,
      name: this.formGroupFunctionDetail.value?.name?.includes('Function_')
        ? this.formGroupFunctionDetail.value?.name?.split('Function_')[1]
        : this.formGroupFunctionDetail?.value?.name,
      code: this.code,
    };

    if (body.id) {
      this.functionService.update(body).subscribe({
        next: () => {
          this.showSnackBar('Update successfully', 'success');
          this.clearDirtyState();
          this.getListFunction(true);
          this.isSubmitting.set(false);
        },

        error: (err) => {
          this.isSubmitting.set(false);
        },
      });
    } else {
      this.functionService.insert({ ...body, id: null }).subscribe({
        next: () => {
          this.showSnackBar('Create successfully', 'success');
          this.getListFunction();
          this.code = `def ${body.name}:\n  pass`;
          this.isSubmitting.set(false);
        },
        error: (err) => {
          this.isSubmitting.set(false);
        },
      });
    }
  }

  deleteFunction(func: IFunction, evt: any) {
    evt.stopPropagation();

    const deleteFuncAction = () => {
      this.dialogService
        .open(ConfirmDialogComponent, {
          data: {
            title: 'Delete this api',
            content: 'Are you sure delete this api?',
            isDelete: true,
          },
          width: '300px',
        })
        .afterClosed()
        .subscribe((value: any) => {
          if (!!value) {
            if (func && func.id) {
              this.functionService.delete(func.id).subscribe({
                next: () => {
                  this.listFunctionTab().splice(
                    this.listFunctionTab().findIndex((v) => v.id === func.id),
                    1
                  );
                  this.functionSelectedId.set(undefined);
                  this.functionSelected.set(undefined);
                  this.showSnackBar('Delete successfully', 'success');
                  this.getListFunction();
                },
                error: () => {
                  this.showSnackBar('Failed to delete api', 'error');
                },
              });
            }
          }
        });
    };

    if (this.isOneFunctionDirty) {
      this.handleConfirmDirtyFunction((value: any) => {
        if (!!value) {
          this.clearDirtyState();
          deleteFuncAction();
        }
      });
      return;
    }
    deleteFuncAction();
  }

  closeDialogCreateOrUpdateFunction() {
    this.createOrUpdateFunctionDialogRef.close();
  }

  private setDirtyState(function_id?: number, state?: boolean) {
    if (!function_id) return;

    this.functionSelectedId.set(function_id);

    const tab = this.listFunctionTab().find((v) => v.id === function_id);
    if (tab) {
      tab.isDirty = state;
    }

    const list = this.listFunction().find((v) => v.id === function_id);
    if (list) {
      list.isDirty = state;
    }

    if (
      this.functionSelected() &&
      this.functionSelected()?.id === function_id
    ) {
      this.functionSelected.set({
        ...this.functionSelected()!,
        isDirty: state,
      });
    }
  }

  selectFunction(function_id?: number | null) {
    if (!function_id) return;
    if (this.functionSelectedId() === function_id) return;

    if (this.isOneFunctionDirty) {
      this.handleConfirmDirtyFunction((value: any) => {
        if (!!value) {
          this.clearDirtyState();
          this.selectFunctionAction(function_id);
        }
      }, false);
      return;
    }

    this.selectFunctionAction(function_id);
  }

  removeFunctionSelectedTab(index: number, evt: any) {
    evt.stopPropagation();
    if (this.listFunctionTab()[index].isDirty) {
      return;
    }
    if (this.listFunction().length === 1 || this.listFunctionTab().length === 1)
      return;
    const removeFunctionSelectedTabAction = () => {
      this.listFunctionTab().splice(index, 1);
      if (this.listFunctionTab().length > 0) {
        this.selectFunction(
          this.listFunctionTab()[this.listFunctionTab().length - 1]?.id
        );
      }
    };

    if (this.isOneFunctionDirty) {
      this.handleConfirmDirtyFunction((value: any) => {
        if (!!value) {
          this.clearDirtyState();
          removeFunctionSelectedTabAction();
        }
      }, false);
      return;
    }
    removeFunctionSelectedTabAction();
  }

  private getListFunction(isEdit: boolean = false, id?: number) {
    this.functionService.findBySearch(this.searchModel).subscribe({
      next: (res) => {
        this.listFunction.set(res && res.length ? res : []);
        if (!this.listFunction().length) return;
        const selectResId = res[0].id;
        if (this.listFunctionTab().length === 0) {
          this.listFunctionTab.update((lfn) => [
            ...lfn,
            { ...res[0], isDirty: false },
          ]);
        }
        if (!isEdit) {
          this.selectFunction(id || selectResId);
        } else {
          if (id) {
            this.listFunctionTab.update((lft) => {
              const newFunction = this.listFunction().find((v) => v.id === id);
              const index = lft.findIndex((v) => v.id === id);
              if (index !== -1) {
                lft[index] = newFunction!;
              }
              return lft;
            });
          }
          this.selectFunctionAction(this.functionSelectedId());
          this.functionSelected.set(
            this.listFunction().find((v) => v.id === this.functionSelectedId())
          );
          /*const indexFunctionSelected = this.listFunctionTab().findIndex(
            (v) => v.id === selectResId
          );
          if (indexFunctionSelected !== -1 && this.functionSelected()) {
            this.listFunctionTab.update((list) => {
              list[indexFunctionSelected] = this.functionSelected()!;
              return list;
            });
          }*/
        }
      },
    });
  }

  private get isOneFunctionDirty() {
    return this.functionSelected()?.isDirty;
    /*const indexDirtyTab = this.listFunctionTab().findIndex((v) => v.isDirty);
    const indexDirtyList = this.listFunction().findIndex((v) => v.isDirty);
    return Boolean(indexDirtyTab > -1 || indexDirtyList > -1);*/
  }

  private handleConfirmDirtyFunction(callback?: any, isDelete: boolean = true) {
    return this.dialogService
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Confirm',
          content:
            'You haven’t saved your changes. Are you sure you want to continue?',
          isDelete: isDelete,
          confirmText: isDelete ? 'Delete' : 'Continue',
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value : any) => {if (!!value){callback && callback(value);}});
  }

  private updateDirtyState(function_id?: number, state?: boolean) {
    const tab = this.listFunctionTab().find((v) => v.id === function_id);
    const list = this.listFunction().find((v) => v.id === function_id);
    if (tab) tab.isDirty = state;
    if (list) list.isDirty = state;
    if (this.functionSelected && this.functionSelected()?.id === function_id) {
      const func = { ...this.functionSelected()!, isDirty: state };
      this.functionSelected.set(func);
    }
  }

  private clearDirtyState() {
    const indexDirtyTab = this.listFunctionTab().findIndex((v) => v.isDirty);
    const indexDirtyList = this.listFunction().findIndex((v) => v.isDirty);
    if (indexDirtyTab !== -1) {
      this.listFunctionTab.update((fn) => {
        fn[indexDirtyTab].isDirty = false;
        return fn;
      });
    }
    if (indexDirtyList !== -1) {
      this.listFunction.update((fn) => {
        fn[indexDirtyList].isDirty = false;
        return fn;
      });
    }
    if (this.functionSelected && this.functionSelected()?.isDirty) {
      this.functionSelected.update((fn) => {
        fn!.isDirty = false;
        return fn;
      });
    }
  }

  private selectFunctionAction(function_id?: number) {
    this.tabIndex = 0;
    this.functionSelectedId.set(function_id);
    this.functionSelected.set(
      this.listFunction().find((v) => v.id === function_id)
    );
    const indexFunctionSelected = this.listFunctionTab().findIndex(
      (v) => v.id === function_id
    );
    if (indexFunctionSelected < 0 && this.functionSelected) {
      this.listFunctionTab.update((fns) => {
        fns.push(this.functionSelected()!);
        return fns;
      });
    }

    this.code = this.functionSelected()?.code ?? '';

    if (this.functionSelected()) {
      this.formGroupFunctionDetail.patchValue(
        { ...this.functionSelected() },
        {
          emitEvent: false,
        }
      );
    }
  }

  private showSnackBar(message: string, type: 'success' | 'error' | 'info') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }
}
