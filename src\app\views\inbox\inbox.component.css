/* Hero chat list styles */
.hero-chat-list {
  display: none;
}

.inbox-wrapper {
  /*height: calc(100vh - 6rem);*/
  height: 90.9vh;
}

.inbox-wrapper .inbox-header {
  /*background-color: var(--color-light-background);*/
  background-color: transparent;
}

.dark .inbox-wrapper .inbox-header {
  background-color: var(--color-dark-background);
}

.inbox-wrapper .inbox-header .inbox-info {
  margin-right: 1rem;
  margin-bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.5rem;
  font-weight: bold;
}

.inbox-wrapper .inbox-header .inbox-filter {
  display: flex;
  align-items: center;
}

@media screen and (max-width: 768px) {

  .hero-chat-list {
    display: flex;
  }

  .inbox-wrapper {
    height: calc(100vh - 6rem);
  }

  .inbox-wrapper .inbox-header {
    display: block;
  }

  .inbox-wrapper .inbox-header .inbox-info {
    width: 100%;
    margin-right: 0;
  }

  .inbox-wrapper .inbox-header .inbox-filter {
    display: none;
    margin-left: 0;
    margin-right: 0;
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
    transform: translateY(-130%);
  }

  .inbox-wrapper .inbox-header .inbox-filter.active {
    transform: translateY(0);
    display: block;
  }
}





::ng-deep .tag-checkbox .mdc-form-field {
  width: 100%;
}

::ng-deep .tag-checkbox .mdc-form-field .mdc-label {
  width: calc(100% - 50px);
}

::ng-deep .tag-checkbox .mdc-form-field .mdc-checkbox {
  padding: 5px;
  margin: 0;
}

::ng-deep .tag-checkbox .mdc-form-field .mdc-checkbox .mat-mdc-checkbox-touch-target {
  width: 30px;
  height: 30px;
}

::ng-deep .tag-checkbox .mdc-form-field .mdc-checkbox .mdc-checkbox__native-control {
  width: 30px;
  height: 30px;
}

::ng-deep .tag-checkbox .mdc-form-field .mdc-checkbox .mdc-checkbox__background {
  top: 5px;
  left: 5px;
}

/* Search filter styles */
.inbox-filter {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  gap: 8px;
}

.inbox-filter>div {
  margin-bottom: 0px;
}

@media (max-width: 800px) {
  .inbox-filter {
    flex-direction: column;
    align-items: flex-start;
  }

  .inbox-filter>div {
    width: 100%;
  }
}

/* Custom styles for Material datepicker */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Material Calendar Styles */
.mat-datepicker-container {
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.2s ease-out;
}

.custom-calendar {
  width: 100%;
  max-width: 700px;
  margin: 0 auto;
  background-color: transparent;
  font-family: inherit;
}

/* Make the calendar display multiple months side by side */
::ng-deep .mat-calendar {
  width: 100%;
  font-family: inherit;
}

::ng-deep .mat-calendar-header {
  padding: 8px 8px 0px 8px;
  background-color: transparent;
}

::ng-deep .mat-calendar-content {
  padding: 0 8px 8px 8px;
}

::ng-deep .mat-calendar-table {
  width: 100%;
}

/* Style the form fields */
::ng-deep .custom-mat-form-field .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

::ng-deep .custom-mat-form-field .mat-mdc-text-field-wrapper {
  background-color: transparent;
  padding: 0;
}

::ng-deep .custom-mat-form-field .mat-mdc-form-field-flex {
  height: 46px;
  align-items: center;
  padding: 0 8px;
}

/* Modern date field styling */
.modern-date-field {
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.modern-date-field:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .modern-date-field:hover {
  border-color: #4b5563;
}

/* Hide the default outline */
::ng-deep .modern-date-field .custom-mat-form-field .mdc-notched-outline__leading,
::ng-deep .modern-date-field .custom-mat-form-field .mdc-notched-outline__notch,
::ng-deep .modern-date-field .custom-mat-form-field .mdc-notched-outline__trailing {
  border-color: transparent !important;
  border-width: 0 !important;
}

/* Style the form field text */
::ng-deep .modern-date-field .custom-mat-form-field .mat-mdc-input-element {
  padding: 8px 4px !important;
  font-size: 0.875rem !important;
}

/* Style the datepicker toggle icon */
::ng-deep .modern-date-field .mat-datepicker-toggle {
  color: #9ca3af !important;
}

::ng-deep .modern-date-field .mat-datepicker-toggle:hover {
  color: var(--color-light-primary) !important;
}

/* Fix cell content in popup */
::ng-deep .mat-datepicker-content .mat-calendar-body-cell-content {
  background-color: transparent !important;
}

::ng-deep .mat-datepicker-content .mat-calendar-body-selected {
  background-color: var(--color-light-primary) !important;
}

::ng-deep .dark .mat-datepicker-content .mat-calendar-body-selected {
  background-color: var(--color-dark-primary) !important;
}

/* Style for date range */
::ng-deep .date-in-range:not(.mat-calendar-body-selected) {
  background-color: rgba(114, 65, 255, 0.1) !important;
}

::ng-deep .dark .date-in-range:not(.mat-calendar-body-selected) {
  background-color: rgba(114, 65, 255, 0.2) !important;
}

/* Fix date range in popup */
::ng-deep .mat-datepicker-content .date-in-range:not(.mat-calendar-body-selected) {
  background-color: rgba(114, 65, 255, 0.1) !important;
}

::ng-deep .dark .mat-datepicker-content .date-in-range:not(.mat-calendar-body-selected) {
  background-color: rgba(114, 65, 255, 0.2) !important;
}

/* Style for Material datepicker toggle icon */
::ng-deep .mat-datepicker-toggle-active {
  color: var(--color-light-primary);
}

::ng-deep .dark .mat-datepicker-toggle-active {
  color: var(--color-dark-primary);
}



/* Style for Material datepicker popup */
::ng-deep .mat-datepicker-content {
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05) !important;
  overflow: hidden !important;
  border: 1px solid #e5e7eb !important;
}

::ng-deep .dark .mat-datepicker-content {
  background-color: #1f2937 !important;
  color: white !important;
  border: 1px solid #374151 !important;
}

/* Fix transparent background in popup calendar */
::ng-deep .mat-datepicker-content .mat-calendar {
  background-color: white !important;
}

::ng-deep .dark .mat-datepicker-content .mat-calendar {
  background-color: #1f2937 !important;
}

/* Fix calendar header in popup */
::ng-deep .mat-datepicker-content .mat-calendar-header {
  background-color: white !important;
  padding: 8px 8px 0px 8px !important;
}

::ng-deep .dark .mat-datepicker-content .mat-calendar-header {
  background-color: #1f2937 !important;
}

/* Fix calendar content in popup */
::ng-deep .mat-datepicker-content .mat-calendar-content {
  background-color: white !important;
  padding: 0 8px 8px 8px !important;
}

::ng-deep .dark .mat-datepicker-content .mat-calendar-content {
  background-color: #1f2937 !important;
}

/* Style calendar cells */
::ng-deep .mat-calendar-body-cell-content {
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
}

::ng-deep .mat-calendar-body-cell:hover .mat-calendar-body-cell-content:not(.mat-calendar-body-selected) {
  background-color: rgba(114, 65, 255, 0.08) !important;
}

::ng-deep .dark .mat-calendar-body-cell:hover .mat-calendar-body-cell-content:not(.mat-calendar-body-selected) {
  background-color: rgba(114, 65, 255, 0.15) !important;
}

/* Make sure the popup has a solid background */
::ng-deep .mat-datepicker-popup {
  background-color: white !important;
}

::ng-deep .dark .mat-datepicker-popup {
  background-color: #1f2937 !important;
}

/* Custom tooltip styles */
::ng-deep .mat-mdc-tooltip {
  white-space: pre-line !important;
  max-width: 300px !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
}

::ng-deep .mat-mdc-tooltip .mdc-tooltip__surface {
  background-color: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  font-size: 12px !important;
}

/* Dark mode tooltip */
.dark ::ng-deep .mat-mdc-tooltip .mdc-tooltip__surface {
  background-color: rgba(30, 30, 30, 0.9) !important;
  color: #f5f5f5 !important;
}

/* Parent container height setup */
.container {
  height: 100%;
}

/* Grid container */
.grid {
  height: 100%;
}

/* Ensure chat content takes full height */
app-chat-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}



/* Override any conflicting styles */
.col-span-12,
.md\\:col-span-8,
.lg\\:col-span-9,
.md\\:col-span-4,
.lg\\:col-span-3 {
  height: 100%;
  min-height: 0;
}
