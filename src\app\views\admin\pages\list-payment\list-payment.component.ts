import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroCheckCircle,
  heroEllipsisHorizontal,
  heroEye,
  heroTrash,
  heroXCircle,
  heroXMark,
} from '@ng-icons/heroicons/outline';
import { map, Observable } from 'rxjs';

import {
  ConfirmDialogComponent,
  DataTableComponent,
  IColumn, SvgIconComponent,
} from '@shared/components';

import { OverlayModule } from '@angular/cdk/overlay';
import { UserAiStore } from '@core/stores';
import {
  DxDialog,
  DxFormField,
  DxInput,
  DxOption, DxPrefix,
  DxSelect,
  DxSnackBar,
} from '@dx-ui/ui';
import {
  EMAIL_SUPER_ADMIN,
  PAYMENT_METHOD,
  PAYMENT_STATUS,
  ROLE_ACCOUNT,
  TYPE_PLAN,
} from '@shared/app.constant';
import { ClickOutsideDirective } from '@shared/directives';
import { PaymentService, PlanService } from '@shared/services';
import { PaymentDetailComponent } from '@views/admin/pages/list-payment/payment-detail/payment-detail.component';
// Removed unused import

// Define the IPaymentInfo interface
export interface IPaymentInfo {
  id?: number;
  fullName: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  plan?: string;
  payment_method?: string;
  amount?: number;
  currency?: string;
  status?: string;
  start_date?: string;
  end_date?: string;
  isDeleting?: boolean;
  isActions?: boolean;
}

@Component({
  selector: 'app-list-payment',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    NgIconsModule,
    DataTableComponent,
    OverlayModule,
    ClickOutsideDirective,
    DxFormField,
    DxInput,
    DxSelect,
    DxOption,
    DxPrefix,
    SvgIconComponent,
  ],
  templateUrl: './list-payment.component.html',
  styleUrl: './list-payment.component.css',
  providers: [
    provideIcons({
      heroTrash,
      heroEllipsisHorizontal,
      heroEye,
      heroCheckCircle,
      heroXCircle,
      heroXMark,
    }),
  ],
})
export class ListPaymentComponent implements OnInit {
  count: number = 0;
  searchTimeout: any;
  columns: IColumn[] = [
    {
      columnDef: 'index',
      headerName: 'No.',
      flex: 0.1,
      minWidth: '40px',
    },
    {
      columnDef: 'fullName',
      headerName: 'Full name',
      flex: 0.4,
      minWidth: '180px',
    },
    {
      columnDef: 'email',
      headerName: 'Email',
      flex: 0.4,
      minWidth: '180px',
    },
    {
      columnDef: 'plan',
      headerName: 'Plan',
      flex: 0.4,
      minWidth: '150px',
      alignHeader: 'center',
      align: 'center',
    },
    {
      columnDef: 'payment_method',
      headerName: 'Payment method',
      flex: 0.3,
      minWidth: '120px',
      alignHeader: 'center',
      align: 'center',
    },
    {
      columnDef: 'amount',
      headerName: 'Amount',
      flex: 0.2,
      minWidth: '120px',
      alignHeader: 'right',
      align: 'right',
    },
    {
      columnDef: 'currency',
      headerName: 'Currency',
      flex: 0.2,
      minWidth: '80px',
      align: 'center',
      alignHeader: 'center',
    },
    {
      columnDef: 'status',
      headerName: 'Status',
      flex: 0.2,
      minWidth: '80px',
    },
    // {
    //   columnDef: 'action',
    //   headerName: 'Action',
    //   flex: 0.1,
    //   minWidth: '80px',
    //   alignHeader: 'center',
    //   align: 'center',
    // },
  ];
  listPayment: IPaymentInfo[] = [];
  searchModel: any = {
    key_word: '',
    payment_method: '',
    plan_id: '0', // Use string '0' instead of number 0 to match SelectOption interface
    status: '',
    sort_by: '',
    page: 0,
    pageSize: 10,
  };
  listPaymentMethod = [
    {
      label: 'All',
      value: '',
    },
    {
      label: 'Stripe',
      value: PAYMENT_METHOD.STRIPE,
    },
    {
      label: 'MB',
      value: PAYMENT_METHOD.MB,
    },
    {
      label: 'Onepay',
      value: PAYMENT_METHOD.ONEPAY,
    },
  ];
  listPlanSearch: Array<{ label: string; value: string }> = [];
  listStatus = [
    {
      label: 'All',
      value: '',
    },
    {
      label:
        PAYMENT_STATUS.PENDING.charAt(0).toUpperCase() +
        PAYMENT_STATUS.PENDING.slice(1).toLowerCase(),
      value: PAYMENT_STATUS.PENDING,
    },
    {
      label:
        PAYMENT_STATUS.DONE.charAt(0).toUpperCase() +
        PAYMENT_STATUS.DONE.slice(1).toLowerCase(),
      value: PAYMENT_STATUS.DONE,
    },
    {
      label:
        PAYMENT_STATUS.CANCEL.charAt(0).toUpperCase() +
        PAYMENT_STATUS.CANCEL.slice(1).toLowerCase(),
      value: PAYMENT_STATUS.CANCEL,
    },
  ];
  EMAIL_SUPER_ADMIN = EMAIL_SUPER_ADMIN;
  currentUser: { email: string; role: string } = {
    email: '',
    role: ROLE_ACCOUNT.USER,
  };

  private paymentService = inject(PaymentService);
  private planService: PlanService = inject(PlanService);
  private userStore = inject(UserAiStore);
  private dialog = inject(DxDialog);
  private snackBar = inject(DxSnackBar);

  constructor() {}

  ngOnInit() {
    this.getCurrentUser();
    this.getAllListPlan();
    this.doSearch();
  }

  // Removed document:click listener as it conflicts with clickOutside directive

  getAllListPlan() {
    this.planService.getAllPlans({}, { page: 1, page_size: 999999 }).subscribe({
      next: (res) => {
        if (res.items && res.items.length > 0) {
          this.listPlanSearch = res.items
            .filter((item: any) => item.price > 0)
            .map((item: any) => ({
              label: item.name,
              value: String(item.id), // Convert to string to match SelectOption interface
            }));
          this.listPlanSearch.unshift({ label: 'All', value: '0' }); // Use string '0' instead of number 0
        } else {
          this.listPlanSearch = [
            {
              label: 'All',
              value: '0', // Use string '0' instead of number 0
            },
          ];
        }
      },
    });
  }

  doSearch(event?: any) {
    if (event) {
      if (event.pageSize) {
        this.searchModel.page = event.pageIndex;
        this.searchModel.pageSize = event.pageSize;
      }
    } else {
      // Reset to first page when filters change
      this.searchModel.page = 0;
    }

    this.getListPayment(this.searchModel, this.searchModel).subscribe(
      (items: IPaymentInfo[]) => {
        this.listPayment = items;
      }
    );
  }

  // Handle search input change
  onSearchChange(event: Event): void {
    // Use debounce to avoid too many API calls
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
      this.changeFilter();
    }, 300);
  }

  // Handle payment method filter change
  onPaymentMethodChange(event: any): void {
    this.changeFilter();
  }

  // Handle plan filter change
  onPlanChange(event: any): void {
    this.changeFilter();
  }

  // Handle status filter change
  onStatusChange(event: any): void {
    this.changeFilter();
  }

  // Reset to first page when filters change
  changeFilter() {
    this.searchModel.page = 0;
    this.doSearch();
  }

  getListPayment(body?: any, params?: any): Observable<IPaymentInfo[]> {
    let requestParams = {};
    if (params) {
      requestParams = { page: params.page + 1, page_size: params.pageSize };
    }
    let requestBody = {};
    if (body) {
      // Convert plan_id from string to number for API call
      const modifiedBody = { ...body };
      if (modifiedBody.plan_id && modifiedBody.plan_id !== '0') {
        modifiedBody.plan_id = Number(modifiedBody.plan_id);
      } else if (modifiedBody.plan_id === '0') {
        // If plan_id is '0' (All), set it to null or remove it
        delete modifiedBody.plan_id;
      }
      // Remove pagination params from body
      delete modifiedBody.page;
      delete modifiedBody.pageSize;
      requestBody = modifiedBody;
    }
    return this.paymentService.getListPayment(requestBody, requestParams).pipe(
      map((res: any) => {
        this.count = res.total;
        if (res.items && res.items.length !== 0) {
          return res.items.map((item: any) => ({
            ...item,
            fullName: item.first_name + ' ' + item.last_name,
            isDeleting: false,
            isActions: false,
            // plan: item.plan ?? TYPE_PLAN.ESSENTIAL_YEAR,
          }));
        } else {
          return [];
        }
      })
    );
  }

  handleClassesPlan(row: any) {
    if (!row || typeof row !== 'string') {
      return '';
    }

    if (row.includes(TYPE_PLAN.FREE)) {
      return 'border-light-plan-free text-light-plan-free hover:bg-light-plan-free hover:text-light-white';
    } else if (row.includes(TYPE_PLAN.START)) {
      return 'border-light-plan-starter text-light-plan-starter hover:bg-light-plan-starter hover:text-light-white';
    } else if (row.includes(TYPE_PLAN.ESSENTIAL)) {
      return 'border-light-plan-essential text-light-plan-essential hover:bg-light-plan-essential hover:text-light-white';
    } else if (row.includes(TYPE_PLAN.BUSINESS)) {
      return 'border-light-plan-business text-light-plan-business hover:bg-light-plan-business hover:text-light-white';
    } else {
      return '';
    }
  }

  handleTimePlanTooltip(row: any, type: string) {
    if (!row) {
      return type === 'start_date' ? 'Start date: None' : 'End date: None';
    }

    let date = '';
    if (type === 'start_date') {
      if (row.start_date) {
        const startDate = new Date(row.start_date);
        date = 'Start date: ' + startDate.toLocaleDateString('en-GB'); // DD/MM/YYYY format
      } else {
        date = 'Start date: ' + 'None';
      }
    } else if (type === 'end_date') {
      if (row.end_date) {
        const endDate = new Date(row.end_date);
        date = 'End date: ' + endDate.toLocaleDateString('en-GB'); // DD/MM/YYYY format
      } else {
        date = 'End date: ' + 'None';
      }
    }
    return date;
  }

  handleNamePlan(name: string) {
    if (!name || typeof name !== 'string') {
      return '';
    }
    return name.includes('Yearly') || name.includes('Monthly')
      ? name.split(' ')[0]
      : name;
  }

  /**
   * View payment details in a dialog
   */
  viewPaymentDetails(row: IPaymentInfo) {
    if (!row) {
      console.error();
      return;
    }

    row.isActions = false; // Close the action menu
    this.dialog.open(PaymentDetailComponent, {
      data: {
        payment: row,
      },
      width: '80vw',
      maxWidth: '100vw',
      panelClass: 'large-dialog',
    });
  }

  /**
   * Update payment status (mark as done or cancel)
   */
  updatePaymentStatus(row: IPaymentInfo, newStatus: string) {
    if (!row) {
      console.error();
      return;
    }

    row.isActions = false; // Close the action menu

    this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title:
            newStatus === 'done' ? 'Mark payment as done' : 'Cancel payment',
          content: `Are you sure you want to ${
            newStatus === 'done' ? 'mark as done' : 'cancel'
          } the payment for ${row.fullName || 'this user'}?`,
          isDelete: false,
        },
        width: '400px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (!!value) {
          // Here you would call the API to update the payment status
          // For now, just update the local state and show a message
          row.status = newStatus;
          this.showSnackBar(
            `Payment ${
              newStatus === 'done' ? 'marked as done' : 'cancelled'
            } successfully`,
            'success'
          );
        }
      });
  }

  /**
   * Show delete confirmation dialog
   */
  showDeleteUser(row: IPaymentInfo) {
    if (!row) {
      console.error();
      return;
    }

    row.isActions = false; // Close the action menu

    this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Delete this payment record',
          content: `Are you sure you want to delete the payment record for ${
            row.fullName || 'this user'
          }?`,
          isDelete: true,
        },
        width: '400px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (!!value) {
          this.confirmDeleteUser(row);
        }
      });
  }

  /**
   * Handle the actual deletion after confirmation
   */
  confirmDeleteUser(row: IPaymentInfo) {
    if (!row) {
      console.error();
      return;
    }

    // Mark the row as deleting to show the spinner
    row.isDeleting = true;

    // Implement the actual delete functionality here
    // For example:
    // this.paymentService.deletePayment(row.id).subscribe({
    //   next: (res) => {
    //     this.showSnackBar("Payment record deleted successfully", "success");
    //     this.doSearch(); // Refresh the list
    //   },
    //   error: (err) => {
    //     this.showSnackBar("Failed to delete payment record", "error");
    //     row.isDeleting = false; // Reset the deleting state
    //   }
    // });

    // For now, just show a message and reset the deleting state after a delay
    setTimeout(() => {
      row.isDeleting = false;
      this.showSnackBar(
        'Delete payment functionality not implemented yet',
        'info'
      );
    }, 1000);
  }

  private getCurrentUser() {
    if (this.userStore.currentUser()) {
      const roleValue = this.userStore.currentUser()?.role;
      const roleKey = Object.keys(ROLE_ACCOUNT).find(
        (key) => ROLE_ACCOUNT[key as keyof typeof ROLE_ACCOUNT] === roleValue
      ) as keyof typeof ROLE_ACCOUNT;
      this.currentUser = {
        email: this.userStore.currentUser()?.email || '',
        role: ROLE_ACCOUNT[roleKey] || ROLE_ACCOUNT.USER,
      };
    }
  }

  showSnackBar(message: string, type: 'success' | 'error' | 'info') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }
}
