import { computed } from '@angular/core';
import { STUDIO_STATUS, STUDIO_STATUS_TYPE } from '@core/constants';
import {
  patchState,
  signalStore,
  withComputed,
  withMethods,
  withState,
} from '@ngrx/signals';
import { CommonUtils } from '@shared/utils';
import { IStudioStore } from './models';

const initialState: IStudioStore = {
  status:
    (CommonUtils.isRemember()
      ? localStorage.getItem('studio')
      : sessionStorage.getItem('studio')) ?? STUDIO_STATUS.DEV,
  debugMode: false,
};

export const StudioStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withComputed(({ status }) => ({
    statusComputed: computed(() => status()),
  })),
  withMethods((store) => ({
    setStudioStatus(status: STUDIO_STATUS_TYPE): void {
      const storage = CommonUtils.isRemember() ? localStorage : sessionStorage;
      storage.setItem('studio', status);
      patchState(store, (state) => ({ ...state, status }));
    },
    setFlowDebugMode(debugMode: boolean): void {
      patchState(store, (state) => ({ ...state, debugMode }));
    },
    debugModeSnapshot(): boolean {
      return store.debugMode();
    },
  }))
);
