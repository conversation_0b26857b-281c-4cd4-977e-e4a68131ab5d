import { ChangeDetectorRef, inject } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import {
  MatDialog,
  MatDialogConfig,
  MatDialogRef,
} from '@angular/material/dialog';
import { DxSnackBar } from '@dx-ui/ui';
import { cloneDeep } from 'lodash';
import { take } from 'rxjs/operators';

export class BaseComponent {
  // Default search model with pagination parameters
  searchModel: any = {
    page: 0,
    pageSize: 20,
  };

  formGroup: FormGroup = new FormGroup({});

  public fb = inject(FormBuilder);
  public dialog = inject(MatDialog);
  public snackBar = inject(DxSnackBar);
  public cdr = inject(ChangeDetectorRef);

  showDialog(
    component?: any,
    options: MatDialogConfig = {},
    callback?: (value: any) => void
  ): MatDialogRef<any> {
    const ref = this.dialog.open(component, {
      width: '30vw',
      ...options,
    });
    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((value: any) => {
        callback && callback(value);
      });
    return ref;
  }

  closeDialog(): void {
    this.dialog.closeAll();
  }

  showSnackBar(message: string, type: 'success' | 'error' | 'info') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }

  mustMatch(controlName: string, matchingControlName: string) {
    return (formGroup: FormGroup) => {
      const control = formGroup.controls[controlName];
      const matchingControl = formGroup.controls[matchingControlName];
      if (matchingControl.errors && !matchingControl.errors['mustMatch']) {
        // return if another validator has already found an error on the matchingControl
        return;
      }

      // Set error on matchingControl if validation fails
      if (control.value !== matchingControl.value) {
        matchingControl.setErrors({ mustMatch: true });
      } else {
        matchingControl.setErrors(null);
      }
    };
  }

  mustNotMatch(controlName: string, matchingControlName: string) {
    return (formGroup: FormGroup) => {
      const control = formGroup.controls[controlName];
      const matchingControl = formGroup.controls[matchingControlName];

      if (matchingControl.errors && !matchingControl.errors['mustNotMatch']) {
        return;
      }

      if (control.value === matchingControl.value) {
        matchingControl.setErrors({ mustNotMatch: true });
      } else {
        matchingControl.setErrors(null);
      }
    };
  }

  cloneDeep(data: any) {
    return cloneDeep(data);
  }

   extractTextDoubleQuotes(str: string): string[]{
    const re = /"(.*?)"/g;
    const result: string[] = [];
    let current;
    while (current = re.exec(str)) {
      const popped = current.pop();
      if (popped) {
        result.push(popped);
      }
    }
    return result.length > 0 && result[0] ? result : [str];
  }

  extractUrls(text: string, role?: string): string {
    if (!text) return '';
    text = text.split(/\s*<dx_text_source>.*<\/dx_text_source>/)[0];
    const urlRegex = /(https?:\/\/[^\s()]+)/g;
    const phoneRegex = /(\(\+\d{1,4}\)|0)((\s?((\d{2,}\s?\d{3,}\s?\d{3,})|(\d{7,})))|(\.?(\d{2,}\.?\d{3,}\.?\d{3,}))|(-?(\d{2,}-?\d{3,}-?\d{3,})))/g;
    const mailRegex = /[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}/g;
    const imageTagRegex = /<img\s+[^>]*src="([^"]*)"[^>]*>/g;
    const imgTagList = (text.match(imageTagRegex) || []).map(v => v.replace(/<img/g, `<img id="imgCheck" class="rounded-xl zoom"`));

    const aTagList = text.replace(urlRegex, '<a href="$&" class="text-light-primary hover:underline" target="_blank">$&</a>').split(/(<[^>]*>)/g);
    const urlList: string[] = [];
    for (let textSplited of aTagList) {
      if (textSplited.includes("<a")) {
        urlList.push(this.extractTextDoubleQuotes(textSplited)[0]);
      }
    }

    return text
      .replace(imageTagRegex, '%image')
      .replace(urlRegex, (match, p1) => {
        let url = p1;
        if (url.endsWith('.')) {
          url = url.slice(0, -1);
        }
        return `<a href="${url}" class="${role === 'user' ? 'text-white' : 'text-light-primary'} hover:underline whitespace-pre-wrap" target="_blank">${url}</a>`;
      })
      .replace(phoneRegex, (match) => {
        if (urlList.some(url => url.includes(match))) return match;
        return `<a href="tel:${match}" class="${role === 'user' ? 'text-white' : 'text-light-primary'} hover:underline whitespace-pre-wrap" target="_blank">${match}</a>`;
      })
      .replace(mailRegex, `<a href="mailto:$&" class="${role === 'user' ? 'text-white' : 'text-light-primary'} hover:underline whitespace-pre-wrap" target="_blank">$&</a>`)
      .replace(/%image/g, () => {
        const img = imgTagList.shift();
        return img ? img : '';
      });
  }

  getImages(text: string): string[] {
    const imageTagRegex = /<img\s+[^>]*src="([^"]*)"[^>]*>/g;
    const srcList: any[] = [];
    let match;

    while ((match = imageTagRegex.exec(text)) !== null) {
      srcList.push({ src: match[1] });
    }

    return srcList;
  }

  classifyMessage(input: any) {
    const trimmedInput = input.trim();
    if (trimmedInput.startsWith('<dx_gll>')) {
      return 'gallery';
    } else if (trimmedInput.startsWith('<dx_gr>')) {
      return 'grid';
    } else {
      return 'text';
    }
  }

  isShowSource(input: any) {
    const trimmedInput = input.trim();
    return Boolean(trimmedInput.includes('<dx_text_source>'));
  }

  extractImages(input: any) {
    const trimmedInput = input.trim();
    let content = '';

    if (
      !trimmedInput.startsWith('<dx_gll>') ||
      !trimmedInput.endsWith('</dx_gll>')
    ) {
      content = trimmedInput.slice(7, -8).trim();
    } else {
      content = trimmedInput.slice(8, -9).trim();
    }

    // Tách các dòng và lọc bỏ các dòng rỗng
    const lines = content
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line);

    return lines.map((line) => {
      const parts = line.split('", "');
      const src = parts[0].replace(/"/g, '');
      const label = parts[1] ? parts[1].replace(/"/g, '') : '';
      return { src, label };
    });
  }

  extractSources(input: any) {
    const trimmedInput = input.trim();
    let content = '';

    const regex = /<dx_text_source>(.*?)<\/dx_text_source>/;
    const match = trimmedInput.match(regex);
    if (match) {
      content = match[1];
    }

    const lines = content
      .split(',')
      .map((line) => line.trim())
      .filter((line) => line);

    // Kết hợp các phần tử thành đối tượng
    const result = [];
    for (let i = 0; i < lines.length; i += 3) {
      // Kiểm tra để tránh lỗi khi số phần tử không chia hết cho 3
      if (i + 2 < lines.length) {
        const source = lines[i].replace(/"/g, ''); // Loại bỏ dấu ngoặc kép
        const title = lines[i + 1].replace(/"/g, '');
        const type = lines[i + 2].replace(/"/g, '');
        result.push({ source, title, type });
      }
    }
    return result;
  }

  convertMarkdownToImgHTML(input: string) {
    // Sử dụng biểu thức chính quy để tìm và thay thế cú pháp markdown ![]() thành <img>
    return input.replace(/!\[([^\]]*)]\(([^)]+)\)/g, (match, altText, src) => {
      return `<img id="imgCheck" class="rounded-xl zoom" src="${src}" alt="${altText}">`;
    });
  }
}
