<div class="flex flex-col">
  <div
    class="w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="text-2xl text-base-content dark:text-dark-base-content font-bold"
    >
      Api key
    </div>
    <div class="flex items-center justify-end">
      <ng-icon
        name="heroXMark"
        class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></ng-icon>
    </div>
  </div>

  <div class="px-6 py-5 max-h-[60vh] overflow-auto">
    <dx-form-field [subscriptHidden]="true" class="w-full" id="apiKey">
      <dx-label class="text-sm">Api Key:</dx-label>
      <input
        dx-input
        [(ngModel)]="data.apiKey"
        (ngModelChange)="data.apiKey = $event.trim()"
        [type]="'text'"
        placeholder="Api key"
      />
    </dx-form-field>
  </div>

  <div
    class="flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Close</button>
    <button
      dxLoadingButton
      [loading]="isLoading()"
      [spinColor]="'white'"
      (click)="saveNewLLM(data)"
    >
      Save
    </button>
  </div>
</div>
