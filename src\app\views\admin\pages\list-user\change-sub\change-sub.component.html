<div class="flex flex-col rounded-3xl">
  <div
    class="w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      Upgrade subscription
    </div>
    <div class="flex items-center justify-end">
      <ng-icon
        name="heroXMark"
        class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></ng-icon>
    </div>
  </div>

  <div class="px-6 pt-6 pb-[3px] space-y-6">
    <div
      class="flex items-center space-x-1 text-xl text-neutral-content dark:text-dark-neutral-content"
    >
      <p>Now subscription:</p>
      <p class="font-bold">{{ nowPlan() }}</p>
    </div>
    <div>
      <dx-form-field>
        <dx-label>New subscription:</dx-label>
        <dx-select
          panelClass="dx-select-ai-select"
          [value]="newSubscription"
          (valueChange)="newSubscription = $event"
        >
          @for (plan of listPlan(); track plan) {
          <!--// set disable-->
          <dx-option [value]="plan.value" class="truncate line-clamp-1">
            {{ plan.label }}
          </dx-option>
          } @empty {
          <div
            class="w-full h-full flex items-center justify-center text-gray-400 italic text-sm dark:text-white"
          >
            No plan found
          </div>
          }
        </dx-select>
      </dx-form-field>
    </div>
  </div>

  <div
    class="flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Close</button>
    <button
      dxLoadingButton="filled"
      [loading]="isLoading()"
      [disabled]="!newSubscription || newSubscription === ''"
      (click)="upgradePlan(data)"
    >
      Save
    </button>
  </div>
</div>
