<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      Tag name
    </div>
    <div class="flex items-center justify-end space-x-4">
      <ng-icon
        name="heroXMark"
        class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></ng-icon>
    </div>
  </div>

  <div class="flex-1 mt-18 mb-20 overflow-y-auto flex flex-col space-y-4 p-6">
    @if (selectedColor) {
    <div
      class="px-4 py-2 rounded-xl mb-4 tag"
      [ngStyle]="{
        'background-color': selectedColor,
        color: getTextColor(selectedColor)
      }"
    >
      {{ data.name || "Tag name" }}
    </div>
    }

    <dx-form-field class="w-full">
      <dx-label class="text-sm"
        >Name <span class="text-red-500">*</span></dx-label
      >
      <input
        dxInput
        [(ngModel)]="tagData.name"
        [type]="'text'"
        placeholder="Enter tag name"
      />
    </dx-form-field>

    <dx-form-field class="w-full">
      <dx-label class="text-sm"
        >Type <span class="text-red-500">*</span></dx-label
      >
      <dx-select [(ngModel)]="tagData.application">
        @for (type of listType; track $index) {
        <dx-option [value]="type.value">{{ type.label }}</dx-option>
        }
      </dx-select>
    </dx-form-field>

    @if (isObjectEmpty(data)) {
    <div class="w-full flex items-center">
      <dx-checkbox [(ngModel)]="tagData.is_active"> Active </dx-checkbox>
    </div>
    }
    <div class="mt-4 rounded-md">
      <h2 class="block text-base-content dark:text-dark-base-content mb-2">
        Pick a Color
      </h2>
      <div class="grid grid-cols-5 gap-2">
        @for (color of colors; track color) {
        <div
          class="w-full h-8 cursor-pointer flex items-center rounded-sm justify-center transition-transform duration-300"
          [ngStyle]="{ 'background-color': color }"
          (click)="setColor(color)"
          [class.selected-border]="color === selectedColor"
        ></div>
        }
      </div>
    </div>
  </div>

  <!-- Actions -->
  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="closeTagDialog()">Close</button>
    <button
      dxLoadingButton="filled"
      [loading]="isLoadingSaveTag"
      [disabled]="!canSave()"
      (click)="saveTag(data.id)"
    >
      Save
    </button>
  </div>
</div>
