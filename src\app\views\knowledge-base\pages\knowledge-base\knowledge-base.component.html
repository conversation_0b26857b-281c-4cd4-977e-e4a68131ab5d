<div class="flex flex-wrap space-x-3 hidden lg:flex">
  <div
    class="max-w-[300px] text-[28px] font-bold text-base-content dark:text-dark-base-content truncate flex items-center hover:cursor-pointer hover:!text-gray-500 dark:hover:!text-gray-100 text-light-text dark:text-dark-text"
    (click)="toBreadcrumbItem(undefined)"
  >
    Knowledge Base
  </div>
  @for (breadcrumb of breadcrumbs; track breadcrumb; let first = $first) {
  <div
    class="max-w-[300px] text-[#94a2b8] text-2xl truncate text-left flex items-center hover:cursor-pointer hover:!text-light-text dark:text-dark-text"
    [ngClass]="
      breadcrumb.id === parentId ? '!text-light-text dark:text-dark-text' : ''
    "
    (click)="toBreadcrumbItem(breadcrumb)"
  >
    <ng-icon name="heroChevronRight" class="!text-xl !text-blue-400"></ng-icon
    >&ensp;{{ breadcrumb.name }}
  </div>
  }
</div>

<dx-card appearance="outlined" class="block mt-6">
  <dx-card-content>
    <div class="w-full flex flex-wrap items-center justify-between">
      <div class="view-toggle-container">
        <div class="view-toggle-wrapper">
          <button
            class="view-toggle-button"
            [ngClass]="{ active: showStyle === 'list' }"
            (click)="onToggleChange({ value: 'list' })"
          >
            <svg
              width="18"
              height="16"
              viewBox="0 0 14 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M1 0C0.447715 0 0 0.447715 0 1C0 1.55228 0.447715 2 1 2H13C13.5523 2 14 1.55228 14 1C14 0.447715 13.5523 0 13 0H1ZM0 6C0 5.44772 0.447715 5 1 5H13C13.5523 5 14 5.44772 14 6C14 6.55228 13.5523 7 13 7H1C0.447715 7 0 6.55228 0 6ZM1 10C0.447715 10 0 10.4477 0 11C0 11.5523 0.447715 12 1 12H13C13.5523 12 14 11.5523 14 11C14 10.4477 13.5523 10 13 10H1Z"
                class="view-toggle-icon"
              ></path>
            </svg>
            <span class="view-toggle-text">List</span>
          </button>
          <button
            class="view-toggle-button"
            [ngClass]="{ active: showStyle === 'grid' }"
            (click)="onToggleChange({ value: 'grid' })"
          >
            <svg
              width="18"
              height="18"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M0 1C0 0.447715 0.447715 0 1 0H5C5.55228 0 6 0.447715 6 1V5C6 5.55228 5.55228 6 5 6H1C0.447715 6 0 5.55228 0 5V1ZM2 2H4V4H2V2ZM0 9C0 8.44772 0.447715 8 1 8H5C5.55228 8 6 8.44772 6 9V13C6 13.5523 5.55228 14 5 14H1C0.447715 14 0 13.5523 0 13V9ZM2 10H4V12H2V10ZM9 0C8.44772 0 8 0.447715 8 1V5C8 5.55228 8.44772 6 9 6H13C13.5523 6 14 5.55228 14 5V1C14 0.447715 13.5523 0 13 0H9ZM12 2H10V4H12V2ZM8 9C8 8.44772 8.44772 8 9 8H13C13.5523 8 14 8.44772 14 9V13C14 13.5523 13.5523 14 13 14H9C8.44772 14 8 13.5523 8 13V9ZM10 10H12V12H10V10Z"
                class="view-toggle-icon"
              ></path>
            </svg>
            <span class="view-toggle-text">Grid</span>
          </button>
        </div>
      </div>
    </div>

    <!--  <div class="w-full flex flex-wrap items-center justify-start pl-4 space-x-3 block lg:hidden">-->
    <!--    <div class="max-w-[300px] text-light-text dark:text-dark-text text-2xl truncate flex items-center hover:cursor-pointer hover:!text-light-text dark:text-dark-text" [ngClass]="breadcrumbs.length && '!text-[#94a2b8]'" (click)="toBreadcrumbItem(undefined)" (mouseup)="$event.stopPropagation();">My knowledge base</div>-->
    <!--    <div *ngIf="breadcrumbs.length" class="max-w-[300px] text-[#94a2b8] text-2xl truncate text-left flex items-center hover:cursor-pointer hover:!text-light-text dark:text-dark-text" (click)="toBreadcrumbItem(breadcrumbs[breadcrumbs.length - 1])">-->
    <!--      <ng-icon name="heroChevronRight"-->
    <!--               class="!text-xl !text-[#94a2b8]"></ng-icon>&ensp;{{ breadcrumb.name }}-->
    <!--    </div>-->
    <!--    <ng-container *ngFor="let breadcrumb of breadcrumbs; let last = last">-->
    <!--      <div *ngIf="last" class="max-w-[300px] text-[#94a2b8] text-2xl truncate text-left flex items-center hover:cursor-pointer hover:!text-light-text dark:text-dark-text" [ngClass]="breadcrumb.id === parentId && '!text-light-text dark:text-dark-text'" (click)="toBreadcrumbItem(breadcrumb)">-->
    <!--        <ng-icon name="heroChevronRight"-->
    <!--                 class="!text-xl !text-[#94a2b8]"></ng-icon>&ensp;{{ breadcrumb.name }}-->
    <!--      </div>-->
    <!--    </ng-container>-->
    <!--  </div>-->

    @if (folderSelection.selected.length === 0 && fileSelection.selected.length
    === 0) {
    <div class="sm:flex justify-between mt-6 w-full">
      <div class="flex gap-3 text-light-text dark:text-dark-text pb-4 w-full">
        <div class="flex gap-3 flex-wrap w-full">
          <div class="w-full md:w-54">
            <div class="relative">
              <div
                class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none"
              >
                <svg
                  class="w-4 h-4 text-gray-500 dark:text-gray-400"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 20 20"
                >
                  <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                  />
                </svg>
              </div>
              <input
                type="search"
                id="kb-search"
                class="block w-full p-2.5 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                placeholder="Search by name..."
                [value]="searchModel.name"
                (input)="onInputChange($event)"
              />
            </div>
          </div>
          <app-select
            class="w-full sm:w-48 filterSelect"
            id="file-type-filter"
            name="file-type-filter"
            [options]="listFileType"
            [placeholder]="'File type'"
            [value]="searchModel.file_type"
            (selectionChange)="onFileTypeChange($event)"
            [searchable]="true"
          />
          <app-select
            class="w-full sm:w-48 filterSelect"
            id="status-filter"
            name="status-filter"
            [options]="listStatus"
            [placeholder]="'Status'"
            [value]="searchModel.file_status"
            (selectionChange)="onFileStatusChange($event)"
            [searchable]="true"
          />
        </div>
      </div>
      @if (!searchingMode) {
      <div
        class="flex sm:pl-0 md:pl-4 gap-3 h-[54px] text-light-text dark:text-dark-text"
      >
        @if (!parentId) { @if (isAllowAssign) {
        <button
          (click)="openCreateFolderDialog()"
          class="bg-primary md:flex sm:justify-center items-center gap-2 h-[40px] text-white px-6 md:px-8 xl:px-6 2xl:px-8 rounded-lg w-full md:min-w-48 xl:min-w-0 2xl:min-w-48"
        >
          <app-custom-icon
            iconName="heroFolderPlus"
            [size]="18"
            class="text-white"
          ></app-custom-icon>
          <span class="hidden md:block xl:hidden 2xl:block">Create folder</span>

          <!--              <span class="flex md:hidden xl:flex 2xl:hidden gap-2 w-full">-->
          <!--                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#FFFFFF"-->
          <!--                  class="size-6">-->
          <!--                  <path stroke-linecap="round" stroke-linejoin="round"-->
          <!--                    d="M12 10.5v6m3-3H9m4.06-7.19-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z" />-->
          <!--                </svg>-->
          <!--                <p>New</p>-->
          <!--              </span>-->
        </button>
        } @if (isAllowAssign) {
        <button
          class="bg-gray-500 md:flex sm:justify-center items-center gap-2 h-[40px] text-white px-6 md:px-8 xl:px-6 2xl:px-8 rounded-lg w-full md:min-w-[14rem] xl:min-w-0 2xl:min-w-[16rem]"
          [matMenuTriggerFor]="menu"
        >
          <app-custom-icon
            iconName="heroArrowDownOnSquare"
            [size]="18"
            class="text-white"
          ></app-custom-icon>
          <span class="hidden md:block xl:hidden 2xl:block"
            >Import from URL/File</span
          >
          <!--              <span class="flex md:hidden xl:flex 2xl:hidden gap-2 w-full">-->
          <!--                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#FFFFFF"-->
          <!--                  class="size-6">-->
          <!--                  <path stroke-linecap="round" stroke-linejoin="round"-->
          <!--                    d="M9 8.25H7.5a2.25 2.25 0 0 0-2.25 2.25v9a2.25 2.25 0 0 0 2.25 2.25h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25H15M9 12l3 3m0 0 3-3m-3 3V2.25" />-->
          <!--                </svg>-->
          <!--                <p>Import</p>-->
          <!--              </span>-->
        </button>
        } } @if (parentId) {
        <button
          *hasPermission="{ permissions: 5, user_permissions: permissions }"
          (click)="openCreateFolderDialog()"
          class="bg-light-primary dark:bg-dark-primary h-[40px] mt-[6px] text-light-text dark:text-dark-text px-6 md:px-8 xl:px-6 2xl:px-8 rounded-lg w-full md:min-w-48 xl:min-w-0 2xl:min-w-48"
        >
          <span class="hidden md:block xl:hidden 2xl:block">Create folder</span>
          <span class="flex md:hidden xl:flex 2xl:hidden gap-2 w-full">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="#808080"
              class="size-6"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 10.5v6m3-3H9m4.06-7.19-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"
              />
            </svg>
            <p>New</p>
          </span>
        </button>
        <button
          class="bg-gray-500 h-[40px] mt-[6px] px-6 md:px-8 xl:px-6 2xl:px-8 rounded-lg w-full md:min-w-[14rem] xl:min-w-0 2xl:min-w-[14rem]"
          *hasPermission="{ permissions: 5, user_permissions: permissions }"
          [matMenuTriggerFor]="menu"
        >
          <span class="hidden md:block xl:hidden 2xl:block"
            >Import from URL/File</span
          >
          <span class="flex md:hidden xl:flex 2xl:hidden gap-2 w-full">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="#808080"
              class="size-6"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M9 8.25H7.5a2.25 2.25 0 0 0-2.25 2.25v9a2.25 2.25 0 0 0 2.25 2.25h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25H15M9 12l3 3m0 0 3-3m-3 3V2.25"
              />
            </svg>
            <p>Import</p>
          </span>
        </button>
        }
        <mat-menu #menu="matMenu" xPosition="before" class="my-menu mt-2">
          <button (click)="showImportUrlDialog()" mat-menu-item class="!h-10">
            Import from URL
          </button>
          <button (click)="showImportFileDialog()" mat-menu-item class="!h-10">
            Import from file
          </button>
        </mat-menu>
      </div>
      }
    </div>
    } @if (!(folderSelection.selected.length === 0 &&
    fileSelection.selected.length === 0)) {
    <div class="px-4 mt-6 h-[54px]">
      <div
        class="w-full rounded-3xl px-3 py-2 flex items-center bg-light-background dark:bg-dark-background rounded-full border border-[#6F767E]"
      >
        <div class="flex items-center justify-center">
          <ng-icon
            (click)="selectFolder(null); selectFile(null)"
            name="heroXMark"
            (mouseup)="$event.stopPropagation()"
            class="text-red-500 text-2xl hover:cursor-pointer hover:bg-[#6F767E] hover:rounded-2xl"
          ></ng-icon>
        </div>
        <div class="ml-5 text-light-text dark:text-dark-text text-sm">
          {{ folderSelection.selected.length + fileSelection.selected.length }}
          items selected
        </div>
        @if (folderSelection.selected && folderSelection.selected.length &&
        fileSelection.selected.length === 0 && isAllowAssign) {
        <div
          (click)="openAssignPermissionDialog()"
          (mouseup)="$event.stopPropagation()"
          class="ml-6 flex items-center justify-center w-10 aspect-square hover:cursor-pointer hover:bg-[#6F767E] hover:rounded-xl"
        >
          <ng-icon
            name="heroUsers"
            class="text-light-text dark:text-dark-text text-2xl"
            dxTooltip="Assign user permission"
            dxTooltipPosition="below"
          ></ng-icon>
        </div>
        } @if (parentId) {
        <div
          *hasPermission="{ permissions: 5, user_permissions: permissions }"
          (click)="deleteAll()"
          (mouseup)="$event.stopPropagation()"
          class="ml-2 flex items-center justify-center w-10 aspect-square hover:cursor-pointer hover:bg-[#6F767E] hover:rounded-xl"
        >
          <ng-icon
            name="heroTrash"
            class="text-red-500 text-2xl"
            dxTooltip="Delete"
            dxTooltipPosition="below"
          ></ng-icon>
        </div>
        } @if (!parentId) { @if (isAllowAssign) {
        <div
          (click)="deleteAll()"
          (mouseup)="$event.stopPropagation()"
          class="ml-2 flex items-center justify-center w-10 aspect-square hover:cursor-pointer hover:bg-[#6F767E] hover:rounded-xl"
        >
          <ng-icon
            name="heroTrash"
            class="text-light-text dark:text-dark-text text-2xl"
            dxTooltip="Delete"
            dxTooltipPosition="below"
          ></ng-icon>
        </div>
        } }
      </div>
    </div>
    }
    <div
      class="flex flex-col items-stretch justify-start mt-[-0px] flex-grow overflow-hidden responsive-height"
    >
      @if (searchingMode) {
      <div>
        <h1 class="text-light-text dark:text-dark-text text-xl p-[14px] pb-0">
          Search result
        </h1>
      </div>
      } @if ((fileList.length > 0 || folderList.length > 0)) { @if (showStyle ==
      'grid') {
      <div class="h-14 flex items-center justify-end space-x-2">
        <div class="">
          <div
            class="flex items-center justify-center p-2 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl"
          >
            @if (searchModel.order === 'DESC') {
            <ng-icon
              (click)="searchModel.order = 'ASC'; applyFilter()"
              (mouseup)="$event.stopPropagation()"
              name="heroArrowDown"
              class="text-light-text dark:text-dark-text text-xl font-bold"
            ></ng-icon>
            } @if (searchModel.order === 'ASC') {
            <ng-icon
              (click)="searchModel.order = 'DESC'; applyFilter()"
              (mouseup)="$event.stopPropagation()"
              name="heroArrowUp"
              class="text-light-text dark:text-dark-text text-xl font-bold"
            ></ng-icon>
            }
          </div>
        </div>
        <div>
          <div
            class="flex items-center justify-end space-x-2 px-2 py-2 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl"
            [matMenuTriggerFor]="sortByMenu"
          >
            @if (searchModel.sort_by === 'name') {
            <div class="text-light-text dark:text-dark-text">Sort by name</div>
            } @if (searchModel.sort_by === 'updated_at') {
            <div class="text-light-text dark:text-dark-text">
              Sort by last updated
            </div>
            } @if (searchModel.sort_by === 'created_at') {
            <div class="text-light-text dark:text-dark-text">
              Sort by recently created
            </div>
            }
            <ng-icon name="heroChevronDown" class="text-xl font-bold"></ng-icon>
          </div>
          <mat-menu #sortByMenu="matMenu" xPosition="before" class="my-menu">
            <button
              class="!h-10 text-white hover:!bg-light-hover dark:hover:!bg-dark-hover"
              mat-menu-item
              (click)="searchModel.sort_by = 'name'; applyFilter()"
              (mouseup)="$event.stopPropagation()"
              [ngClass]="
                searchModel.sort_by === 'name'
                  ? '!bg-light-hover dark:!bg-dark-hover !text-light-text dark:!text-dark-text'
                  : ''
              "
            >
              <span>Name</span>
            </button>
            <button
              class="!h-10 !text-white hover:!bg-light-hover dark:hover:!bg-dark-hover"
              mat-menu-item
              (click)="searchModel.sort_by = 'updated_at'; applyFilter()"
              (mouseup)="$event.stopPropagation()"
              [ngClass]="
                searchModel.sort_by === 'updated_at'
                  ? '!bg-light-hover dark:!bg-dark-hover !text-light-text dark:!text-dark-text'
                  : ''
              "
            >
              <span>Last updated</span>
            </button>
            <button
              class="!h-10 !text-white hover:!bg-light-hover dark:hover:!bg-dark-hover"
              mat-menu-item
              (click)="searchModel.sort_by = 'created_at'; applyFilter()"
              (mouseup)="$event.stopPropagation()"
              [ngClass]="
                searchModel.sort_by === 'created_at'
                  ? '!bg-light-hover dark:!bg-dark-hover !text-light-text dark:!text-dark-text'
                  : ''
              "
            >
              <span>Recently created</span>
            </button>
          </mat-menu>
        </div>
      </div>
      }
      <div
        class="flex-grow overflow-hidden"
        [ngStyle]="{
          height:
            showStyle === 'grid' ? 'calc(100% - 50px)' : 'calc(100% - 0px)'
        }"
      >
        @switch (showStyle) { @case ('grid') {
        <div class="pr-3 h-full flex flex-col overflow-hidden">
          <div
            class="h-full flex flex-col flex-grow overflow-hidden"
            (clickOutside)="showStyle == 'grid' && removeSelectedFolderOrFile()"
          >
            @if (folderList.length > 0) {
            <div
              class="flex flex-col items-stretch justify-start space-y-2"
              style="height: 40%; min-height: 200px; max-height: 40%"
            >
              <div class="w-full px-5 h-14 flex items-center">
                <div class="text-lg text-light-text dark:text-dark-text">
                  Folders ({{ folderList.length }})
                </div>
                <button
                  (click)="refreshVirtualScrollViewports()"
                  class="ml-2 text-sm text-light-text dark:text-dark-text hover:text-blue-500"
                >
                  Refresh
                </button>
              </div>
              <cdk-virtual-scroll-viewport
                #folderViewport
                id="folderViewport"
                itemSize="50"
                minBufferPx="200"
                maxBufferPx="400"
                style="height: calc(100% - 56px); overflow: auto"
                class="h-full"
              >
                <div
                  class="w-full grid sm:grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-7 pl-5 file-folder-list-wrapper"
                >
                  <ng-container
                    *cdkVirtualFor="
                      let folder of folderList;
                      let i = index;
                      trackBy: trackByFolderId
                    "
                  >
                    <div
                      (dblclick)="toFolder(folder)"
                      (click)="selectFolder(folder)"
                      [matContextMenuTriggerFor]="folderMenu"
                      [ngClass]="
                        folderSelection.isSelected(folder)
                          ? 'bg-light-gray dark:bg-dark-gray text-[#39207f]'
                          : 'hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover'
                      "
                      class="col-span-1 w-full rounded-2xl bg-light-gray dark:bg-dark-gray flex flex-col items-stretch folder-item"
                    >
                      <div
                        class="flex flex-grow-0 flex-shrink-0 basis-14 items-center file-folder-wrapper"
                      >
                        <div class="w-[50px] flex items-center justify-center">
                          <ng-icon
                            name="faSolidFolderClosed"
                            class="text-yellow-400 text-2xl folder-icon"
                            (dblclick)="
                              $event.stopPropagation();
                              toFolder(prepareFolder(folder))
                            "
                          ></ng-icon>
                        </div>
                        <div
                          class="w-[calc(100%-56px)] overflow-hidden text-left flex flex-col"
                        >
                          <div
                            class="truncate folder-name"
                            (dblclick)="
                              $event.stopPropagation();
                              toFolder(prepareFolder(folder))
                            "
                            [dxTooltip]="folder?.name ?? ''"
                            dxTooltipPosition="above"
                            [ngClass]="
                              folderSelection.isSelected(folder)
                                ? 'text-[#c2e7ff]'
                                : 'text-light-text dark:text-dark-text'
                            "
                          >
                            {{ folder.name }}
                          </div>
                        </div>
                        <div class="flex items-center pr-3">
                          <ng-icon
                            name="heroEllipsisVertical"
                            (click)="$event.stopPropagation()"
                            [matMenuTriggerFor]="folderMenu"
                            class="text-light-text dark:text-dark-text text-xl hover:cursor-pointer menu-trigger"
                          ></ng-icon>
                        </div>
                        <mat-menu
                          #folderMenu="matMenu"
                          xPosition="after"
                          class="my-menu"
                        >
                          @if (isAllowAssign) {
                          <button
                            class="!h-10 text-light-text dark:text-dark-text"
                            mat-menu-item
                            (click)="
                              selectFolder(folder); openAssignPermissionDialog()
                            "
                          >
                            <div class="flex items-center">
                              <ng-icon
                                name="heroUserPlus"
                                class="text-blue-500 text-2xl mr-3"
                              ></ng-icon>
                              <span class="text-light-text dark:text-dark-text"
                                >Assign role</span
                              >
                            </div>
                          </button>
                          }
                          <button
                            *hasPermission="{
                              permissions: 7,
                              user_permissions:
                                folder.users && folder.users[0]
                                  ? folder.users[0].permission
                                  : 0
                            }"
                            class="!h-10 text-light-text dark:text-dark-text"
                            mat-menu-item
                            (click)="
                              selectFolder(folder);
                              openCreateFolderDialog(folder)
                            "
                          >
                            <div class="flex items-center">
                              <ng-icon
                                name="heroPencilSquare"
                                class="text-orange-500 text-2xl mr-3"
                              ></ng-icon>
                              <span class="text-light-text dark:text-dark-text"
                                >Rename</span
                              >
                            </div>
                          </button>
                          @if (searchingMode) {
                          <button
                            *hasPermission="{
                              permissions: 7,
                              user_permissions: permissions
                            }"
                            class="!h-10 text-light-text dark:text-dark-text"
                            mat-menu-item
                            (click)="goToFolderLocation(folder)"
                          >
                            <div class="flex items-center">
                              <ng-icon
                                name="heroFolder"
                                class="text-yellow-400 text-2xl mr-3"
                              ></ng-icon>
                              <span class="text-light-text dark:text-dark-text"
                                >Show file location</span
                              >
                            </div>
                          </button>
                          }
                          <button
                            *hasPermission="{
                              permissions: 5,
                              user_permissions:
                                folder.users && folder.users[0]
                                  ? folder.users[0].permission
                                  : 0
                            }"
                            class="!h-10 text-light-text dark:text-dark-text"
                            mat-menu-item
                            (click)="deleteFolder(folder)"
                          >
                            <div class="flex items-center">
                              <ng-icon
                                name="heroTrash"
                                class="text-red-500 text-2xl mr-3"
                              ></ng-icon>
                              <span class="text-light-text dark:text-dark-text"
                                >Delete</span
                              >
                            </div>
                          </button>
                        </mat-menu>
                      </div>
                    </div>
                  </ng-container>
                </div>
              </cdk-virtual-scroll-viewport>
            </div>
            } @if (fileList.length > 0) {
            <div
              class="flex flex-col items-stretch justify-start space-y-2 mt-4 flex-grow"
              style="height: 60%; min-height: 200px"
            >
              <div class="w-full px-5 h-14 flex items-center">
                <div class="text-lg text-light-text dark:text-dark-text">
                  Files ({{ fileList.length }})
                </div>
              </div>
              <cdk-virtual-scroll-viewport
                #fileViewport
                id="fileViewport"
                itemSize="50"
                minBufferPx="200"
                maxBufferPx="400"
                style="height: calc(100% - 56px); overflow: auto"
                class="h-full"
              >
                <div
                  class="w-full grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-7 pl-5 file-folder-list-wrapper"
                >
                  <ng-container
                    *cdkVirtualFor="
                      let file of fileList;
                      trackBy: trackByFileId
                    "
                  >
                    <div
                      (dblclick)="viewContentFile(file)"
                      (click)="selectFile(file)"
                      [matContextMenuTriggerFor]="fileMenu"
                      [ngClass]="
                        fileSelection.isSelected(file)
                          ? 'bg-light-primary dark:bg-dark-primary text-white'
                          : 'opacity-80 hover:cursor-pointer hover:opacity-100 bg-light-hover dark:bg-dark-hover'
                      "
                      class="col-span-1 h-52 md:h-52 lg:h-56 xl:h-52 2xl:h-56 w-full rounded-2xl flex flex-col items-stretch mb-2"
                    >
                      <div
                        class="flex flex-grow-0 flex-shrink-0 basis-12 items-center file-folder-wrapper"
                      >
                        @if (file.status === 'COMPLETED' && !file.isDeleting) {
                        <div class="w-[50px] flex items-center justify-center">
                          @if (file.ext === 'PDF') {
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 512 512"
                            style="width: 20px; height: 20px"
                            fill="#f28b82"
                          >
                            <path
                              d="M0 64C0 28.7 28.7 0 64 0L224 0l0 128c0 17.7 14.3 32 32 32l128 0 0 144-208 0c-35.3 0-64 28.7-64 64l0 144-48 0c-35.3 0-64-28.7-64-64L0 64zm384 64l-128 0L256 0 384 128zM176 352l32 0c30.9 0 56 25.1 56 56s-25.1 56-56 56l-16 0 0 32c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-48 0-80c0-8.8 7.2-16 16-16zm32 80c13.3 0 24-10.7 24-24s-10.7-24-24-24l-16 0 0 48 16 0zm96-80l32 0c26.5 0 48 21.5 48 48l0 64c0 26.5-21.5 48-48 48l-32 0c-8.8 0-16-7.2-16-16l0-128c0-8.8 7.2-16 16-16zm32 128c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-16 0 0 96 16 0zm80-112c0-8.8 7.2-16 16-16l48 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 32 32 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 48c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-64 0-64z"
                            />
                          </svg>
                          } @if (file.ext === 'CSV') {
                          <ng-icon
                            name="faSolidFileCsv"
                            class="!text-[#81c995] text-xl"
                          ></ng-icon>
                          } @if (file.ext === 'MD') {
                          <ng-icon
                            name="faSolidFilePen"
                            class="!text-[#8ab4f8] text-xl"
                          ></ng-icon>
                          } @if (file.ext === 'TXT') {
                          <ng-icon
                            name="faSolidFileLines"
                            class="!text-[#8ab4f8] text-xl"
                          ></ng-icon>
                          } @if (file.ext === 'URL') {
                          <ng-icon
                            name="faSolidLink"
                            class="!text-[#c58af9] text-xl"
                          ></ng-icon>
                          } @if (file.ext === 'UNKNOWN') {
                          <ng-icon
                            name="faSolidFile"
                            class="t!ext-[#6F767E] text-xl"
                          ></ng-icon>
                          }
                        </div>
                        } @if (file.status === 'IN_PROGRESS' &&
                        !file.isDeleting) {
                        <div class="w-[50px] flex items-center justify-center">
                          <mat-spinner diameter="24"></mat-spinner>
                        </div>
                        } @if (file.status === 'ERROR' && !file.isDeleting) {
                        <div class="w-[50px] flex items-center justify-center">
                          <ng-icon
                            name="faSolidXmark"
                            class="text-red-500 text-xl"
                          ></ng-icon>
                        </div>
                        } @if (file.status === 'PENDING' && !file.isDeleting) {
                        <div class="w-[50px] flex items-center justify-center">
                          <ng-icon
                            name="heroClock"
                            class="text-yellow-400 text-xl"
                          ></ng-icon>
                        </div>
                        }
                        <div
                          class="w-[calc(100%-56px)] overflow-hidden text-left flex flex-col"
                        >
                          <div
                            class="truncate"
                            [dxTooltip]="file?.name"
                            dxTooltipPosition="above"
                            [ngClass]="
                              fileSelection.isSelected(file)
                                ? 'text-[#c2e7ff]'
                                : 'text-light-text dark:text-dark-text'
                            "
                          >
                            {{ file.name }}
                          </div>
                        </div>
                        <div class="flex items-center pr-3">
                          <ng-icon
                            name="heroEllipsisVertical"
                            (click)="$event.stopPropagation()"
                            [matMenuTriggerFor]="fileMenu"
                            class="text-light-text dark:text-dark-text text-xl hover:cursor-pointer menu-trigger"
                          ></ng-icon>
                        </div>
                        <mat-menu
                          #fileMenu="matMenu"
                          xPosition="after"
                          class="my-menu"
                        >
                          <button
                            *hasPermission="{
                              permissions: 3,
                              user_permissions: permissions
                            }"
                            class="!h-10 text-light-text dark:text-dark-text"
                            mat-menu-item
                            (click)="viewInfoFile(file)"
                          >
                            <div class="flex items-center">
                              <ng-icon
                                name="heroInformationCircle"
                                class="text-blue-500 text-2xl mr-3"
                              ></ng-icon>
                              <span class="text-light-text dark:text-dark-text"
                                >Info</span
                              >
                            </div>
                          </button>
                          <button
                            *hasPermission="{
                              permissions: 7,
                              user_permissions: permissions
                            }"
                            class="!h-10 text-light-text dark:text-dark-text"
                            mat-menu-item
                            (click)="
                              selectFile(file); openRenameFileDialog(file)
                            "
                          >
                            <div class="flex items-center">
                              <ng-icon
                                name="heroPencilSquare"
                                class="text-orange-500 text-2xl mr-3"
                              ></ng-icon>
                              <span class="text-light-text dark:text-dark-text"
                                >Rename</span
                              >
                            </div>
                          </button>
                          @if (file.ext === 'URL') {
                          <button
                            *hasPermission="{
                              permissions: 3,
                              user_permissions: permissions
                            }"
                            class="!h-10 text-light-text dark:text-dark-text"
                            mat-menu-item
                            (click)="retrainFile(file?.id)"
                          >
                            <div class="flex items-center">
                              <ng-icon
                                name="heroArrowPath"
                                class="text-green-500 text-2xl mr-3"
                              ></ng-icon>
                              <span class="text-light-text dark:text-dark-text"
                                >Recrawl</span
                              >
                            </div>
                          </button>
                          }
                          <button
                            *hasPermission="{
                              permissions: 7,
                              user_permissions: permissions
                            }"
                            class="!h-10 text-light-text dark:text-dark-text"
                            mat-menu-item
                            (click)="moveFile(file)"
                          >
                            <div class="flex items-center">
                              <svg
                                class="text-blue-500 mr-3"
                                width="24px"
                                height="24px"
                                viewBox="0 0 24 24"
                                focusable="false"
                                fill="#3B82F6"
                              >
                                <path fill="none" d="M0 0h24v24H0V0z"></path>
                                <path
                                  d="M20 6h-8l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 12H4V8h16v10zm-8.01-9l-1.41 1.41L12.16 12H8v2h4.16l-1.59 1.59L11.99 17 16 13.01 11.99 9z"
                                ></path>
                              </svg>
                              <span class="text-light-text dark:text-dark-text"
                                >Move</span
                              >
                            </div>
                          </button>
                          @if (searchingMode) {
                          <button
                            *hasPermission="{
                              permissions: 7,
                              user_permissions: permissions
                            }"
                            class="!h-10 text-light-text dark:text-dark-text"
                            mat-menu-item
                            (click)="goToFileLocation(file)"
                          >
                            <div class="flex items-center">
                              <ng-icon
                                name="heroFolder"
                                class="text-yellow-400 text-2xl mr-3"
                              ></ng-icon>
                              <span class="text-light-text dark:text-dark-text"
                                >Show file location</span
                              >
                            </div>
                          </button>
                          } @if (parentId) {
                          <button
                            *hasPermission="{
                              permissions: 5,
                              user_permissions: permissions
                            }"
                            class="!h-10 text-light-text dark:text-dark-text"
                            mat-menu-item
                            (click)="deleteFile(file)"
                          >
                            <div class="flex items-center">
                              <ng-icon
                                name="heroTrash"
                                class="text-red-500 text-2xl mr-3"
                              ></ng-icon>
                              <span class="text-light-text dark:text-dark-text"
                                >Delete</span
                              >
                            </div>
                          </button>
                          } @if (!parentId) { @if (isAllowAssign) {
                          <button
                            class="!h-10 text-light-text dark:text-dark-text"
                            mat-menu-item
                            (click)="deleteFile(file)"
                          >
                            <div class="flex items-center">
                              <ng-icon
                                name="heroTrash"
                                class="text-red-500 text-2xl mr-3"
                              ></ng-icon>
                              <span class="text-light-text dark:text-dark-text"
                                >Delete</span
                              >
                            </div>
                          </button>
                          } }
                        </mat-menu>
                      </div>
                      <div class="flex-grow px-3 pb-3">
                        <div
                          class="h-full bg-light-background dark:bg-dark-background rounded-[6px] flex items-center justify-center"
                        >
                          @if (file.ext === 'PDF') {
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 512 512"
                            style="width: 60px; height: 60px"
                            fill="#f28b82"
                          >
                            <path
                              d="M0 64C0 28.7 28.7 0 64 0L224 0l0 128c0 17.7 14.3 32 32 32l128 0 0 144-208 0c-35.3 0-64 28.7-64 64l0 144-48 0c-35.3 0-64-28.7-64-64L0 64zm384 64l-128 0L256 0 384 128zM176 352l32 0c30.9 0 56 25.1 56 56s-25.1 56-56 56l-16 0 0 32c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-48 0-80c0-8.8 7.2-16 16-16zm32 80c13.3 0 24-10.7 24-24s-10.7-24-24-24l-16 0 0 48 16 0zm96-80l32 0c26.5 0 48 21.5 48 48l0 64c0 26.5-21.5 48-48 48l-32 0c-8.8 0-16-7.2-16-16l0-128c0-8.8 7.2-16 16-16zm32 128c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-16 0 0 96 16 0zm80-112c0-8.8 7.2-16 16-16l48 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 32 32 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 48c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-64 0-64z"
                            />
                          </svg>
                          } @if (file.ext === 'CSV') {
                          <ng-icon
                            name="faSolidFileCsv"
                            class="!text-[#81c995] text-[60px]"
                          ></ng-icon>
                          } @if (file.ext === 'MD') {
                          <ng-icon
                            name="faSolidFilePen"
                            class="!text-[#8ab4f8] text-[60px]"
                          ></ng-icon>
                          } @if (file.ext === 'TXT') {
                          <ng-icon
                            name="faSolidFileLines"
                            class="!text-[#8ab4f8] text-[60px]"
                          ></ng-icon>
                          } @if (file.ext === 'URL') {
                          <ng-icon
                            name="faSolidLink"
                            class="!text-[#c58af9] text-[60px]"
                          ></ng-icon>
                          } @if (file.ext === 'UNKNOWN') {
                          <ng-icon
                            name="faSolidFile"
                            class="!text-[#6F767E] text-[60px]"
                          ></ng-icon>
                          }
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </div>
              </cdk-virtual-scroll-viewport>
            </div>
            }
          </div>
        </div>
        } @case ('list') {
        <div class="pr-3 h-full flex flex-col overflow-hidden">
          <div
            class="text-light-text dark:text-dark-text pl-4 w-full h-full mt-5 flex flex-col"
            [ngStyle]="{ height: 'calc(100% - 20px)', overflow: 'hidden' }"
            (clickOutside)="showStyle == 'list' && removeSelectedFolderOrFile()"
          >
            <div
              class="flex flex-col flex-grow overflow-hidden"
              [ngStyle]="{ height: 'calc(100% - 52px)' }"
            >
              <!--              <div class="w-full flex border-b py-2 border-[#444746] ">-->
              <div
                class="list-header border-b border-b-light-border-line dark:border-b-dark-border-line"
              >
                <div class="list-cell name">
                  <div class="flex items-center gap-2">
                    <p>Name</p>
                    <div class="ml-2">
                      <div
                        class="flex items-center justify-center p-1 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl"
                      >
                        @if (nameOrder === 'DESC') {
                        <ng-icon
                          (click)="changeFilter('ASC', 'name')"
                          (mouseup)="$event.stopPropagation()"
                          name="heroArrowDown"
                          class="text-blue-500"
                        ></ng-icon>
                        } @if (nameOrder === 'ASC') {
                        <ng-icon
                          (click)="changeFilter('DESC', 'name')"
                          (mouseup)="$event.stopPropagation()"
                          name="heroArrowUp"
                          class="text-blue-500"
                        ></ng-icon>
                        }
                      </div>
                    </div>
                  </div>
                </div>
                <div class="list-cell date">
                  <div class="flex items-center justify-center">
                    <p>Created at</p>
                    <div class="ml-2">
                      <div
                        class="flex items-center justify-center p-1 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl"
                      >
                        @if (createdOrder === 'DESC') {
                        <ng-icon
                          (click)="changeFilter('ASC', 'created_at')"
                          (mouseup)="$event.stopPropagation()"
                          name="heroArrowDown"
                          class="text-blue-500"
                        ></ng-icon>
                        } @if (createdOrder === 'ASC') {
                        <ng-icon
                          (click)="changeFilter('DESC', 'created_at')"
                          (mouseup)="$event.stopPropagation()"
                          name="heroArrowUp"
                          class="text-blue-500"
                        ></ng-icon>
                        }
                      </div>
                    </div>
                  </div>
                </div>
                <div class="list-cell date">
                  <div class="flex items-center justify-center">
                    <p>Last update</p>
                    <div class="ml-2">
                      <div
                        class="flex items-center justify-center p-1 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl"
                      >
                        @if (updateOrder === 'DESC') {
                        <ng-icon
                          (click)="changeFilter('ASC', 'updated_at')"
                          (mouseup)="$event.stopPropagation()"
                          name="heroArrowDown"
                          class="text-blue-500"
                        ></ng-icon>
                        } @if (updateOrder === 'ASC') {
                        <ng-icon
                          (click)="changeFilter('DESC', 'updated_at')"
                          (mouseup)="$event.stopPropagation()"
                          name="heroArrowUp"
                          class="text-blue-500"
                        ></ng-icon>
                        }
                      </div>
                    </div>
                  </div>
                </div>
                <div class="list-cell status">
                  <div class="flex items-center justify-center">Status</div>
                </div>
                <div class="list-cell actions"></div>
              </div>
              <cdk-virtual-scroll-viewport
                #listViewport
                id="listViewport"
                itemSize="50"
                minBufferPx="200"
                maxBufferPx="400"
                style="
                  height: calc(100% - 56px);
                  overflow: auto;
                  width: 100%;
                  flex-grow: 1;
                "
                class="h-full"
              >
                <ng-container
                  *cdkVirtualFor="
                    let item of combinedList;
                    let i = index;
                    trackBy: trackByItemId
                  "
                >
                  <!-- Hiển thị folder -->
                  @if (isFolder(item)) {
                  <div
                    (dblclick)="toFolder(item)"
                    (click)="selectFolder(item)"
                    [matContextMenuTriggerFor]="folderMenu"
                    [ngClass]="
                      folderSelection.isSelected(item)
                        ? 'bg-light-primary dark:bg-dark-primary text-light-text dark:text-dark-text'
                        : 'hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover'
                    "
                    class="list-row"
                  >
                    <div class="list-cell name">
                      <ng-icon
                        name="faSolidFolder"
                        class="text-yellow-400 text-xl inline-table mr-3 folder-icon"
                        (dblclick)="$event.stopPropagation(); toFolder(item)"
                      ></ng-icon>
                      <p
                        class="truncate folder-name"
                        [dxTooltip]="item.name"
                        dxTooltipPosition="above"
                        (dblclick)="$event.stopPropagation(); toFolder(item)"
                      >
                        {{ item.name }}
                      </p>
                    </div>
                    <div
                      class="list-cell date"
                      [dxTooltip]="getFormattedDateTime(item, 'create')"
                      [dxTooltipPosition]="'left'"
                    >
                      {{ getFormattedDate(item, "create") }}
                    </div>
                    <div
                      class="list-cell date"
                      [dxTooltip]="getFormattedDateTime(item, 'update')"
                      [dxTooltipPosition]="'left'"
                    >
                      {{ getFormattedDate(item, "update") }}
                    </div>
                    <div class="list-cell status"></div>
                    <div class="list-cell actions">
                      <ng-icon
                        name="heroEllipsisHorizontal"
                        (click)="$event.stopPropagation()"
                        [matMenuTriggerFor]="folderMenu"
                        class="text-light-text dark:text-dark-text text-xl hover:cursor-pointer menu-trigger"
                      ></ng-icon>
                      <mat-menu
                        #folderMenu="matMenu"
                        xPosition="after"
                        class="my-menu"
                      >
                        @if (isAllowAssign) {
                        <button
                          class="!h-10 text-light-text dark:text-dark-text"
                          mat-menu-item
                          (click)="
                            selectFolder(item); openAssignPermissionDialog()
                          "
                        >
                          <div class="flex items-center">
                            <ng-icon
                              name="heroUserPlus"
                              class="text-blue-500 text-2xl mr-3"
                            ></ng-icon>
                            <span class="text-light-text dark:text-dark-text"
                              >Assign role</span
                            >
                          </div>
                        </button>
                        }
                        <button
                          *hasPermission="{
                            permissions: 7,
                            user_permissions: item.users[0]
                              ? item.users[0].permission
                              : 0
                          }"
                          class="!h-10 text-light-text dark:text-dark-text"
                          mat-menu-item
                          (click)="
                            selectFolder(item); openCreateFolderDialog(item)
                          "
                        >
                          <div class="flex items-center">
                            <ng-icon
                              name="heroPencilSquare"
                              class="text-orange-500 text-2xl mr-3"
                            ></ng-icon>
                            <span class="text-light-text dark:text-dark-text"
                              >Rename</span
                            >
                          </div>
                        </button>
                        @if (searchingMode) {
                        <button
                          *hasPermission="{
                            permissions: 7,
                            user_permissions: permissions
                          }"
                          class="!h-10 text-light-text dark:text-dark-text"
                          mat-menu-item
                          (click)="goToFolderLocation(item)"
                        >
                          <div class="flex items-center">
                            <ng-icon
                              name="heroFolder"
                              class="text-yellow-400 text-2xl mr-3"
                            ></ng-icon>
                            <span class="text-light-text dark:text-dark-text"
                              >Show folder location</span
                            >
                          </div>
                        </button>
                        }
                        <button
                          *hasPermission="{
                            permissions: 5,
                            user_permissions: item.users[0]
                              ? item.users[0].permission
                              : 0
                          }"
                          class="!h-10 text-light-text dark:text-dark-text"
                          mat-menu-item
                          (click)="deleteFolder(item)"
                        >
                          <div class="flex items-center">
                            <ng-icon
                              name="heroTrash"
                              class="text-red-500 text-2xl mr-3"
                            ></ng-icon>
                            <span class="text-light-text dark:text-dark-text"
                              >Delete</span
                            >
                          </div>
                        </button>
                      </mat-menu>
                    </div>
                  </div>
                  }
                  <!-- Hiển thị file -->
                  @if (isFile(item)) {
                  <div
                    class="list-row border-b border-b-light-border-line dark:border-b-dark-border-line"
                    (dblclick)="viewContentFile(item)"
                    [matContextMenuTriggerFor]="fileMenu"
                    (click)="selectFile(item)"
                    [ngClass]="
                      fileSelection.isSelected(item)
                        ? 'bg-light-primary dark:bg-dark-primary text-light-text dark:text-dark-text'
                        : 'hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover'
                    "
                  >
                    <div class="list-cell name">
                      <!--                      <ng-container *ngIf="item.ext === 'PDF'">
                            <svg class="!inline-table mr-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"
                              style="width:20px;height:20px" fill="#f28b82">
                              <path
                                d="M0 64C0 28.7 28.7 0 64 0L224 0l0 128c0 17.7 14.3 32 32 32l128 0 0 144-208 0c-35.3 0-64 28.7-64 64l0 144-48 0c-35.3 0-64-28.7-64-64L0 64zm384 64l-128 0L256 0 384 128zM176 352l32 0c30.9 0 56 25.1 56 56s-25.1 56-56 56l-16 0 0 32c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-48 0-80c0-8.8 7.2-16 16-16zm32 80c13.3 0 24-10.7 24-24s-10.7-24-24-24l-16 0 0 48 16 0zm96-80l32 0c26.5 0 48 21.5 48 48l0 64c0 26.5-21.5 48-48 48l-32 0c-8.8 0-16-7.2-16-16l0-128c0-8.8 7.2-16 16-16zm32 128c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-16 0 0 96 16 0zm80-112c0-8.8 7.2-16 16-16l48 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 32 32 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 48c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-64 0-64z" />
                            </svg>
                          </ng-container>
                          <ng-icon *ngIf="item.ext === 'CSV'" name="faSolidFileCsv"
                          class="!text-[#81c995] text-xl !inline-table mr-3"></ng-icon>
                          <ng-icon *ngIf="item.ext === 'MD'" name="faSolidFilePen"
                          class="!text-[#8ab4f8] !inline-table text-xl mr-3"></ng-icon>
                          <ng-icon *ngIf="item.ext === 'TXT'" name="faSolidFileLines"
                          class="!text-[#8ab4f8] text-xl !inline-table mr-3"></ng-icon>
                          <ng-icon *ngIf="item.ext === 'URL'" name="faSolidLink"
                          class="!text-[#c58af9] text-xl !inline-table mr-3"></ng-icon>
                          <ng-icon *ngIf="item.ext === 'UNKNOWN'" name="faSolidFile"
                          class="!text-[#6F767E] text-xl !inline-table mr-3"></ng-icon>
                          -->
                      @switch(item.ext){ @case ('PDF'){
                      <svg
                        class="!inline-table mr-3"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 512 512"
                        style="width: 20px; height: 20px"
                        fill="#f28b82"
                      >
                        <path
                          d="M0 64C0 28.7 28.7 0 64 0L224 0l0 128c0 17.7 14.3 32 32 32l128 0 0 144-208 0c-35.3 0-64 28.7-64 64l0 144-48 0c-35.3 0-64-28.7-64-64L0 64zm384 64l-128 0L256 0 384 128zM176 352l32 0c30.9 0 56 25.1 56 56s-25.1 56-56 56l-16 0 0 32c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-48 0-80c0-8.8 7.2-16 16-16zm32 80c13.3 0 24-10.7 24-24s-10.7-24-24-24l-16 0 0 48 16 0zm96-80l32 0c26.5 0 48 21.5 48 48l0 64c0 26.5-21.5 48-48 48l-32 0c-8.8 0-16-7.2-16-16l0-128c0-8.8 7.2-16 16-16zm32 128c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-16 0 0 96 16 0zm80-112c0-8.8 7.2-16 16-16l48 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 32 32 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-32 0 0 48c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-64 0-64z"
                        />
                      </svg>
                      } @case ('CSV'){
                      <ng-icon
                        name="faSolidFileCsv"
                        class="!text-[#81c995] text-xl !inline-table mr-3"
                      ></ng-icon>
                      } @case ('MD'){
                      <ng-icon
                        name="faSolidFilePen"
                        class="!text-[#8ab4f8] !inline-table text-xl mr-3"
                      ></ng-icon>
                      } @case ('TXT'){
                      <ng-icon
                        name="faSolidFileLines"
                        class="!text-[#8ab4f8] text-xl !inline-table mr-3"
                      ></ng-icon>
                      } @case ('URL'){
                      <ng-icon
                        name="faSolidLink"
                        class="!text-[#c58af9] text-xl !inline-table mr-3"
                      ></ng-icon>
                      } @case ('UNKNOWN'){
                      <ng-icon
                        name="faSolidFile"
                        class="!text-[#6F767E] text-xl !inline-table mr-3"
                      ></ng-icon>
                      } @default {
                      <ng-icon
                        name="faSolidFile"
                        class="!text-[#6F767E] text-xl !inline-table mr-3"
                      ></ng-icon>
                      } }
                      <p
                        class="truncate"
                        [dxTooltip]="item.name"
                        dxTooltipPosition="above"
                      >
                        {{ item.name }}
                      </p>
                    </div>
                    <div
                      class="list-cell date"
                      [dxTooltip]="getFormattedDateTime(item, 'create')"
                      [dxTooltipPosition]="'left'"
                    >
                      {{ getFormattedDate(item, "create") }}
                    </div>
                    <div
                      class="list-cell date"
                      [dxTooltip]="getFormattedDateTime(item, 'update')"
                      [dxTooltipPosition]="'left'"
                    >
                      {{ getFormattedDate(item, "update") }}
                    </div>
                    <div class="list-cell status">
                      @switch (item.status) { @case ('COMPLETED') {
                      <mat-chip class="status-chip green">Completed</mat-chip>
                      } @case ('IN_PROGRESS') {
                      <mat-chip class="status-chip blue">
                        <div class="flex gap-2 items-center">
                          <mat-spinner diameter="24"></mat-spinner>
                          <p>Processing</p>
                        </div>
                      </mat-chip>
                      } @case ('ERROR') {
                      <mat-chip class="status-chip red">Error</mat-chip>
                      } @case ('PENDING') {
                      <mat-chip class="status-chip yellow">Pending</mat-chip>
                      } @default {
                      <mat-chip class="status-chip">-</mat-chip>
                      } }
                    </div>
                    <div class="list-cell actions">
                      <ng-icon
                        name="heroEllipsisHorizontal"
                        (click)="$event.stopPropagation()"
                        [matMenuTriggerFor]="fileMenu"
                        class="text-light-text dark:text-dark-text text-xl hover:cursor-pointer menu-trigger"
                      ></ng-icon>
                      <mat-menu
                        #fileMenu="matMenu"
                        xPosition="after"
                        class="my-menu bg-light-background dark:bg-dark-background"
                      >
                        <button
                          *hasPermission="{
                            permissions: 3,
                            user_permissions: permissions
                          }"
                          class="!h-10 text-light-text dark:text-dark-text"
                          mat-menu-item
                          (click)="viewInfoFile(item)"
                        >
                          <div class="flex items-center">
                            <ng-icon
                              name="heroInformationCircle"
                              class="text-blue-500 text-2xl mr-3"
                            ></ng-icon>
                            <span class="text-light-text dark:text-dark-text"
                              >Info</span
                            >
                          </div>
                        </button>
                        <button
                          *hasPermission="{
                            permissions: 7,
                            user_permissions: permissions
                          }"
                          class="!h-10 text-light-text dark:text-dark-text"
                          mat-menu-item
                          (click)="selectFile(item); openRenameFileDialog(item)"
                        >
                          <div class="flex items-center">
                            <ng-icon
                              name="heroPencilSquare"
                              class="text-orange-500 text-2xl mr-3"
                            ></ng-icon>
                            <span class="text-light-text dark:text-dark-text"
                              >Rename</span
                            >
                          </div>
                        </button>
                        @if (item.ext === 'URL') {
                        <button
                          *hasPermission="{
                            permissions: 3,
                            user_permissions: permissions
                          }"
                          class="!h-10 text-light-text dark:text-dark-text"
                          mat-menu-item
                          (click)="retrainFile(item?.id)"
                        >
                          <div class="flex items-center">
                            <ng-icon
                              name="heroArrowPath"
                              class="text-green-500 text-2xl mr-3"
                            ></ng-icon>
                            <span class="text-light-text dark:text-dark-text"
                              >Recrawl</span
                            >
                          </div>
                        </button>
                        }
                        <button
                          *hasPermission="{
                            permissions: 7,
                            user_permissions: permissions
                          }"
                          class="!h-10 text-light-text dark:text-dark-text"
                          mat-menu-item
                          (click)="moveFile(item)"
                        >
                          <div class="flex items-center">
                            <svg
                              class="text-blue-500 mr-3"
                              width="24px"
                              height="24px"
                              viewBox="0 0 24 24"
                              focusable="false"
                              fill="#3B82F6"
                            >
                              <path fill="none" d="M0 0h24v24H0V0z"></path>
                              <path
                                d="M20 6h-8l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 12H4V8h16v10zm-8.01-9l-1.41 1.41L12.16 12H8v2h4.16l-1.59 1.59L11.99 17 16 13.01 11.99 9z"
                              ></path>
                            </svg>
                            <span class="text-light-text dark:text-dark-text"
                              >Move</span
                            >
                          </div>
                        </button>
                        @if (searchingMode) {
                        <button
                          *hasPermission="{
                            permissions: 7,
                            user_permissions: permissions
                          }"
                          class="!h-10 text-light-text dark:text-dark-text"
                          mat-menu-item
                          (click)="goToFileLocation(item)"
                        >
                          <div class="flex items-center">
                            <ng-icon
                              name="heroFolder"
                              class="text-yellow-400 text-2xl mr-3"
                            ></ng-icon>
                            <span class="text-light-text dark:text-dark-text"
                              >Show file location</span
                            >
                          </div>
                        </button>
                        } @if (parentId) {
                        <button
                          *hasPermission="{
                            permissions: 5,
                            user_permissions: permissions
                          }"
                          class="!h-10 text-light-text dark:text-dark-text"
                          mat-menu-item
                          (click)="deleteFile(item)"
                        >
                          <div class="flex items-center">
                            <ng-icon
                              name="heroTrash"
                              class="text-red-500 text-2xl mr-3"
                            ></ng-icon>
                            <span class="text-light-text dark:text-dark-text"
                              >Delete</span
                            >
                          </div>
                        </button>
                        } @if (!parentId) { @if (isAllowAssign) {
                        <button
                          class="!h-10 text-light-text dark:text-dark-text"
                          mat-menu-item
                          (click)="deleteFile(item)"
                        >
                          <div class="flex items-center">
                            <ng-icon
                              name="heroTrash"
                              class="text-red-500 text-2xl mr-3"
                            ></ng-icon>
                            <span class="text-light-text dark:text-dark-text"
                              >Delete</span
                            >
                          </div>
                        </button>
                        } }
                      </mat-menu>
                    </div>
                  </div>
                  }
                </ng-container>
                <!-- Loading indicator for list view -->
                @if (isLoadingMore) {
                <div class="w-full flex justify-center items-center p-4">
                  <mat-spinner diameter="30"></mat-spinner>
                </div>
                }
              </cdk-virtual-scroll-viewport>
            </div>
          </div>
        </div>
        } }
      </div>
      }
    </div>
  </dx-card-content>
</dx-card>

<ng-template #createFolderDialog let-data>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text"
  >
    <div
      class="header border-bottom-gray pt-6 px-6 flex justify-between items-center pb-3"
    >
      <div class="text-2xl font-bold card-title capitalize">
        {{ data.isCreate ? "Add folder" : "Edit folder" }}
      </div>
      <div id="btn-close-dialog-create-intent-1" class="hover:cursor-pointer">
        <svg
          (click)="closeDialog()"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
            fill="#6F767E"
          />
        </svg>
      </div>
    </div>

    <div class="w-full px-6 pt-6 content overflow-auto max-h-[75vh]">
      <form [formGroup]="createFolderForm">
        <div
          class="border-light-gray rounded-2xl bg-light-background dark:bg-dark-background p-4"
        >
          <div class="mb-6">
            <mat-label class="required">Name</mat-label>
            <input
              id="input-name-folder-form"
              class="w-full mt-2 bg-transparent border-light-gray px-4 h-[40px] rounded-lg"
              type="text"
              placeholder="Folder name"
              formControlName="name"
              (keydown)="onKeyDownFolderDialog($event)"
            />
            <app-error-message
              [control]="createFolderForm.get('name')"
              [name]="'Folder name'"
            ></app-error-message>
          </div>
        </div>
      </form>
    </div>

    <div class="footer p-6 flex justify-end">
      <div class="flex">
        <button
          id="btn-close-dialog-create-intent-2"
          class="cursor-pointer h-[40px] min-w-[120px] px-3 rounded-2xl text-light-text dark:text-dark-text bg-light-secondary-background dark:bg-dark-secondary-background border border-light-border-line"
          (click)="closeDialog()"
        >
          <span>Cancel</span>
        </button>

        <app-loading-button
          [id]="'btn-submit-create-folder-form'"
          [class]="
            'h-[40px] min-w-[120px] bg-light-user-chat-background dark:bg-dark-user-chat-background text-white px-3 rounded-2xl border-light-gray button-disabled hover:cursor-pointer'
          "
          [disabled]="createFolderForm.invalid"
          [style]="'margin-left: 12px;'"
          [loading]="isCreatingFolder"
          [color]="'white'"
          (click)="saveCreatingFolder()"
        >
          <span>Save</span>
        </app-loading-button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #renameFileDialog let-data>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text"
  >
    <div
      class="header border-bottom-gray pt-6 px-6 flex justify-between items-center pb-3"
    >
      <div class="text-2xl font-bold card-title capitalize">Rename file</div>
      <div id="btn-close-dialog-create-intent-1" class="hover:cursor-pointer">
        <svg
          (click)="closeDialog()"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
            fill="#6F767E"
          />
        </svg>
      </div>
    </div>

    <div class="w-full px-6 pt-6 content overflow-auto max-h-[75vh]">
      <div
        class="border border-gray-200 dark:border-gray-600 rounded-2xl bg-light-secondary-background dark:bg-dark-secondary-background p-4"
      >
        <div class="mb-6">
          <mat-label class="required" for="name">Name</mat-label>
          <input
            id="input-name-folder-form"
            class="w-full mt-2 bg-light-background dark:bg-dark-background border border-gray-200 dark:border-gray-600 px-4 h-[40px] rounded-lg"
            type="text"
            placeholder="File name"
            [(ngModel)]="fileRename.new_name"
          />
        </div>
      </div>
    </div>

    <div class="footer p-6 flex justify-end">
      <div class="flex">
        <button
          id="btn-close-dialog-create-intent-2"
          class="cursor-pointer h-[40px] min-w-[120px] px-3 rounded-2xl bg-light-secondary-background dark:bg-dark-secondary-background border border-gray-200 dark:border-gray-600"
          (click)="closeDialog()"
        >
          <span>Cancel</span>
        </button>

        <app-loading-button
          [id]="'btn-submit-create-folder-form'"
          [class]="
            'h-[40px] min-w-[120px] bg-light-user-chat-background dark:bg-dark-user-chat-background px-3 rounded-2xl border border-gray-200 dark:border-gray-600 button-disabled hover:cursor-pointer'
          "
          [style]="'margin-left: 12px;'"
          [loading]="isCreatingFolder"
          [color]="'white'"
          (click)="saveRenameFile()"
        >
          <span>Save</span>
        </app-loading-button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #upFileDialog>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text p-6 space-y-4"
  >
    <!--        header-->
    <div
      class="header border-b border-b-light-border-line dark:border-b-dark-border-line flex justify-between items-center pb-3"
    >
      <div class="flex items-center space-x-2">
        <p class="text-2xl font-bold card-title capitalize">Upload file</p>
        <p class="text-sm">(.pdf, .csv, .txt, .md, .docx)</p>
      </div>
      <div>
        <svg
          (click)="closeDialog()"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
            fill="#6F767E"
          />
        </svg>
      </div>
    </div>

    <div class="content w-full overflow-auto space-y-4">
      <form [formGroup]="uploadFileForm" enctype="multipart/form-data">
        <div class="flex items-center">
          <button
            class="h-[40px] min-w-[120px] mr-3 bg-light-primary dark:bg-dark-primary px-3 rounded-full cursor-pointer border-0 text-white"
            (click)="fileUploadRef.click()"
          >
            Select file
          </button>
          <input
            #fileUploadRef
            type="file"
            id="file"
            name="file"
            class="hidden"
            [accept]="['.csv', '.pdf', '.txt', '.md', '.docx']"
            (change)="uploadFile($event)"
          />
          <label class="ml-2" for="file">
            {{ documentName ? documentName : "" }}
          </label>
        </div>
      </form>

      @if (checkFileCsv(documentName)) {
      <div class="flex-1 mb-2">
        <label for="metadata_columns">Metadata columns:</label>
        <mat-form-field class="w-full form-field-custom">
          <input
            class="!text-light-white"
            type="text"
            matInput
            id="metadata_columns"
            appTrimString
            trim="blur"
            [(ngModel)]="metadata"
          />
        </mat-form-field>
      </div>
      }
    </div>
    <!-- Actions -->
    <div class="footer flex justify-end space-x-3">
      <button
        class="bg-red-600 h-[40px] min-w-[120px] px-3 rounded-full cursor-pointer text-white"
        (click)="closeDialog()"
      >
        <span>Cancel</span>
      </button>
      <app-loading-button
        [id]="'btn-submit-upload-file-form'"
        [class]="
          'h-[40px] min-w-[120px] bg-light-green dark:bg-dark-green text-white px-3 rounded-full border-light-gray button-disabled hover:cursor-pointer'
        "
        [disabled]="!fileUpload"
        [style]="'margin-left: 12px;'"
        [loading]="isImportingFile"
        [color]="'white'"
        (click)="saveFileUpload(loadUploadFile)"
      >
        <span>Upload file</span>
      </app-loading-button>
    </div>
  </div>
</ng-template>

<ng-template #importUrlDialog>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text p-6"
  >
    <!--        header-->
    <div class="header border-b flex justify-between items-center pb-3">
      <div class="text-2xl font-bold card-title capitalize">
        Import from Url
      </div>
      <div>
        <svg
          (click)="closeDialog()"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
            fill="#6F767E"
          />
        </svg>
      </div>
    </div>

    <div class="w-full pt-2 px-[2px] content overflow-auto">
      <div class="flex-1 mb-2">
        <label for="metadata_columns"
          >Enter the URL of the page you want to import</label
        >
        <input
          class="w-full mt-2 py-2 text-light-text dark:text-dark-text bg-transparent border border-light-border-line dark:border-dark-border-line focus:outline-none focus:border-primary rounded-md px-4"
          type="text"
          id="import-url"
          [(ngModel)]="rootURL"
          placeholder="URL address, e.g. https://dxgpt.ai"
          (keydown.enter)="importUrl()"
        />
      </div>
    </div>

    <div class="mt-4">
      <mat-radio-group
        aria-label="Select an option"
        class="flex flex-col gap-4 my-2"
        [(ngModel)]="modeGetUrl"
      >
        <mat-radio-button
          labelPosition="after"
          value="getOne"
          class="rounded-lg border-2 border-light-border-line dark:border-dark-border-line py-2 pl-4 pr-2"
          [ngClass]="{
            'border-light-primary dark:border-dark-primary':
              modeGetUrl === 'getOne'
          }"
        >
          <div class="w-full justify-start text-light-text dark:text-dark-text">
            <h5 class="font-semibold">Individual URL</h5>
            <p>Import the entire content of a single webpage.</p>
          </div>
        </mat-radio-button>
        <mat-radio-button
          labelPosition="after"
          value="getAll"
          class="rounded-lg border-2 border-light-border-line dark:border-dark-border-line py-2 pl-4 pr-2"
          [ngClass]="{
            'border-light-primary dark:border-dark-primary':
              modeGetUrl === 'getAll'
          }"
        >
          <div class="w-full justify-start text-light-text dark:text-dark-text">
            <h5 class="font-semibold">Site-wide (all URLs)</h5>
            <p>
              Import all the webpages of a website. Each webpage will be
              imported as a separate Article.
            </p>
          </div>
        </mat-radio-button>
      </mat-radio-group>
    </div>
    <!-- Actions -->
    <div class="footer mt-6 flex justify-end">
      <button
        class="cursor-pointer h-[40px] min-w-[120px] bg-light-red dark:bg-dark-red text-white px-3 rounded-full"
        (click)="closeDialog()"
      >
        <span>Cancel</span>
      </button>
      <app-loading-button
        [id]="'btn-submit-import-url-form'"
        [class]="
          'h-[40px] min-w-[120px] bg-light-user-chat-background dark:bg-dark-user-chat-background text-white px-3 rounded-full border-light-gray button-disabled hover:cursor-pointer'
        "
        [disabled]="rootURL === ''"
        [style]="'margin-left: 12px;'"
        [loading]="isImportingUrl"
        [color]="'white'"
        (click)="importUrl()"
      >
        <span>Import</span>
      </app-loading-button>
    </div>
  </div>
</ng-template>

<ng-template #assignPermissionFolderDialog let-data>
  <div class="text-light-text dark:text-dark-text h-full">
    <div
      class="header border-bottom-gray pt-6 px-6 flex justify-between items-center pb-3"
    >
      <div class="text-2xl font-bold card-title capitalize">
        Share {{ data.countFolder }}
        {{ data.countFolder > 1 ? "folders" : "folder" }}
      </div>
      <div
        id="btn-close-dialog-assign-permissions-1"
        class="hover:cursor-pointer"
      >
        <svg
          (click)="closeDialog()"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
            fill="#6F767E"
          />
        </svg>
      </div>
    </div>

    <div
      class="w-full px-6 content overflow-auto max-h-[75vh]"
      [ngClass]="folderSelection.selected.length == 1 ? 'pt-6' : ''"
    >
      @if (!(folderSelection.selected.length > 1)) {
      <app-select
        class="app-search-custom block my-2 create-tool-select"
        [placeholder]="'Select user'"
        [listOptions]="listUserInAIAllowed"
        [setOptionValue]="'id'"
        [setOptionLabel]="'email'"
        [value]="getAssignPermissionsFormArrayValue('user_id')"
        (valueChange)="addAssignPermissions($event)"
      >
      </app-select>
      <!--<app-search-input
          class="app-search-custom block my-2 create-tool-select background-bold"
          [placeholder]="'Select user'"
          [listOptions]="listUserInAIAllowed" [setOptionValue]="'id'" [setOptionLabel]="'email'"
          [value]="getAssignPermissionsFormArrayValue('user_id')" (valueChange)="addAssignPermissions($event)"
        [setCanBeSearch]="false" [haveTooltip]="false">

        </app-search-input>-->
      }
      <form [formGroup]="assignFolderForm" class="mt-6">
        <h1 class="mb-3">Users with access</h1>
        @for (assignPermission of assignPermissionsFormArray.controls; track
        assignPermission; let i = $index) {
        <div class="w-full mb-4 grid grid-cols-8 gap-5 items-center h-12">
          <div class="col-span-4 h-full flex items-center space-x-3">
            <div
              class="w-10 aspect-square rounded-full bg-[#00BFFF] flex items-center justify-center"
            >
              {{ assignPermission.get("username")?.value | charFirst }}
            </div>
            <div class="flex flex-col space-y-1">
              <div class="text-light-text dark:text-dark-text truncate">
                {{ assignPermission.get("username")?.value || "" }}
              </div>
              @if (assignPermission.get('folder_id')?.value) {
              <div class="flex flex-col items-stretch">
                <div class="flex items-center space-x-2">
                  <div class="flex items-center justify-center">
                    <ng-icon
                      name="faSolidFolderClosed"
                      class="text-gray-500 text-md"
                    ></ng-icon>
                  </div>
                  <div
                    class="w-[calc(100%-26px)] overflow-hidden text-left flex flex-col"
                  >
                    <div class="truncate text-gray-500 text-sm">
                      {{
                        getFolderById(
                          assignPermission.get("folder_id")?.value || 0
                        )?.name || ""
                      }}
                    </div>
                  </div>
                </div>
              </div>
              }
            </div>
          </div>
          <div class="col-span-4 h-full w-full flex items-center">
            <app-select
              class="app-search-custom w-full block create-tool-select !text-[12px]"
              [placeholder]="'Select permissions'"
              [listOptions]="listPermissions"
              [setOptionValue]="'value'"
              [setOptionLabel]="'label'"
              [value]="
                getAssignPermissionsFormArrayValueIndex('permissions', i)
              "
              (valueChange)="selectPermissions($event, i)"
            >
            </app-select>
            <!--              <app-search-input class="app-search-custom w-full block create-tool-select background-bold !text-[12px]"-->
            <!--                [placeholder]="'Select permissions'" [listOptions]="listPermissions" [setOptionValue]="'value'"-->
            <!--                [setOptionLabel]="'label'" [value]="getAssignPermissionsFormArrayValueIndex('permissions', i)"-->
            <!--                (valueChange)="selectPermissions($event, i)" [setCanBeSearch]="false" [haveTooltip]="false"-->
            <!--                [multiple]="true" [showClear]="true"-->
            <!--              [isDisable]="(folderSelection.selected.length > 1)"></app-search-input>-->
          </div>
        </div>
        }
      </form>
    </div>

    @if (!(folderSelection.selected.length > 1)) {
    <div class="footer p-6 flex justify-end">
      <div class="flex">
        <button
          id="btn-close-dialog-assign-permissions-2"
          class="cursor-pointer h-[40px] min-w-[120px] px-3 rounded-2xl bg-light-secondary-background dark:bg-dark-secondary-background border-light-gray"
          (click)="closeDialog()"
        >
          <span>Cancel</span>
        </button>
        <app-loading-button
          [id]="'btn-submit-intent-form'"
          [class]="
            'h-[40px] min-w-[120px] bg-light-user-chat-background dark:bg-dark-user-chat-background text-white px-3 rounded-2xl border-light-gray button-disabled hover:cursor-pointer'
          "
          [disabled]="assignFolderForm.invalid"
          [style]="'margin-left: 12px;'"
          [loading]="isSavingPermission"
          [color]="'white'"
          (click)="savePermissionAssign()"
        >
          <span>Save</span>
        </app-loading-button>
      </div>
    </div>
    }
  </div>
  vs
</ng-template>

<ng-template #viewContentDialog let-data>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text p-6"
  >
    <!--        header-->
    <div class="header pb-3 border-b flex justify-between items-center">
      <div class="text-2xl font-bold card-title capitalize">
        File content
        <label
          class="custom-toggle-switch ml-3 text-[14px] inline-flex items-center cursor-pointer"
        >
          <span class="mr-2">Edit</span>
          <input
            type="checkbox"
            class="sr-only peer"
            [checked]="editMode"
            (input)="handleEditModeChange($event)"
          />
          <div
            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-light-primary dark:peer-checked:bg-dark-primary"
          ></div>
        </label>
      </div>
      <div>
        <svg
          (click)="closeDialog()"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="cursor-pointer"
        >
          <path
            d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
            fill="#6F767E"
          />
        </svg>
      </div>
    </div>

    <div
      class="content w-full h-full max-h-[60vh] mt-2 text-light-text dark:text-dark-text overflow-auto relative"
    >
      <button
        type="button"
        class="button-copy absolute right-2 top-2 py-1 px-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        (click)="copyText(selectedFile?.text_content)"
      >
        <ng-icon name="faSolidCopy" class="button-icon text-white"></ng-icon>
      </button>
      @if (!editMode) {
      <div>
        @if (selectedFile?.text_content) {
        <div
          #contentDiv
          [innerText]="selectedFile.text_content"
          class="min-h-[40vh] leading-8 text-light-text dark:text-dark-text"
        ></div>
        } @else {
        <div
          class="leading-8 flex items-center justify-center min-h-[40vh] text-light-text dark:text-dark-text"
        >
          Empty content
        </div>
        }
      </div>
      } @if (editMode) {
      <textarea
        [(ngModel)]="draftContent"
        #editTextarea
        class="min-h-[40vh] edit-textarea leading-8 w-full text-light-text dark:text-dark-text bg-light-background dark:bg-dark-background"
      ></textarea>
      }
    </div>
    <!-- Actions -->
    @if (editMode) {
    <div class="footer mt-6 flex justify-end gap-4">
      <button
        class="bg-[#ff0000] cursor-pointer h-[40px] min-w-[100px] px-3 rounded-full text-white"
        (click)="cancelSaveRetrain()"
      >
        <span>Cancel</span>
      </button>
      <button
        class="bg-light-primary cursor-pointer h-[40px] min-w-[100px] px-3 rounded-full text-white"
        (click)="confirmRetrain()"
      >
        <span>Save and retrain</span>
      </button>
    </div>
    }
  </div>
</ng-template>

<ng-template #viewFileInfoDialog let-data>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text p-6"
  >
    <!--        header-->
    <div class="header pb-3 border-b flex justify-between items-center">
      <div class="text-2xl font-bold card-title capitalize">File info</div>
      <div class="cursor-pointer">
        <svg
          (click)="closeDialog()"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
            fill="#6F767E"
          />
        </svg>
      </div>
    </div>

    <div
      class="content w-full max-h-[60vh] mt-2 text-light-text dark:text-dark-text overflow-auto"
    >
      @if (data.name) {
      <div class="font-bold text-lg mb-2 flex items-center gap-2">
        <p>File name</p>
        <ng-icon
          name="heroPencilSquare"
          (click)="toggleEdit(data)"
          class="text-2xl text-orange-500 cursor-pointer"
        ></ng-icon>
      </div>
      @if (!isEditing) {
      <div class="leading-8">
        {{ data.name }}
      </div>
      } @if (isEditing) {
      <div class="leading-8 w-full px-[2px]">
        <div class="flex gap-2 items-center">
          <input
            class="w-full mt-2 bg-transparent border border-gray-200 dark:border-gray-600 px-4 h-[40px] rounded-lg text-light-text dark:text-dark-text"
            type="text"
            placeholder="File name"
            [(ngModel)]="fileRename.new_name"
            (keydown.enter)="saveRenameFile()"
          />
          <button
            class="mt-2 bg-light-primary dark:bg-dark-primary text-white px-3 py-1 rounded-lg"
            (click)="saveRenameFile()"
          >
            Save
          </button>
          <button
            class="mt-2 bg-red-500 text-white px-3 py-1 rounded-lg"
            (click)="cancelEdit()"
          >
            Cancel
          </button>
        </div>
      </div>
      } } @if (data.url) {
      <div class="font-bold text-lg mb-2 mt-5">URL</div>
      <a
        [href]="data.url"
        class="leading-8 text-light-primary"
        target="_blank"
        >{{ data.url }}</a
      >
      } @if (data.created_at) {
      <div class="font-bold text-lg mb-2 mt-5">Created at</div>
      <div class="leading-8">
        {{ data.created_at | date : " HH:mm:ss dd/MMM/yyyy" : timeZone }}
      </div>
      } @if (data.updated_at) {
      <div class="font-bold text-lg mb-2 mt-5">Last updated at</div>
      <div class="leading-8">
        {{ data.updated_at | date : " HH:mm:ss dd/MMM/yyyy" : timeZone }}
      </div>
      }
    </div>
    <!-- Actions -->
    <div class="footer mt-6 flex justify-end">
      <button
        class="cursor-pointer h-[40px] min-w-[100px] px-3 rounded-full hover:bg-light-secondary-background dark:hover:bg-dark-secondary-background"
        (click)="closeDialog()"
      >
        <span>Close</span>
      </button>
    </div>
  </div>
</ng-template>

<ng-template #loadUploadFile>
  <mat-spinner diameter="40"></mat-spinner>
</ng-template>

<ng-template #moveFileDialog let-data>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text p-6 w-full"
  >
    <!--        header-->
    <div class="header flex justify-between items-center">
      <div class="text-2xl font-bold card-title capitalize">
        Move "{{ data.name }}"
      </div>
      <div class="cursor-pointer">
        <svg
          (click)="closeDialog()"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
            fill="#6F767E"
          />
        </svg>
      </div>
    </div>

    <div class="flex flex-wrap pb-3 pt-4 border-b space-x-3 hidden lg:flex">
      <div
        class="max-w-[300px] text-light-text dark:text-dark-text text-lg truncate flex items-center hover:cursor-pointer hover:!text-light-text dark:hover:!text-dark-text"
        [ngClass]="breadcrumbsMoveDialog.length ? '!text-[#94a2b8]' : ''"
        (click)="toBreadcrumbItemMoveDialog(undefined)"
      >
        My Knowledge Base
      </div>
      @if (breadcrumbsMoveDialog.length > 0) { @for (breadcrumb of
      breadcrumbsMoveDialog; track breadcrumb; let first = $first) {
      <div
        class="max-w-[300px] text-[#94a2b8] text-lg truncate text-left flex items-center hover:cursor-pointer hover:!text-light-text dark:text-dark-text"
        [ngClass]="
          breadcrumb.id === parentIdMoveDialog
            ? '!text-light-text dark:text-dark-text'
            : ''
        "
        (click)="toBreadcrumbItemMoveDialog(breadcrumb)"
      >
        <app-custom-icon
          iconName="heroChevronRight"
          class="!text-xl !text-[#94a2b8]"
        ></app-custom-icon>
        {{ breadcrumb.name }}
      </div>
      } }
    </div>

    <div class="mt-2 h-80">
      @if (!loadingMoveDialog) { @for (folder of foldersCanMoveTo; track folder;
      let i = $index) {
      <div
        class="flex gap-2 cursor-pointer py-2 rounded-xl"
        [ngClass]="
          folder.id === selectedFolderMoveId
            ? 'bg-[#2d1a66] text-light-text dark:text-dark-text'
            : 'hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover'
        "
        (click)="onFolderMoveClick(folder.id)"
        (dblclick)="goToNextFolder(folder)"
      >
        <div class="w-[50px] flex items-center justify-center">
          <ng-icon
            name="faSolidFolder"
            class="text-yellow-400 text-2xl"
          ></ng-icon>
        </div>
        {{ folder.name }}
      </div>
      } } @else {
      <div class="flex items-center justify-center h-full">
        <mat-spinner diameter="40"></mat-spinner>
      </div>
      }
    </div>

    <!-- Actions -->
    <div class="footer mt-6 flex justify-end gap-4">
      <button
        class="bg-light-secondary-background dark:bg-dark-secondary-background cursor-pointer h-[40px] min-w-[100px] px-3 rounded-full"
        (click)="closeDialog()"
      >
        <span>Close</span>
      </button>
      <button
        class="bg-light-primary text-white cursor-pointer h-[40px] min-w-[100px] px-3 rounded-full"
        (click)="moveFileToFolder()"
      >
        <span>Save</span>
      </button>
    </div>
  </div>
</ng-template>
