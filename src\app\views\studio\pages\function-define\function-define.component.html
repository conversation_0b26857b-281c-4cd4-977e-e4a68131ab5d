<div class="h-full flex flex-col overflow-hidden">
  <div class="flex items-start justify-between">
    <h1
      class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
    >
      Function
    </h1>
    <app-flow-env-select></app-flow-env-select>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-7 gap-y-4 md:gap-x-4 h-full mt-6">
    <!--    list event-->
    <div class="col-span-2 flex flex-col w-full">
      <div
        class="rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
      >
        <div
          class="p-4 flex flex-wrap 2xl:flex-nowrap gap-2 2xl:gap-0 2xl:space-x-2 items-center justify-between border-b border-primary-border dark:border-dark-primary-border"
        >
          <dx-form-field
            class="w-full"
            id="search"
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <app-svg-icon
              dxPrefix
              type="icSearch"
              class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
            ></app-svg-icon>
            <input
              dxInput
              [(ngModel)]="searchModel.key_word"
              (ngModelChange)="searchSubject.next($event)"
              [type]="'text'"
              placeholder="Search by name"
            />
          </dx-form-field>
          <button
            class="flex-shrink-0 w-full 2xl:w-fit"
            dx-button="filled"
            [disabled]="studioStoreStatus() === STUDIO_STATUS.LIVE"
            (click)="createFunction()"
          >
            Add function
          </button>
        </div>

        <div class="h-full list-functions overflow-y-auto">
          @for (data of listFunction(); track $index; let first = $first, last =
          $last) {
          <div
            class="p-4 space-x-3 flex items-center justify-between hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover cursor-pointer group"
            [ngClass]="{
              'border-b border-primary-border dark:border-dark-primary-border':
                !last,
              'rounded-b-2xl': last,
              'bg-base-400-hover dark:bg-dark-base-400-hover':
                data.id === functionSelectedId()
            }"
            (click)="selectFunction(data?.id)"
          >
            <div class="flex space-x-3 items-center justify-start">
              <div class="w-5 h-5">
                <app-svg-icon
                  type="icFunction"
                  class="w-5 h-5 !text-neutral-content dark:!text-dark-neutral-content group-hover:!text-primary-hover dark:group-hover:!text-dark-primary-hover"
                  [ngClass]="{
                    '!text-primary-hover dark:!text-dark-primary-hover':
                      data.id === functionSelectedId()
                  }"
                ></app-svg-icon>
              </div>
              <div
                class="w-full text-neutral-content dark:text-dark-neutral-content text-ellipsis group-hover:text-base-content group-hover:dark:text-dark-base-content"
                [ngClass]="{
                  '!text-base-content dark:!text-dark-base-content':
                    data.id === functionSelectedId()
                }"
              >
                {{ data.name }}
              </div>
            </div>
            @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
            <div class="flex space-x-3 items-center justify-end">
              <app-svg-icon
                type="icEdit"
                class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer hover:text-primary-hover dark:hover:text-dark-primary-hover"
                (click)="editFunction(data, $event)"
              ></app-svg-icon>
              <app-svg-icon
                type="icTrash"
                class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                (click)="deleteFunction(data, $event)"
              ></app-svg-icon>
            </div>
            }
          </div>
          } @empty {
          <div class="flex items-center justify-center py-4">
            <div
              class="text-neutral-content dark:text-dark-neutral-content italic text-[15px]"
            >
              No data available
            </div>
          </div>
          }
        </div>
      </div>
    </div>

    <!--    edit event-->
    <div
      class="col-span-5 flex flex-col w-full h-full rounded-2xl border border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
    >
      <div
        class="flex items-end bg-base-100 dark:bg-dark-base-100 rounded-t-2xl"
      >
        @for (tab of listFunctionTab(); track $index ;let last = $last) {
        <div class="flex items-end">
          @if (tab.id === functionSelectedId()) {
          <app-svg-icon
            type="icRightCorner"
            class="w-[11px] h-4 !text-base-200 dark:!text-dark-base-200"
          ></app-svg-icon>
          }
          <div
            class="px-4 py-3 space-x-2 flex items-center text-neutral-content dark:text-dark-neutral-content cursor-pointer"
            [ngClass]="{
              'bg-base-200 dark:bg-dark-base-200 text-base-content dark:text-dark-base-content':
                tab.id === functionSelectedId(),
              'rounded-t-2xl group': tab.id == functionSelectedId(),
              'text-neutral-content dark:text-dark-neutral-content':
                tab.id !== functionSelectedId()
            }"
            (click)="selectFunction(tab.id)"
          >
            <app-svg-icon
              type="icFunction"
              class="w-5 h-5 group-hover:!text-primary-hover dark:group-hover:!text-dark-primary-hover"
            ></app-svg-icon>
            <div class="text-base font-semibold">
              {{ tab.name }}
            </div>
            @if (tab.isDirty) {
            <div class="w-3 h-3 bg-error bg-dark-error rounded-full"></div>
            } @else { @if (!(listFunction().length === 1 ||
            listFunctionTab().length === 1)) {
            <app-svg-icon
              type="icClose"
              class="w-5 h-5 cursor-pointer"
              (click)="removeFunctionSelectedTab($index, $event)"
            ></app-svg-icon>
            } }
          </div>
          @if (tab.id === functionSelectedId()) {
          <app-svg-icon
            type="icRightCorner"
            class="w-[11px] h-4 scale-x-[-1] !text-base-200 dark:!text-dark-base-200"
          ></app-svg-icon>
          }
        </div>
        <div
          class="items-center justify-center flex h-full text-primary-border dark:text-dark-primary-border text-xl"
        >
          |
        </div>
        } @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
        <div
          class="flex h-12 w-12 items-center justify-center !text-neutral-content dark:!text-dark-neutral-content"
        >
          <app-svg-icon
            type="icPlus"
            class="w-6 h-6 cursor-pointer"
            (click)="createFunction()"
          ></app-svg-icon>
        </div>
        }
      </div>

      <div class="flex-1 overflow-y-auto flex flex-col p-6">
        @if(functionSelected() && listFunctionTab().length > 0) {
        <div class="w-full h-full relative flex flex-col">
          <div
            class="flex-1 mb-14 bg-white dark:bg-dark-secondary-background py-6 rounded-xl border border-primary-border dark:border-dark-primary-border"
          >
            <ngx-monaco-editor
              [options]="editorOptions()"
              [(ngModel)]="code"
              (ngModelChange)="onChangeCode()"
            ></ngx-monaco-editor>
          </div>
          <div
            class="absolute bottom-0 left-0 right-0 flex items-center justify-end sm:justify-start"
          >
            <button
              dxLoadingButton="filled"
              [loading]="isSubmitting()"
              [disabled]="studioStoreStatus() === STUDIO_STATUS.LIVE"
              (click)="saveFunction(true)"
            >
              Save
            </button>
          </div>
        </div>
        } @else{
        <div
          class="w-full h-full flex items-center justify-center text-neutral-content dark:text-dark-neutral-content"
        >
          Start adding your first Function to select
        </div>
        }
      </div>
    </div>
  </div>
</div>

<!--
<div class="cs-drawer-wrapper bg-transparent text-light-text dark:text-dark-text rounded-lg flex flex-col">
  <div class="drawer-header flex justify-between border-b border-b-light-border-line dark:border-b-dark-border-line">
    <div class="func-info w-full">
      <span class="text-[28px] font-bold text-base-content dark:text-dark-base-content">Function</span>
      <button
        class="cursor-pointer h-[40px] min-w-[120px] bg-light-primary px-4 py-2 rounded-lg text-white flex items-center justify-center"
        style="margin-left: 12px;"
        (click)="saveFunction(true)"
      >
        <span class="text-sm">Save</span>
      </button>
    </div>
  </div>

  <div class="flex relative h-full">
    <div class="list-func border-r border-r-light-border-line dark:border-r-dark-border-line relative">
      <div class="h-full">
        <div class="py-6 px-8">
          <input
            class="w-full p-2 border rounded-md focus:outline-none bg-light-secondary-background dark:bg-dark-secondary-background"
            type="text" placeholder="Search by name"
            [(ngModel)]="searchModel.key_word" (keydown.enter)="doSearch($event)">
        </div>
        <div class="overflow-y-auto" style="height: calc(100% - 141px) ; max-height: 66.6vh">
          @if (listFunction.length === 0) {
            <div class="func-item !justify-center hover:!cursor-default !bg-transparent">
              <div>No function added</div>
            </div>
          }
          @if (listFunction.length > 0) {
            @for (func of listFunction; track func) {
              <div class="func-item" (click)="selectFunction(func.id)"
                   [class.activate]="func.id === functionSelectedId">
                <div class="truncate">{{ func.name }}</div>
                <div class="flex items-center space-x-2">
                  <ng-icon (click)="editFunction(func, $event)" class="text-[#fa6505]"
                           name="heroPencilSquareMini"></ng-icon>
                  <ng-icon (click)="deleteFunction(func, $event)" class="text-red-500" name="heroTrashMini"></ng-icon>
                </div>
              </div>
            }
          }
        </div>
      </div>
      <div
        class="px-6 py-4 absolute left-0 bottom-0 right-0 border-t border-t-light-border-line dark:border-t-dark-border-line rounded-bl flex items-center justify-center">
        <button type="button" class="bg-light-primary px-4 py-2 rounded-lg flex items-center space-x-1 text-white"
                (click)="createFunction()">
          <ng-icon name="heroPlusMini" class="text-2xl"></ng-icon>
          <span>Add new function</span>
        </button>
      </div>
    </div>

    <div class="flex-1 flex flex-col h-full">
      <div class="messages h-full overflow-y-auto">
        <div class="chat-content-container h-full">
          <div class="w-full h-full flex flex-col">
            @if (listFunction.length > 0) {
              <div class="h-12 bg-gray-50">
                <ng-scrollbar
                  class="scroll-custom-horizontal"
                  track="horizontal"
                  appearance="compact"
                  visibility="hover"
                >
                  @if (listFunction.length > 0) {
                    <div class="flex items-start">
                      @for (funcTab of listFunctionTab; track funcTab; let index = $index) {
                        <div
                          class="w-48 h-12 flex items-center justify-between space-x-4 px-3 border-r hover:cursor-pointer"
                          [ngClass]="funcTab.id === functionSelectedId ? 'border-t-2 border-t-light-primary bg-white': ''"
                          (click)="selectFunction(funcTab.id)">
                          <div class="truncate"
                               [class.font-bold]="funcTab.id === functionSelectedId">{{ funcTab.name }}
                          </div>
                          @if (!funcTab?.isDirty) {
                            <div (click)="removeFunctionSelectedTab(index, $event)">
                              <ng-icon name="heroXMarkSolid"></ng-icon>
                            </div>
                          }
                          @if (funcTab?.isDirty) {
                            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                          }
                        </div>
                      }
                      <div
                        (click)="createFunction()"
                        class="w-12 h-12 bg-gray-100 border-r flex items-center justify-center hover:cursor-pointer hover:bg-gray-200">
                        <ng-icon class="text-lg" name="heroPlusSolid"></ng-icon>
                      </div>
                    </div>
                  }
                </ng-scrollbar>
              </div>
            }

            @if (functionSelected) {
              &lt;!&ndash;              <form [formGroup]="formGroupFunctionDetail" class="w-full">
                                <div class="p-4 w-full flex items-center justify-between space-x-4">
                                  <div class="flex-grow w-full flex items-center space-x-3">
                                    <app-search-input
                                      class="select-custom"
                                      [listOptions]="REST_API_METHOD_LIST"
                                      [setOptionValue]="'value'"
                                      [setOptionLabel]="'label'"
                                      [value]="formGroupApiDetail.get('method').value"
                                      (valueChange)="formGroupApiDetail.patchValue({ method: $event })"
                                      [setCanBeSearch]="false"
                                      [haveTooltip]="false"
                                    ></app-search-input>
                                    <input class="w-5/6 h-[40px] p-2 border rounded-md focus:outline-none bg-gray-50" type="text"
                                           formControlName="url">
                                  </div>
                                  <div class="flex items-center space-x-3">
                                    <button type="button"
                                            class="border border-light-orange bg-white px-4 py-2 rounded-lg flex items-center space-x-1 text-light-orange button-disabled h-[40px]"
                                            [disabled]="!apiSelected.url"
                                            (click)="testApi()">
                                      <span class="whitespace-nowrap">Send</span>
                                    </button>
                                    <button type="button"
                                            class="bg-light-primary px-4 py-2 rounded-lg flex items-center space-x-1 text-white h-[40px]"
                                            (click)="saveApi(true)">
                                      <span class="whitespace-nowrap">Save</span>
                                    </button>
                                  </div>
                                </div>
                              </form>&ndash;&gt;
              <div class="flex-grow w-full">
                <ngx-monaco-editor [options]="editorOptions" [(ngModel)]="code"></ngx-monaco-editor>
              </div>
              <div class="px-6 py-4">
                @if (functionSelectedId) {
                  <button type="button"
                          class="bg-light-primary px-3 py-2 rounded-lg flex items-center space-x-1 text-white"
                          (click)="saveFunction(true)">
                    <span>Save</span>
                  </button>
                }
              </div>
            }
            @if (!functionSelected) {
              <div class="w-full h-full flex justify-center items-center">
                <div class="mt-16">Start adding your first function</div>
              </div>
            }
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
-->
