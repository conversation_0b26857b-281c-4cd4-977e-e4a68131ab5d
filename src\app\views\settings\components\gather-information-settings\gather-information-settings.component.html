<div class="settings-tab-content w-full p-6 flex flex-col space-y-6">
  <div class="flex items-center space-x-2">
    <dx-slide-toggle [formControl]="enabled"></dx-slide-toggle>
    <div
      class="text-xl font-bold text-base-content dark:text-dark-base-content"
    >
      Gather information
    </div>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div class="lg:w-3/4 flex flex-wrap items-center space-x-4">
    <dx-form-field class="flex-1">
      <dx-label>AI Model</dx-label>
      <dx-select
        placeholder="Choose the AI model you would like this agent to use"
      ></dx-select>
    </dx-form-field>
    <dx-form-field class="flex-1">
      <dx-label>Initial question count</dx-label>
      <input
        dxInput
        type="number"
        placeholder="Enter how many initial questions the agent should ask before gathering information"
      />
    </dx-form-field>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div class="w-full space-y-4">
    <div
      class="text-lg font-bold text-base-content dark:text-dark-base-content"
    >
      Default parameters
    </div>
    <div
      class="min-h-[50px] p-4 rounded-xl border border-primary-border dark:border-dark-primary-border flex flex-col items-center"
    >
      <div class="w-full flex flex-col items-stretch gap-4 mb-4">
        <!--          header-->
        <div class="grid grid-cols-24 gap-4">
          <div class="col-span-1 flex items-center"></div>
          <div class="col-span-3 flex items-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Key</span
            >
          </div>
          <div class="col-span-3 flex items-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Type</span
            >
          </div>
          <div class="col-span-5 flex items-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Description</span
            >
          </div>
          <div class="col-span-8 flex items-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Note</span
            >
          </div>
          <div class="col-span-2 flex items-center justify-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Required</span
            >
          </div>
          <div class="col-span-2 flex items-center">
            <span class="text-sm font-semibold text-transparent">Action</span>
          </div>
        </div>

        @for (param of default_parameters.value; track $index) {
        <div
          draggable="false"
          (dragstart)="onDragStart($event, $index)"
          (dragover)="onDragOver($event)"
          (drop)="onDrop($event, $index)"
          class="grid grid-cols-24 gap-4"
        >
          <div class="col-span-1 flex items-center justify-between">
            <app-svg-icon
              type="icDrag"
              class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"
            ></app-svg-icon>
          </div>
          <div class="col-span-3 flex items-center justify-between">
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <input
                dxInput
                [disabled]="true"
                [ngModel]="param.key"
                placeholder="Key"
              />
            </dx-form-field>
          </div>
          <div class="col-span-3 flex items-center justify-between">
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <dx-select
                [disabled]="true"
                [ngModel]="param.type"
                placeholder="Type"
              >
                @for (type of paramTypes; track $index) {
                <dx-option [value]="type.value"> {{ type.label }}</dx-option>
                }
              </dx-select>
            </dx-form-field>
          </div>
          <div class="col-span-5 flex items-center justify-between">
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <input
                dxInput
                [ngModel]="param.description"
                (ngModelChange)="
                  changeDefaultParam($event, $index, 'description')
                "
                placeholder="Description"
              />
            </dx-form-field>
          </div>
          <div class="col-span-8 flex items-center justify-between">
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <input
                dxInput
                [ngModel]="param.note"
                (ngModelChange)="changeDefaultParam($event, $index, 'note')"
                placeholder="Note"
              />
            </dx-form-field>
          </div>
          <div class="col-span-2 flex items-center justify-center">
            <dx-checkbox
              [ngModel]="param.is_required"
              (ngModelChange)="
                changeDefaultParam($event, $index, 'is_required')
              "
            ></dx-checkbox>
          </div>
          <div class="col-span-2 flex items-center justify-center"></div>
        </div>
        }
      </div>
    </div>
  </div>

  <div class="w-full mb-8 space-y-4">
    <div class="w-full flex items-center justify-between">
      <div
        class="text-lg font-bold text-base-content dark:text-dark-base-content"
      >
        Custom parameters
      </div>
      <button dxButton (click)="addParam()">Add parameters</button>
    </div>
    <div
      class="min-h-[50px] p-4 rounded-xl border border-primary-border dark:border-dark-primary-border flex flex-col items-center"
    >
      <div class="w-full flex flex-col items-stretch gap-4 mb-4">
        <!--          header-->
        <div class="grid grid-cols-24 gap-4">
          <div class="col-span-1 flex items-center"></div>
          <div class="col-span-3 flex items-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Key</span
            >
          </div>
          <div class="col-span-3 flex items-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Type</span
            >
          </div>
          <div class="col-span-5 flex items-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Description</span
            >
          </div>
          <div class="col-span-8 flex items-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Note</span
            >
          </div>
          <div class="col-span-2 flex items-center justify-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Required</span
            >
          </div>
          <div class="col-span-2 flex items-center justify-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Action</span
            >
          </div>
        </div>

        @for (param of parameters.value; track $index) {
        <div
          draggable="true"
          (dragstart)="onDragStart($event, $index)"
          (dragover)="onDragOver($event)"
          (drop)="onDrop($event, $index)"
          class="grid grid-cols-24 gap-4"
        >
          <div class="col-span-1 flex items-center justify-between">
            <app-svg-icon
              type="icDrag"
              class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-grab"
            ></app-svg-icon>
          </div>
          <div class="col-span-3 flex items-center justify-between">
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <input
                dxInput
                [ngModel]="param.key"
                (ngModelChange)="changeCustomParam($event, $index, 'key')"
                placeholder="Key"
              />
            </dx-form-field>
          </div>
          <div class="col-span-3 flex items-center justify-between">
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <dx-select
                [ngModel]="param.type"
                (ngModelChange)="changeCustomParam($event, $index, 'type')"
                placeholder="Type"
              >
                @for (type of paramTypes; track $index) {
                <dx-option [value]="type.value"> {{ type.label }}</dx-option>
                }
              </dx-select>
            </dx-form-field>
          </div>
          <div class="col-span-5 flex items-center justify-between">
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <input
                dxInput
                [ngModel]="param.description"
                (ngModelChange)="
                  changeCustomParam($event, $index, 'description')
                "
                placeholder="Description"
              />
            </dx-form-field>
          </div>
          <div class="col-span-8 flex items-center justify-between">
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <input
                dxInput
                [ngModel]="param.note"
                (ngModelChange)="changeCustomParam($event, $index, 'note')"
                placeholder="Note"
              />
            </dx-form-field>
          </div>
          <div class="col-span-2 flex items-center justify-center">
            <dx-checkbox
              [ngModel]="param.is_required"
              (ngModelChange)="changeCustomParam($event, $index, 'is_required')"
            ></dx-checkbox>
          </div>
          <div class="col-span-2 flex items-center justify-center">
            <app-svg-icon
              type="icTrash"
              (click)="deleteParam($index)"
              class="w-6 h-6 !text-error dark:!text-dark-error"
            ></app-svg-icon>
          </div>
        </div>
        }
      </div>
    </div>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div class="flex flex-col lg:w-3/4">
    <dx-form-field>
      <dx-label>Webhook</dx-label>
      <app-svg-icon
        dxPrefix
        type="icLinkPrefix"
        class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
      ></app-svg-icon>
      <input
        dxInput
        placeholder="Paste url here"
        [formControl]="webhook_set_user_data"
      />
    </dx-form-field>
  </div>
</div>
