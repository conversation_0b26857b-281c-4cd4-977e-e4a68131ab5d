<div class="h-full flex flex-col overflow-hidden">
  <div class="flex items-start justify-between">
    <h1
      class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
    >
      Agent - Tool
    </h1>
    <!-- <app-flow-env-select></app-flow-env-select> -->
  </div>

  <div class="mt-6 h-full flex flex-col w-full">
    <div class="w-full h-full grid grid-cols-8 gap-x-4">
      <div class="col-span-3 grid grid-cols-7 gap-x-4">
        <div
          class="col-span-6 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
        >
          <!--          header-->
          <div class="flex flex-col space-y-4 justify-between">
            <div
              class="text-2xl font-bold text-base-content dark:text-dark-base-content"
            >
              Tools
            </div>
            <div class="flex space-x-4 items-center justify-between">
              <dx-form-field
                class="flex items-center justify-start"
                [style.margin-bottom]="0"
                [style.--dx-form-field-label-offset-y]="0"
                [subscriptHidden]="true"
              >
                <input
                  dxInput
                  [type]="'text'"
                  [ngModel]="searchAgentTerm()"
                  (ngModelChange)="searchAgentTerm.set($event)"
                  placeholder="Search by name"
                />
              </dx-form-field>
              @if (studioStore.status() === STUDIO_STATUS.DEV) {
                <div class="flex items-center justify-end">
                  <button
                    dxButton="filled"
                    (click)="openCreateToolDialog()"
                    class="px-4 py-2"
                  >
                    <div class="flex items-center justify-between space-x-2">
                      <span class="text-sm font-medium">Add Tool</span>
                    </div>
                  </button>
                </div>
              }
            </div>
          </div>
          <!--          content-->
          <div class="h-full">
            <div
              cdkDropList
              [cdkDropListData]="filteredTools()"
              [cdkDropListConnectedTo]="connectedDropLists()"
              id="tool-source"
              class="h-full overflow-auto list-tools w-full space-y-4"
            >
              @for (tool of filteredTools(); track tool.id) {
                <div
                  cdkDrag
                  [cdkDragData]="tool"
                  (cdkDragStarted)="onDragStarted($event)"
                  (cdkDragEnded)="onDragEnded($event)"
                  class="rounded-2xl flex flex-col border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400 cursor-grab"
                >
                  <div class="px-6 py-5 flex flex-col relative">
                    <div class="flex justify-between space-x-3">
                      <div class="flex items-center space-x-3">
                        <app-svg-icon
                          type="icTool"
                          class="w-6 h-6 !text-base-content dark:!text-dark-base-content"
                        ></app-svg-icon>
                        <div
                          class="text-base-content dark:text-dark-base-content text-base font-bold items-end !cursor-pointer hover:!underline"
                          (click)="onEditFlowTool(tool)"
                        >
                          {{ tool.name }}
                        </div>
                      </div>
                      @if (studioStore.status() === STUDIO_STATUS.DEV) {
                        <ng-icon
                          name="heroEllipsisHorizontal"
                          class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                          cdkOverlayOrigin
                          #trigger="cdkOverlayOrigin"
                          (click)="
                        openToolAction.set(
                          openToolAction() ? undefined : tool.id
                        );
                        $event.stopPropagation()
                      "
                        >
                        </ng-icon>
                        <ng-template
                          cdkConnectedOverlay
                          [cdkConnectedOverlayOrigin]="trigger"
                          [cdkConnectedOverlayOpen]="openToolAction() === tool.id"
                          [cdkConnectedOverlayPush]="true"
                          [cdkConnectedOverlayPositions]="[
                                  {
                                    originX: 'start',
                                    originY: 'center',
                                    overlayX: 'end',
                                    overlayY: 'top',
                                    offsetY: 10,
                                  },
                                  {
                                    originX: 'start',
                                    originY: 'center',
                                    overlayX: 'end',
                                    overlayY: 'bottom',
                                    offsetY: 10
                                  },
                                ]"
                        >
                          <ul
                            class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                            (clickOutside)="openToolAction.set(undefined)"
                          >
                            <!-- <li>
                              <button
                                (click)="
                                  $event.stopPropagation(); onPublishTool(tool)
                                "
                                    class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                                  >
                                    <app-svg-icon
                                      type="icCheck"
                                      class="w-6 h-6 flex items-center justify-center"
                                    ></app-svg-icon>
                                    <div
                                      class="flex items-center justify-between text-[16px] font-medium"
                                    >
                                      Publish
                                    </div>
                                  </button>
                                </li> -->
                            <li>
                              <button
                                (click)="$event.stopPropagation(); onEditTool(tool)"
                                class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                              >
                                <app-svg-icon
                                  type="icEdit"
                                  class="w-6 h-6 flex items-center justify-center"
                                ></app-svg-icon>
                                <div
                                  class="flex items-center justify-between text-[16px] font-medium"
                                >
                                  Edit
                                </div>
                              </button>
                            </li>
                            <li>
                              <button
                                (click)="
                              $event.stopPropagation(); onDeleteTool(tool)
                            "
                                class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                              >
                                <app-svg-icon
                                  type="icTrash"
                                  class="w-6 h-6 flex items-center justify-center"
                                ></app-svg-icon>
                                <div
                                  class="flex items-center justify-between text-[16px] font-medium"
                                >
                                  Delete
                                </div>
                              </button>
                            </li>
                          </ul>
                        </ng-template>
                      }
                    </div>
                    <div
                      class="mt-2 ml-9 text-neutral-content dark:text-neutral-content text-[13px] font-normal"
                    >
                      {{ tool.description }}
                    </div>
                  </div>
                  <!--                  <div class="drag-handle m-2 w-6 h-1 mx-auto bg-gray-300 dark:bg-gray-500 rounded-full"></div>-->
                  <!--<div class="flex justify-between items-start">
                          <div>
                            <h3 class="font-medium">{{ tool.name }}</h3>
                            @if (studioStore.status() === STUDIO_STATUS.LIVE) {
                              <div
                                class="text-xs text-light-text/70 dark:text-dark-text/70 mt-1"
                              >
                                Version: {{ tool.name }}
                              </div>
                            }
                          </div>
                        </div>
                        <p class="text-sm text-light-text/70 dark:text-dark-text/70 mt-2">
                          {{ tool.description }}
                        </p>

                        <div class="drag-handle mt-2 w-6 h-1 mx-auto bg-gray-300 dark:bg-gray-500 rounded-full"></div>-->
                </div>
              } @empty {
                <div
                  class="text-neutral-content dark:text-dark-neutral-content flex items-center justify-center text-sm py-4"
                >
                  <span>No data available</span>
                </div>
              }
            </div>
          </div>
        </div>
        <div class="col-span-1 flex items-center justify-center">
          <div class="w-16 h-16 flex items-center justify-center p-[11px]">
            <ng-icon
              name="heroArrowRight"
              class="text-[42px] !text-neutral-content dark:!text-dark-neutral-content"
            ></ng-icon>
          </div>
        </div>
      </div>

      <div
        class="col-span-5 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
      >
        <div class="flex flex-col space-y-4 justify-between">
          <div
            class="text-2xl font-bold text-base-content dark:text-dark-base-content"
          >
            Agents
          </div>
          <div class="flex space-x-4 items-center justify-between">
            <dx-form-field
              class="flex md:w-[376px] items-center justify-start"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <input
                dxInput
                [type]="'text'"
                [ngModel]="searchAgentTerm()"
                (ngModelChange)="searchAgentTerm.set($event)"
                placeholder="Search by name"
              />
            </dx-form-field>
            @if (studioStore.status() === STUDIO_STATUS.DEV) {
              <div class="flex items-center justify-end">
                <button
                  dxButton="filled"
                  (click)="openCreateAgentDialog()"
                  class="px-4 py-2"
                >
                  <div class="flex items-center justify-between space-x-2">
                    <span class="text-sm">Add Agent</span>
                  </div>
                </button>
              </div>
            }
          </div>
        </div>

        <div class="flex-1 flex flex-col overflow-y-auto list-agents">
          @for (agent of filteredAgents(); track agent.id) {
            <div class="mb-4 relative">
              <div
                [id]="'agent-drop-' + agent.id"
                cdkDropList
                [cdkDropListData]="agent.tools || []"
                [cdkDropListConnectedTo]="connectedDropLists()"
                (cdkDropListDropped)="onDrop($event, agent.id || 0)"
                class="rounded-2xl flex flex-col border border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
              >
                <div
                  class="rounded-2xl px-6 py-5 flex items-center justify-between bg-base-400 dark:bg-dark-base-400"
                  [ngClass]="{
                  'outline outline-primary dark:outline-primary':
                    selectingAgentId() === agent.id
                }"
                >
                  <div class="flex items-center space-x-4">
                    <div class="w-fit py-1 flex justify-between items-center">
                      <div
                        class="rounded-full w-10 h-10 px-[10px] pt-[8px] pb-[12px] bg-primary justify-between items-center"
                      >
                        <app-svg-icon
                          type="icUnion"
                          class="w-5 h-5 rounded-full !text-white"
                        ></app-svg-icon>
                      </div>
                    </div>
                    <div class="flex flex-col space-x-2">
                      <div
                        class="text-base-content dark:text-dark-base-content text-xl font-bold"
                      >
                        {{ agent.name }}
                      </div>
                      <div
                        class="text-neutral-content dark:text-dark-neutral-content text-[15px] font-normal"
                      >
                        {{ agent.description }}
                      </div>
                    </div>
                  </div>
                  <div class="flex justify-end items-center space-x-2">
                    <div
                      class="text-[15px] font-semibold whitespace-nowrap text-primary dark:text-dark-primary cursor-pointer"
                      (click)="selectAgent(agent.id)"
                    >
                      {{ agent.tools?.length || 0 }} tools
                    </div>
                    <ng-icon
                      [name]="
                      agent.id && agent.id === selectingAgentId()
                        ? 'heroChevronUp'
                        : 'heroChevronDown'
                    "
                      class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                      (click)="selectAgent(agent.id)"
                    ></ng-icon>
                    <ng-icon
                      name="heroEllipsisHorizontal"
                      class="ml-4 text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                      (click)="
                      $event.stopPropagation();
                      openAgentAction.set(
                        openAgentAction() ? undefined : agent.id
                      )
                    "
                      cdkOverlayOrigin
                      #trigger="cdkOverlayOrigin"
                    ></ng-icon>
                    <ng-template
                      cdkConnectedOverlay
                      [cdkConnectedOverlayOrigin]="trigger"
                      [cdkConnectedOverlayOpen]="openAgentAction() === agent.id"
                      [cdkConnectedOverlayPush]="true"
                      [cdkConnectedOverlayPositions]="[
                        {
                          originX: 'start',
                          originY: 'center',
                          overlayX: 'end',
                          overlayY: 'top',
                          offsetY: 10,
                        },
                        {
                          originX: 'start',
                          originY: 'center',
                          overlayX: 'end',
                          overlayY: 'bottom',
                          offsetY: 10
                        },
                      ]"
                    >
                      <ul
                        class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                        (clickOutside)="openAgentAction.set(undefined)"
                      >
                        <li>
                          <button
                            (click)="$event.stopPropagation(); onViewAgent(agent)"
                            class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                          >
                            <app-svg-icon
                              type="icShow"
                              class="w-6 h-6 flex items-center justify-center"
                            ></app-svg-icon>
                            <div
                              class="flex items-center justify-between text-[16px] font-medium"
                            >
                              View
                            </div>
                          </button>
                        </li>
                        <li>
                          <button
                            (click)="$event.stopPropagation(); onEditAgent(agent)"
                            class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                          >
                            <app-svg-icon
                              type="icEdit"
                              class="w-6 h-6 flex items-center justify-center"
                            ></app-svg-icon>
                            <div
                              class="flex items-center justify-between text-[16px] font-medium"
                            >
                              Edit
                            </div>
                          </button>
                        </li>
                        <li>
                          <button
                            (click)="
                            $event.stopPropagation(); onDeleteAgent(agent)
                          "
                            class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                          >
                            <app-svg-icon
                              type="icTrash"
                              class="w-6 h-6 flex items-center justify-center"
                            ></app-svg-icon>
                            <div
                              class="flex items-center justify-between text-[16px] font-medium"
                            >
                              Delete
                            </div>
                          </button>
                        </li>
                      </ul>
                    </ng-template>
                  </div>
                </div>
                @if (agent.id && agent.id === selectingAgentId()) {
                  <div>
                    <div
                      cdkDropList
                      [id]="'agent-tools-' + agent.id"
                      [cdkDropListData]="agent.tools || []"
                      [cdkDropListConnectedTo]="connectedDropLists()"
                      (cdkDropListDropped)="onDrop($event, agent.id!)"
                      (click)="$event.stopPropagation()"
                      class="pr-6 pl-[29px] min-h-[100px] rounded-lg py-4 drop-area overflow-y-auto space-y-4"
                    >
                      @if (agent.tools && agent.tools.length > 0) {
                        @for (tool of agent.tools; track tool.id) {
                          <div
                            cdkDrag
                            [cdkDragData]="tool"
                            (cdkDragStarted)="onDragStarted($event)"
                            (cdkDragEnded)="onDragEnded($event)"
                            class="flex justify-between items-center cursor-move agent-tool-item"
                          >
                            <div class="space-x-2 w-full">
                              <div class="flex flex-row items-center space-x-2">
                                <app-svg-icon
                                  type="icDrag"
                                  class="w-6 h-6 !text-neutral-content"
                                ></app-svg-icon>
                                <div
                                  class="px-6 py-4 w-full flex rounded-2xl border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                                >
                                  <div class="w-full py-1 flex space-x-3 items-center">
                                    <app-svg-icon
                                      type="icTool"
                                      class="w-6 h-6 !text-base-content dark:!text-dark-base-content"
                                    ></app-svg-icon>

                                    <div class="flex flex-col space-x-2">
                                      <div
                                        class="text-base-content dark:text-dark-base-content text-base font-bold"
                                      >
                                        {{ tool.name }}
                                      </div>
                                      @if (studioStore.status() === STUDIO_STATUS.LIVE) {
                                        <div
                                          class="text-xs text-dark-neutral-content dark:text-dark-neutral-content"
                                        >
                                          Version: {{ tool.name }}
                                        </div>
                                      }
                                    </div>
                                  </div>
                                  <div class="flex justify-end space-x-4">
                                    @if (studioStore.status() === STUDIO_STATUS.DEV) {
                                      <div
                                        class="absolute top-0 right-0 mt-4 mr-4 p-1 rounded-full flex space-x-3"
                                      >
                                        <ng-icon
                                          name="heroXMark"
                                          class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                                          (click)="
                                  removeToolFromAgent(
                                    agent.id || 0,
                                    tool.id || 0
                                  );
                                  $event.stopPropagation()
                                "
                                        ></ng-icon>
                                        <ng-icon
                                          name="heroEllipsisHorizontal"
                                          class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                                          cdkOverlayOrigin
                                          #trigger="cdkOverlayOrigin"
                                          (click)="
                                  openToolAgentAction.set(
                                    openToolAgentAction() ? undefined : tool.id
                                  );
                                  $event.stopPropagation()
                                "
                                        >
                                        </ng-icon>
                                        <ng-template
                                          cdkConnectedOverlay
                                          [cdkConnectedOverlayOrigin]="trigger"
                                          [cdkConnectedOverlayOpen]="
                                  openToolAgentAction() === tool.id
                                "
                                          [cdkConnectedOverlayPush]="true"
                                          [cdkConnectedOverlayPositions]="[
                                      {
                                        originX: 'start',
                                        originY: 'center',
                                        overlayX: 'end',
                                        overlayY: 'top',
                                        offsetY: 10,
                                      },
                                      {
                                        originX: 'start',
                                        originY: 'center',
                                        overlayX: 'end',
                                        overlayY: 'bottom',
                                        offsetY: 10
                                      },
                                    ]"
                                        >
                                          <ul
                                            class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                                            (clickOutside)="
                                    openToolAgentAction.set(undefined)
                                  "
                                          >
                                            <!-- <li>
                                              <button
                                                (click)="
                                                  $event.stopPropagation();
                                                  onPublishTool(tool)
                                                "
                                                class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                                              >
                                                <app-svg-icon
                                                  type="icCheck"
                                                  class="w-6 h-6 flex items-center justify-center"
                                                ></app-svg-icon>
                                                <div
                                                  class="flex items-center justify-between text-[16px] font-medium"
                                                >
                                                  Publish
                                                </div>
                                              </button>
                                            </li> -->
                                            <li>
                                              <button
                                                (click)="
                                        $event.stopPropagation();
                                        onEditTool(tool)
                                      "
                                                class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                                              >
                                                <app-svg-icon
                                                  type="icEdit"
                                                  class="w-6 h-6 flex items-center justify-center"
                                                ></app-svg-icon>
                                                <div
                                                  class="flex items-center justify-between text-[16px] font-medium"
                                                >
                                                  Edit
                                                </div>
                                              </button>
                                            </li>
                                            <li>
                                              <button
                                                (click)="
                                        $event.stopPropagation();
                                        onDeleteTool(tool)
                                      "
                                                class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                                              >
                                                <app-svg-icon
                                                  type="icTrash"
                                                  class="w-6 h-6 flex items-center justify-center"
                                                ></app-svg-icon>
                                                <div
                                                  class="flex items-center justify-between text-[16px] font-medium"
                                                >
                                                  Delete
                                                </div>
                                              </button>
                                            </li>
                                          </ul>
                                        </ng-template>
                                      </div>
                                    }
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        }
                      } @else {
                        <div
                          class="text-center text-light-text/50 dark:text-dark-text/50 py-4"
                        >
                          No data available
                        </div>
                      }
                    </div>
                  </div>
                }
              </div>
            </div>
          }
        </div>
      </div>
    </div>
  </div>
</div>
