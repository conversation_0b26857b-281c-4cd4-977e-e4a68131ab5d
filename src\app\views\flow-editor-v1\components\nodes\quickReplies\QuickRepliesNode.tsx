// @ts-nocheck
import GoTo<PERSON><PERSON>Handle from "@flow-editor-v1/components/flow/GoToBlockHandle";
import InputField from "@flow-editor-v1/components/form/InputField";
import InputFieldSuffix from "@flow-editor-v1/components/form/InputFieldSuffix";
import SelectField from "@flow-editor-v1/components/form/SelectField";
import Icon from "@flow-editor-v1/components/icon/icon";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor-v1/components/styled";
import NodeTooltip from "@flow-editor-v1/components/tooltip/NodeTooltip";
import { useFlowInstance } from "@flow-editor-v1/hook";
import { lang } from "@flow-editor-v1/init";
import { FlowDebugState, IQuickRepliesAction, LayoutState, QuickRepliesNodeData, VariableDefState } from "@flow-editor-v1/model";
import {
  useBuildFlowState,
  useFlowDebugState,
  useFlowInstanceState,
  useLayoutState,
  useStudioState,
  useVariableDefState
} from "@flow-editor-v1/store";
import { isValidConnection } from "@flow-editor-v1/utils/flow";
import { DragDropContext, Draggable, Droppable } from "@hello-pangea/dnd";
import { yupResolver } from "@hookform/resolvers/yup";
import { STUDIO_STATUS } from "@shared/app.constant";
import { ColorPicker, Modal, Popover, Spin, Typography } from "antd";
import _, { debounce } from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { Handle, Position, useUpdateNodeInternals } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";
import { translationApi } from "../../../api/translationApi";
import NodeHeader from "../NodeHeader";

const StyledHandleSourceAnchor = styled.div<{ $bgColor, $color, $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => props.$color};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => props.$data ? 'normal' : 'italic'};
  background: ${(props) =>
    props.$bgColor ? props.$bgColor : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const messageFormSchema = yup.object().shape({
  type: yup.string().required(),
  value: yup.string().required("Message is required"),
  language: yup.string().required()
});

const actionFormSchema = yup.object().shape({
  type: yup.string().required("Type is required"),
  label: yup.string().required("Label is required"),
  bgColor: yup.string().required("Background color is required"),
  textColor: yup.string().required("Text color is required"),
  value: yup.string(),
  link: yup.string(),
  text_aliases: yup.array().of(yup.string().required()),
  action_id: yup.string()
});

const quickRepliesNodeDataFormSchema = yup.object().shape({
  messages: yup.array().of(messageFormSchema).required(),
  actions: yup.array().of(actionFormSchema).required(),
  var_store_id: yup.number().nullable()
});

const QuickRepliesNode = ({data}: { data: QuickRepliesNodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [modalLanguageOpen, setModalLanguageOpen] = useState<string | null>(null);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [reversed, setReverseHandle] = useState<boolean>(false);
  const [handlePositions, setHandlePositions] = useState<number[]>([]);
  const [nodeMessages, setNodeMessages] = useState(data.messages || []);
  const {flowInstance} = useFlowInstanceState(state => state);
  const {variablesDev} = useVariableDefState<VariableDefState>(state => state);
  const {status} = useStudioState(state => state)
  const {debug} = useFlowDebugState<FlowDebugState>(state => state);
  const inputWithPopoverRef = useRef({});
  const {setDirty} = useBuildFlowState((state) => state);
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const updateNodeInternal = useUpdateNodeInternals();
  const {theme} = useLayoutState<LayoutState>(state => state);

  const divMessageRef = useRef<HTMLDivElement>(null);
  const [heightDivMessage, setHeightDivMessage] = useState<number | null>(null);

  const {
    control,
    setValue,
    handleSubmit,
    reset,
    getValues,
    watch
  } = useForm({
    resolver: yupResolver(quickRepliesNodeDataFormSchema)
  })

  const {
    fields: messageFields,
    append: messageAppend,
    remove: messageRemove,
    replace: messageReplace,
    update: messageUpdate
  } = useFieldArray({
    control,
    name: 'messages'
  });

  const {
    fields: actionFields,
    append: actionAppend,
    remove: actionRemove,
    replace: actionReplace,
    update: actionUpdate
  } = useFieldArray({
    control,
    name: 'actions'
  });

  const handleAddMessage = () => {
    messageAppend({
      type: 'text',
      value: '',
      language: ''
    })
  }

  const handleDeleteMessage = (index: number, fieldId: string) => {
    messageRemove(index)
    if (inputWithPopoverRef.current) {
      delete inputWithPopoverRef.current[fieldId]
    }
  }

  const handleAddAction = () => {
    actionAppend({
      type: "button",
      label: "",
      bgColor: "#dadada",
      textColor: "#000000",
      value: "",
      link: "",
      text_aliases: [],
    } as IQuickRepliesAction)
  }

  const handleDeleteAction = (index: number, fieldId: string) => {
    actionRemove(index)
    if (inputWithPopoverRef.current) {
      delete inputWithPopoverRef.current[fieldId]
    }
  }

  const resetForm = () => {
    reset()
  }

  const onSubmit = (formValue) => {
    data.messages = _.cloneDeep(formValue?.messages);
    formValue?.actions.forEach((action, index) => {
      action.action_id = `${data.id}_source#${index + 1}`
    })
    data.actions = _.cloneDeep(formValue?.actions);
    data.var_store_id = formValue?.var_store_id;
    setHeightDivMessage(heightDivMessage);
    resetForm();
    setModalOpen(false);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  }

  const onSubmitLanguage = (e) => {
    e.preventDefault()
    data.messages = _.cloneDeep(getValues().messages) as any;
    setModalLanguageOpen(null);
  }

  useEffect(() => {
    resetForm();
    messageReplace(_.cloneDeep(data.messages && data.messages.length ? data.messages : [{
      type: 'text',
      value: '',
      language: ''
    }]));
    actionReplace(_.cloneDeep(data.actions && data.actions.length ? data.actions : [{
      type: "button",
      label: "",
      bgColor: "#dadada",
      textColor: "#000000",
      value: "",
      link: "",
      text_aliases: [],
      action_id: ""
    }]));
    data.var_store_id && setValue('var_store_id', data.var_store_id)
    updateNodeInternal(data.id);
  }, [JSON.stringify(data), modalOpen]);

  useEffect(() => {
    if (heightDivMessage !== null && data.actions) {
      const positions = data.actions.map((_, index) => heightDivMessage + 59 + 37 * index);
      setHandlePositions(positions);
      updateNodeInternal(data.id);
    }
  }, [heightDivMessage, data.actions]);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      for (let entry of entries) {
        setHeightDivMessage(entry.contentRect.height + 18);
      }
    });

    if (divMessageRef.current) {
      observer.observe(divMessageRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [nodeMessages]);

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    if (!data.debug) setModalOpen(true)
  }, [data.debug]);

  const handleClickInput = () => {
    setModalLanguageOpen(null)
  }

  const handleOnFocusInput = () => {
    setModalLanguageOpen(null)
  }

  const handleOnChangeInput = async (sentence, index) => {
    if (sentence && index > -1) {
      setConfirmLoading(true);
      const {language} = await translationApi.detectLanguage({
        sentence,
        api_keys: [localStorage.getItem('current-api-key')]
      });
      if (language) {
        setValue(`messages.${index}.language`, language)
        setConfirmLoading(false);
        data.messages = _.cloneDeep(watch().messages) as any;
      }
    }
  }

  const debounceOnChangeInput = useCallback(debounce(handleOnChangeInput, 1000), []);

  const handleOnOpenModalLanguageChange = () => {
    setModalLanguageOpen(null)
  }

  const handleClickSuffix = async (e, fieldId, index) => {
    e.preventDefault();
    setModalLanguageOpen(fieldId)
    if (!getValues().messages[index].language) {
      const {language} = await translationApi.detectLanguage({
        sentence: watch().messages[index].value,
        api_keys: [localStorage.getItem('current-api-key')]
      });
      if (language) {
        messageUpdate(index, {...messageFields[index], language});
        setConfirmLoading(false);
        data.messages = _.cloneDeep(getValues().messages) as any;
      }
    }
  }

  const handleMouseDownSuffix = (e) => {
    e.preventDefault();
  };

  const handleClosePopover = (e) => {
    e.stopPropagation()
    setModalLanguageOpen(null)
  }

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const reorderedItems = Array.from(getValues().actions);
    const [movedItem] = reorderedItems.splice(result.source.index, 1);
    reorderedItems.splice(result.destination.index, 0, movedItem);
    actionReplace(reorderedItems)
  };

  const renderHandles = () => {
    if (!data.actions || data.actions.length === 0) return null;

    return data.actions.map((_, index) => {
      const key = `${data.id}_source#${index + 1}`;
      const goToBlock = data.goToBlockSource?.find(v => v.sourceHandle === key);

      if (goToBlock) {
        return (
          <GoToBlockHandle
            key={key}
            data={data}
            sourceHandle={key}
            style={{
              height: 14,
              width: 84,
              top: `${handlePositions[index] || 0}px`,
              right: -80,
              backgroundColor: theme === 'dark' ? 'white' : 'black',
              fontSize: 10,
              color: theme === 'dark' ? 'black' : 'white',
              border: 'none',
              borderRadius: 10,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          />
        );
      }

      return (
        <Handle
          key={key}
          type="source"
          position={Position.Right}
          id={key}
          style={{
            height: 8,
            width: 8,
            top: `${handlePositions[index] || 0}px`,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
      );
    });
  };

  useEffect(() => {
    const subscription = watch((value) => {
      if (value.messages && value.messages.length > 0) {
        const validMessages = value.messages.filter(msg => msg.value && msg.value.trim() !== '');
        if (validMessages.length > 0) {
          const newMessages = validMessages.map(msg => ({
            type: msg.type || 'text',
            value: msg.value || '',
            language: msg.language || ''
          }));
          setNodeMessages(newMessages);
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  useEffect(() => {
    if (nodeMessages.length > 0) {
      updateNodeInternal(data.id);
    }
  }, [nodeMessages]);

  const renderMessageDiv = () => {
    if (!nodeMessages?.length || !nodeMessages[0]?.value) return null;

    return (
      <div
        ref={divMessageRef}
        className="line-clamp-3 mt-3 mb-2"
        style={{ fontSize: '10px' }}
      >
        {nodeMessages[0].value}
      </div>
    );
  };

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $debug={data.debug} $theme={theme}>
      {
        debug && (
          <div className="absolute left-0 text-[10px]" style={{top: -20, color: '#D4D9E2'}}>{data.id}</div>
        )
      }
      {
        status && status === STUDIO_STATUS.DEV && (
          <NodeTooltip data={data} selected={data.selected}/>
        )
      }
      <NodeHeader data={data} iconName={data.icon}/>
      {renderMessageDiv()}
      <div>
        <Handle
          type="target"
          position={reversed ? Position.Right : Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
        {data.actions && data.actions.length ? (
          <div>
            {data.actions.map((action, index) => {
              if (index === 0) {
                return (
                  <StyledHandleSourceAnchor
                    key={index}
                    $bgColor={action.bgColor}
                    $color={action.textColor}
                    $data={data.actions}
                    onDoubleClick={handleOpenModal}
                  >
                    {action.label ? action.label : "Configure"}
                  </StyledHandleSourceAnchor>
                );
              }
              if (index > 0) {
                return (
                  <StyledHandleSourceAnchor
                    key={index}
                    $bgColor={action.bgColor}
                    $color={action.textColor}
                    $data={data.actions}
                    onDoubleClick={handleOpenModal}
                  >
                    {action.label ? action.label : "Configure"}
                  </StyledHandleSourceAnchor>
                );
              }
            })}
          </div>
        ) : (
          <StyledHandleSourceAnchor
            $bgColor={data.node_color}
            $color={'white'}
            $data={data.actions}
            onDoubleClick={handleOpenModal}
          >
            Configure
          </StyledHandleSourceAnchor>
        )}
        <div>
          {renderHandles()}
        </div>
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => {
          if (!confirmLoading) setModalOpen(false)
        }}
        onCancel={() => {
          if (!confirmLoading) setModalOpen(false)
        }}
        footer={null}
        closable={false}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {
          setNodeSelected(false);
          resetStyle()
        }}
      >
        <Spin spinning={confirmLoading}>
          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col space-y-4 mt-6">
            <div className="flex flex-col">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Messages <span style={{color: 'red'}}>*</span>
              </Typography.Text>
              {
                messageFields.map((field, index) => (
                  <div key={field.id} className="flex items-center justify-between gap-4 mt-2">
                    <Popover
                      color={theme === 'light' ? '#EBEBEF' : '#1E232E'}
                      content={
                        <div className="flex flex-col space-y-4 mt-6">
                          <SelectField
                            name={`messages[${index}].language`}
                            control={control}
                            options={lang}
                          />

                          <div
                            className="w-full flex justify-end items-center space-x-4"
                            style={{marginTop: 24}}
                          >
                            <StyledButtonModal
                              type="button"
                              key="cancel"
                              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
                              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
                              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
                              onClick={handleClosePopover}
                            >
                              Cancel
                            </StyledButtonModal>
                            <StyledButtonModal
                              $bgColor={"#7F75CF"}
                              $color={"white"}
                              type={undefined}
                              onClick={onSubmitLanguage}
                            >
                              Save
                            </StyledButtonModal>
                          </div>
                        </div>
                      }
                      title="Localization"
                      placement="left"
                      getPopupContainer={() => inputWithPopoverRef.current[field.id]}
                      open={modalLanguageOpen === field.id}
                      onOpenChange={handleOnOpenModalLanguageChange}>
                      <InputFieldSuffix ref={(el) => inputWithPopoverRef.current[field.id] = el} style={{flexGrow: 1}}
                                        type={'textarea'}
                                        name={`messages.${index}.value`}
                                        suffix={<Icon iconName={'RiTranslate2'} size={16} style={{color: '#6F767E'}}/>}
                                        onMouseDownSuffix={(e) => handleMouseDownSuffix(e)}
                                        onClickSuffix={(e) => handleClickSuffix(e, field.id, index)}
                                        onChangeInput={(e, name) => debounceOnChangeInput(e, index)}
                                        onClickInput={(e) => handleClickInput()}
                                        onFocusInput={(e, name) => handleOnFocusInput()}
                                        disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                                        setValue={setValue}/>
                    </Popover>
                    {
                      status && status === STUDIO_STATUS.DEV && (
                        <div className="hover:cursor-pointer" onClick={(e) => handleDeleteMessage(index, field.id)}>
                          <Icon iconName={'RiDeleteBinLine'} size={24} style={{color: '#6F767E'}}/>
                        </div>
                      )
                    }
                  </div>
                ))
              }
            </div>

            {
              status && status === STUDIO_STATUS.DEV && (
                <div className="flex gap-3 items-center hover:cursor-pointer" onClick={handleAddMessage}>
                  <Icon iconName={'RiAddCircleFill'} size={18} style={{color: data.node_color}}/>
                  <div style={{color: data.node_color}}>Add multiple message for randomizing</div>
                </div>
              )
            }

            <div className="flex flex-col">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Actions</Typography.Text>
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="droppable-1" direction="vertical">
                  {
                    (provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className="flex flex-col mt-2 space-y-3"
                      >
                        {
                          actionFields.map((field, index) => (
                            <Draggable key={field.id} draggableId={field.id} index={index}>
                              {
                                (provided) => (
                                  <div ref={provided.innerRef}
                                       {...provided.draggableProps}
                                       {...provided.dragHandleProps}
                                       style={{
                                         userSelect: "none",
                                         ...provided.draggableProps.style
                                       }}
                                       className="p-2 rounded-lg flex items-center justify-center border border-gray-700">
                                    <div className="cursor-move">
                                      <Icon iconName={'RiDraggable'} size={20} style={{color: '#6F767E'}}/>
                                    </div>
                                    <div className="flex-grow grid grid-cols-1 lg:grid-cols-2">
                                      <div className="col-span-1 flex flex-col space-y-2">
                                        <div className="grid grid-cols-4 gap-6 items-center">
                                          <Typography.Text
                                            className="col-span-1 text-white text-right">Label <span style={{color: 'red'}}>*</span></Typography.Text>
                                          <div className="col-span-3 w-full">
                                            <InputField
                                              type={"input"}
                                              name={`actions[${index}].label`}
                                              control={control}
                                              disabled={status && status === STUDIO_STATUS.LIVE}
                                              setValue={setValue}
                                            />
                                          </div>
                                        </div>
                                        <div className="grid grid-cols-4 gap-6 items-center">
                                          <Typography.Text
                                            className="text-white col-span-1 text-right">Text <span style={{color: 'red'}}>*</span></Typography.Text>
                                          <div className="col-span-3 w-full">
                                            <ColorPicker
                                              value={getValues(`actions.${index}.textColor`) as string}
                                              onChange={(color) => setValue(`actions.${index}.textColor`, color.toHexString())}
                                              disabled={status && status === STUDIO_STATUS.LIVE}
                                            />
                                          </div>
                                        </div>
                                        <div className="grid grid-cols-4 gap-6 items-center">
                                          <Typography.Text
                                            className="text-white col-span-1 text-right">Background <span style={{color: 'red'}}>*</span></Typography.Text>
                                          <div className="col-span-3 w-full">
                                            <ColorPicker
                                              value={getValues(`actions.${index}.bgColor`) as string}
                                              onChange={(color) => setValue(`actions.${index}.bgColor`, color.toHexString())}
                                              disabled={status && status === STUDIO_STATUS.LIVE}
                                            />
                                          </div>
                                        </div>
                                      </div>
                                      <div className="col-span-1 flex flex-col space-y-2">
                                        <div className="grid grid-cols-4 gap-6 items-center">
                                          <Typography.Text
                                            className="col-span-1 text-white text-right">Aliases</Typography.Text>
                                          <div className="col-span-3 w-full">
                                            <SelectField name={`actions[${index}].text_aliases`}
                                                         disabled={status && status === STUDIO_STATUS.LIVE}
                                                         control={control}
                                                         mode={'tags'} options={[]} style={{width: '100%'}}/>
                                          </div>
                                        </div>
                                        <div className="grid grid-cols-4 gap-6 items-center">
                                          <Typography.Text
                                            className="col-span-1 text-white text-right">Value</Typography.Text>
                                          <div className="col-span-3 w-full">
                                            <InputField
                                              type={"textarea"}
                                              name={`actions[${index}].value`}
                                              control={control}
                                              disabled={status && status === STUDIO_STATUS.LIVE}
                                              setValue={setValue}
                                            />
                                          </div>
                                        </div>
                                        <div className="grid grid-cols-4 gap-6 items-center">
                                          <Typography.Text
                                            className="col-span-1 text-white text-right">Link</Typography.Text>
                                          <div className="col-span-3 w-full">
                                            <InputField
                                              type={"input"}
                                              name={`actions[${index}].link`}
                                              control={control}
                                              disabled={status && status === STUDIO_STATUS.LIVE}
                                              setValue={setValue}
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    {
                                      status && status === STUDIO_STATUS.DEV && (
                                        <div className="ml-3 hover:cursor-pointer"
                                             onClick={(e) => handleDeleteAction(index, field.id)}>
                                          <Icon iconName={'RiDeleteBinLine'} size={24} style={{color: '#6F767E'}}/>
                                        </div>
                                      )
                                    }
                                  </div>
                                )
                              }
                            </Draggable>
                          ))
                        }
                        {provided.placeholder}
                      </div>
                    )
                  }
                </Droppable>
              </DragDropContext>
            </div>

            {
              status && status === STUDIO_STATUS.DEV && (
                <div className="flex gap-3 items-center hover:cursor-pointer" onClick={handleAddAction}>
                  <Icon iconName={'RiAddCircleFill'} size={18} style={{color: data.node_color}}/>
                  <div style={{color: data.node_color}}>Add action</div>
                </div>
              )
            }

            {/* <Divider style={{borderColor: "white"}}/>

            <div className="w-full flex justify-end">
              <div className="flex justify-end items-center space-x-2">
                <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Store response in:</Typography.Text>
                <SelectField style={{width: '250px'}} name={'var_store_id'} control={control}
                             options={variablesDev.map(v => ({
                               ...v,
                               key: v.id,
                               value: v.id,
                               label: v.var_name
                             }))}/>
              </div>
            </div> */}

            <div className="w-full flex justify-end items-center space-x-4" style={{marginTop: 24}}>
              <StyledButtonModal
                type="button"
                key="cancel"
                $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
                $color={theme === 'light' ? '#282826' : '#F8FAFF'}
                $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
                onClick={() => {if(!confirmLoading) setModalOpen(false)}}
              >
                Cancel
              </StyledButtonModal>
              <StyledButtonModal
                type="submit"
                key="save"
                $bgColor={"#7F75CF"}
                $color={"white"}
                disabled={status && status === STUDIO_STATUS.LIVE}
              >
                Save
              </StyledButtonModal>
            </div>
          </form>
        </Spin>
      </Modal>
    </StyledNodeFlow>
  );
};

export default QuickRepliesNode;
