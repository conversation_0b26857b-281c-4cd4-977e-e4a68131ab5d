<div class="h-full flex flex-col overflow-hidden">
  <!-- Header Section -->
  <div class="flex flex-col items-stretch justify-start">
    <!-- Breadcrumb -->
    <div class="flex flex-wrap space-x-3 hidden lg:flex">
      <div
        class="max-w-[300px] text-[28px] font-bold text-base-content dark:text-dark-base-content truncate flex items-center hover:cursor-pointer hover:!text-gray-500 dark:hover:!text-gray-100 text-light-text dark:text-dark-text"
        (click)="toBreadcrumbItem(undefined)"
      >
        Knowledge Base
      </div>
      @for (breadcrumb of breadcrumbs(); track breadcrumb; let first = $first) {
      <div
        class="max-w-[300px] text-[#94a2b8] text-2xl truncate text-left flex items-center hover:cursor-pointer hover:!text-light-text dark:text-dark-text"
        [ngClass]="
          breadcrumb.id === parentId()
            ? '!text-light-text dark:text-dark-text'
            : ''
        "
        (click)="toBreadcrumbItem(breadcrumb)"
      >
        <ng-icon
          name="heroChevronRight"
          class="!text-xl !text-[#94a2b8]"
        ></ng-icon>
        &ensp;{{ breadcrumb.name }}
      </div>
      }
    </div>

    <!-- Mobile Breadcrumb -->
    <div
      class="w-full flex flex-wrap items-center justify-start pl-4 space-x-3 block lg:hidden"
    >
      <div
        class="max-w-[300px] text-light-text dark:text-dark-text text-2xl truncate flex items-center hover:cursor-pointer hover:!text-light-text dark:text-dark-text"
        [ngClass]="breadcrumbs().length > 0 ? '!text-[#94a2b8]' : ''"
        (click)="toBreadcrumbItem(undefined)"
      >
        Knowledge Base
      </div>
      @if (breadcrumbs().length) {
      <div
        class="max-w-[300px] text-[#94a2b8] text-2xl truncate text-left flex items-center hover:cursor-pointer hover:!text-light-text dark:text-dark-text"
        (click)="toBreadcrumbItem(breadcrumbs()[breadcrumbs().length - 1])"
      >
        <ng-icon
          name="heroChevronRight"
          class="!text-xl !text-[#94a2b8]"
        ></ng-icon
        >&ensp;{{ breadcrumbs()[breadcrumbs().length - 1].name }}
      </div>
      }
    </div>
  </div>

  <div
    class="mt-6 space-y-6 p-6 h-full rounded-2xl flex flex-col overflow-hidden responsive-height bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
  >
  @if (showSelectionInfo()) {
    <div class="flex items-center justify-between w-full bg-base-100 dark:bg-dark-base-100 text-base-content dark:text-dark-base-content px-4 py-[3px] rounded-full border border-primary-border dark:border-dark-primary-border">
      <div class="flex items-center" >
        <ng-icon name="heroFolder" class="text-xl mr-2"></ng-icon>
        <span class="font-medium">{{ selectionText() }}</span>
      </div>
      <div class="flex items-center">
        @if (folderSelection.selected && folderSelection.selected.length &&
        fileSelection.selected.length === 0 && isAllowAssign) {
        <div
          (click)="openAssignPermissionDialog()"
          (mouseup)="$event.stopPropagation()"
          class="ml-6 flex items-center justify-center w-10 aspect-square hover:cursor-pointer hover:bg-[#6F767E] hover:rounded-xl"
        >
          <ng-icon
            name="heroUserPlus"
            class="text-light-text dark:text-dark-text text-2xl"
          ></ng-icon>
        </div>
        } @if (parentId()) {
        <div
          (click)="deleteAll()"
          (mouseup)="$event.stopPropagation()"
          class="ml-2 flex items-center justify-center w-10 aspect-square hover:cursor-pointer hover:bg-[#6F767E] hover:rounded-xl"
        >
          <ng-icon
            name="heroTrash"
            class="text-red-500 text-2xl"
          ></ng-icon>
        </div>
        } @if (!parentId()) { @if (isAllowAssign) {
        <div
          (click)="deleteAll()"
          (mouseup)="$event.stopPropagation()"
          class="ml-2 flex items-center justify-center w-10 aspect-square hover:cursor-pointer hover:bg-[#6F767E] hover:rounded-xl"
        >
          <ng-icon
            name="heroTrash"
            class="text-light-text dark:text-dark-text text-2xl"
          ></ng-icon>
        </div>
        } }
        <button
          (click)="clearAllSelectedFolderAndFile()"
          class="flex items-center justify-center ml-2">
          <ng-icon name="heroXMark" class="text-lg"></ng-icon>
        </button>
      </div>
    </div>
    } @else {
    <!-- Search and Filters -->
    <div class="flex flex-wrap items-center justify-between gap-y-3">
      <div class="flex flex-wrap items-center gap-x-3 gap-y-2">
        <!-- Search Bar -->
        <dx-form-field
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
          class="w-full md:w-64"
        >
          <app-svg-icon
            dxPrefix
            type="icSearch"
            class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
          ></app-svg-icon>
          <input
            dxInput
            type="text"
            [(ngModel)]="searchModel.name"
            (ngModelChange)="onSearchChange($event)"
            placeholder="Search by name"
          />
        </dx-form-field>

        <!-- File Type Filter -->
        <dx-form-field
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
          class="w-full md:w-48"
        >
          <dx-select
            [(ngModel)]="searchModel.file_type"
            (selectionChange)="applyFilter()"
            placeholder="All file type"
          >
            <dx-option *ngFor="let type of listFileType" [value]="type.value">
              {{ type.label }}
            </dx-option>
          </dx-select>
        </dx-form-field>

        <!-- Status Filter -->
        <dx-form-field
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
          class="w-full md:w-48"
        >
          <dx-select
            [(ngModel)]="searchModel.file_status"
            (selectionChange)="applyFilter()"
            placeholder="All status"
          >
            <dx-option *ngFor="let status of listStatus" [value]="status.value">
              {{ status.label }}
            </dx-option>
          </dx-select>
        </dx-form-field>
      </div>

      <div class="flex flex-wrap items-center gap-x-3 gap-y-2">
        <!-- View Toggle -->
        <div
          class="p-1 rounded-xl cursor-pointer bg-base-300 dark:bg-dark-base-300 border border-primary-border dark:border-dark-primary-border"
        >
          <div
            class="flex flex-row overflow-hidden text-neutral-content dark:text-dark-neutral-content bg-transparent"
          >
            <div
              class="flex items-center justify-center rounded-lg p-1"
              [ngClass]="{
                'bg-base-400 dark:bg-dark-base-400 inset-shadow-xs inset-shadow-decoration-100 shadow-md':
                  showStyle() === 'list'
              }"
              (click)="toggleView('list')"
            >
              <app-svg-icon
                type="icList"
                class="w-5.5 h-5.5"
                [ngClass]="[
                  showStyle() === 'list'
                    ? '!text-base-content dark:!text-dark-base-content'
                    : '!text-neutral-content dark:!text-dark-neutral-content'
                ]"
              ></app-svg-icon>
            </div>
            <div
              class="flex items-center justify-center rounded-lg p-1"
              [ngClass]="{
                'bg-base-400 dark:bg-dark-base-400 inset-shadow-xs dark:inset-shadow-dark-decoration-100 shadow-md':
                  showStyle() === 'grid'
              }"
              (click)="toggleView('grid')"
            >
              <app-svg-icon
                type="icCategory"
                class="w-5.5 h-5.5"
                [ngClass]="[
                  showStyle() === 'grid'
                    ? '!text-base-content dark:!text-dark-base-content '
                    : '!text-neutral-content dark:!text-dark-neutral-content'
                ]"
              ></app-svg-icon>
            </div>
          </div>
        </div>

        <div
          class="text-xs font-bold text-primary-border dark:text-dark-primary-border"
        >
          |
        </div>

        <!-- Import Button -->
        <div cdkOverlayOrigin #importMenu="cdkOverlayOrigin">
          <button
            dxButton="elevated"
            (click)="openCreateMenu.set(!openCreateMenu())"
          >
            <div class="flex items-center">
              <app-svg-icon
                type="icUpload"
                class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"
              ></app-svg-icon>
              Import
            </div>
          </button>
          <ng-template
            cdkConnectedOverlay
            [cdkConnectedOverlayOrigin]="importMenu"
            [cdkConnectedOverlayOpen]="openCreateMenu()"
            [cdkConnectedOverlayPositions]="[
              {
                originX: 'start',
                originY: 'bottom',
                overlayX: 'center',
                overlayY: 'top',
                offsetY: 5
              }
            ]"
          >
            <ul
              (clickOutside)="openCreateMenu.set(false)"
              class="p-2 w-[245px] flex flex-col justify-between shadow-lg rounded-xl border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
            >
              <li
                (click)="showImportUrlDialog()"
                class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-100 dark:hover:bg-dark-base-100"
              >
                <div
                  class="flex items-center justify-between text-[16px] font-medium !text-base-content dark:!text-dark-base-content"
                >
                  <app-svg-icon
                    type="icLinkPrefix"
                    class="w-6 h-6 text-xl mr-3"
                  ></app-svg-icon>
                  Import URL
                </div>
              </li>
              <li
                (click)="showUploadDialog()"
                class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-100 dark:hover:bg-dark-base-100"
              >
                <div
                  class="flex items-center justify-between text-[16px] font-medium !text-base-content dark:!text-dark-base-content"
                >
                  <app-svg-icon
                    type="icUpload"
                    class="w-6 h-6 text-xl mr-3"
                  ></app-svg-icon>
                  Import File
                </div>
              </li>
            </ul>
          </ng-template>
        </div>
        <!-- Create Folder Button -->
        <button dx-button="filled" (click)="showCreateFolderDialog()">
          <!--              <app-svg-icon type="icPlus" class="w-6 h-6"></app-svg-icon>-->
          Create folder
        </button>
      </div>
    </div>
  }
    <!-- Content Section -->
    <div
      class="flex-grow overflow-hidden"
      [ngStyle]="{
        height:
          showStyle() === 'grid' ? 'calc(100% - 50px)' : 'calc(100% - 0px)'
      }"
    >
      @if (searchingMode()) {
      <div class="p-4">
        <h1 class="text-light-text dark:text-dark-text text-xl">
          Search result
        </h1>
      </div>
      } @if (isLoadingMore() && folderList().length === 0 && fileList().length
      === 0) {
      <div
        class="flex flex-col items-center justify-center h-full text-center p-8"
      >
        <div
          class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"
        ></div>
        <p class="text-light-text dark:text-dark-text">Loading...</p>
      </div>
      } @if (folderList().length > 0 || fileList().length > 0) { @switch
      (showStyle()) { @case ('grid') {
      <app-grid-view
        [folderList]="folderList()"
        [fileList]="fileList()"
        [searchModel]="searchModel"
        [folderSelection]="folderSelection"
        [fileSelection]="fileSelection"
        (folderSelected)="onFolderSelected($event)"
        (fileSelected)="onFileSelected($event)"
        (folderDoubleClick)="onFolderDoubleClick($event)"
        (fileDoubleClick)="onFileDoubleClick($event)"
        (folderContextMenu)="onFolderContextMenu($event)"
        (fileContextMenu)="onFileContextMenu($event)"
        (folderRename)="onFolderRename($event)"
        (folderDelete)="onFolderDelete($event)"
        (fileInfo)="onFileInfo($event)"
        (fileRename)="onFileRename($event)"
        (fileMove)="onFileMove($event)"
        (fileDelete)="onFileDelete($event)"
        (refreshRequested)="onRefreshRequested()"
      >
      </app-grid-view>
      } @case ('list') {
      <app-list-view
        [combinedList]="combinedList()"
        [searchModel]="searchModel"
        [folderSelection]="folderSelection"
        [fileSelection]="fileSelection"
        (folderSelected)="onFolderSelected($event)"
        (fileSelected)="onFileSelected($event)"
        (folderDoubleClick)="onFolderDoubleClick($event)"
        (fileDoubleClick)="onFileDoubleClick($event)"
        (folderContextMenu)="onFolderContextMenu($event)"
        (fileContextMenu)="onFileContextMenu($event)"
        (sortChanged)="onSortChanged($event)"
        (folderRename)="onFolderRename($event)"
        (folderDelete)="onFolderDelete($event)"
        (fileInfo)="onFileInfo($event)"
        (fileRename)="onFileRename($event)"
        (fileMove)="onFileMove($event)"
        (fileDelete)="onFileDelete($event)"
      >
      </app-list-view>
      } } } @else {
      <div
        class="flex flex-col items-center justify-center h-full text-center p-8"
      >
        <ng-icon
          name="heroFolder"
          class="text-6xl text-light-text dark:text-dark-text opacity-50 mb-4"
        ></ng-icon>
        <h3 class="text-xl text-light-text dark:text-dark-text mb-2">
          No files or folders found
        </h3>
        <p class="text-light-text dark:text-dark-text opacity-70">
          Upload some files or create folders to get started
        </p>
      </div>
      }

      <!-- Load More Button -->
      @if (hasMoreData() && !isLoadingMore() && (folderList().length > 0 ||
      fileList().length > 0)) {
      <div class="flex justify-center p-4">
        <button
          class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center space-x-2"
          (click)="loadMore()"
        >
          <span>Load More</span>
        </button>
      </div>
      }

      <!-- Loading More Indicator -->
      @if (isLoadingMore() && (folderList().length > 0 || fileList().length >
      0)) {
      <div class="flex justify-center p-4">
        <div
          class="flex items-center space-x-2 text-light-text dark:text-dark-text"
        >
          <div
            class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"
          ></div>
          <span>Loading more...</span>
        </div>
      </div>
      }
    </div>
  </div>

  <!-- Context Menus -->
  <mat-menu #folderMenu="matMenu" xPosition="after" class="my-menu">
    <button
      class="!h-10 text-light-text dark:text-dark-text"
      mat-menu-item
      (click)="renameFolderAction()"
    >
      <div class="flex items-center">
        <ng-icon
          name="heroPencilSquare"
          class="text-yellow-500 text-2xl mr-3"
        ></ng-icon>
        <span class="text-light-text dark:text-dark-text">Rename</span>
      </div>
    </button>
    <button
      class="!h-10 text-light-text dark:text-dark-text"
      mat-menu-item
      (click)="deleteFolderAction()"
    >
      <div class="flex items-center">
        <ng-icon name="heroTrash" class="text-red-500 text-2xl mr-3"></ng-icon>
        <span class="text-light-text dark:text-dark-text">Delete</span>
      </div>
    </button>
  </mat-menu>

  <mat-menu #fileMenu="matMenu" xPosition="after" class="my-menu">
    <button
      class="!h-10 text-light-text dark:text-dark-text"
      mat-menu-item
      (click)="viewFileInfoAction()"
    >
      <div class="flex items-center">
        <ng-icon
          name="heroInformationCircle"
          class="text-blue-500 text-2xl mr-3"
        ></ng-icon>
        <span class="text-light-text dark:text-dark-text">Info</span>
      </div>
    </button>
    <button
      class="!h-10 text-light-text dark:text-dark-text"
      mat-menu-item
      (click)="renameFileAction()"
    >
      <div class="flex items-center">
        <ng-icon
          name="heroPencilSquare"
          class="text-yellow-500 text-2xl mr-3"
        ></ng-icon>
        <span class="text-light-text dark:text-dark-text">Rename</span>
      </div>
    </button>
    <button
      class="!h-10 text-light-text dark:text-dark-text"
      mat-menu-item
      (click)="moveFileAction()"
    >
      <div class="flex items-center">
        <ng-icon
          name="heroArrowRight"
          class="text-purple-500 text-2xl mr-3"
        ></ng-icon>
        <span class="text-light-text dark:text-dark-text">Move</span>
      </div>
    </button>
    <button
      class="!h-10 text-light-text dark:text-dark-text"
      mat-menu-item
      (click)="deleteFileAction()"
    >
      <div class="flex items-center">
        <ng-icon name="heroTrash" class="text-red-500 text-2xl mr-3"></ng-icon>
        <span class="text-light-text dark:text-dark-text">Delete</span>
      </div>
    </button>
  </mat-menu>
</div>

<!-- Dialog Templates -->

<!-- Create Folder Dialog -->
<!--
<ng-template #createFolderDialog let-data>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text">
    <div class="header border-bottom-gray pt-6 px-6 flex justify-between items-center pb-3">
      <div class="text-2xl font-bold card-title capitalize">
        {{ data.isCreate ? "Add folder" : "Edit folder" }}
      </div>
      <div class="hover:cursor-pointer" (click)="closeDialog()">
        <ng-icon name="heroXMark" class="text-2xl text-light-text dark:text-dark-text"></ng-icon>
      </div>
    </div>

    <div class="w-full px-6 pt-6 content overflow-auto max-h-[75vh]">
      <form [formGroup]="createFolderForm">
        <div class="border-light-gray rounded-2xl bg-light-background dark:bg-dark-background p-4">
          <div class="mb-6">
            <label class="block text-sm font-medium mb-2">Name <span class="text-red-500">*</span></label>
            <input
              class="w-full mt-2 bg-transparent border border-light-border dark:border-dark-border px-4 h-[40px] rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              type="text" placeholder="Folder name" formControlName="name" (keydown)="onKeyDownFolderDialog($event)"/>
            @if (createFolderForm.get('name')?.invalid && createFolderForm.get('name')?.touched) {
              <div class="text-red-500 text-sm mt-1">Folder name is required</div>
            }
          </div>
        </div>
      </form>
    </div>

    <div class="footer px-6 py-4 border-t border-light-border dark:border-dark-border flex justify-end space-x-3">
      <button
        class="px-4 py-2 border border-light-border dark:border-dark-border rounded-lg hover:bg-light-hover dark:hover:bg-dark-hover"
        (click)="closeDialog()">
        Cancel
      </button>
      <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
              [disabled]="createFolderForm.invalid || isCreatingFolder()" (click)="saveCreatingFolder()">
        @if (isCreatingFolder()) {
          <div class="flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Saving...
          </div>
        } @else {
          {{ data.isCreate ? "Create" : "Update" }}
        }
      </button>
    </div>
  </div>
</ng-template>
-->

<!-- Upload File Dialog -->
<!--<ng-template #upFileDialog>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text p-6 space-y-4">
    <div
      class="header border-b border-b-light-border-line dark:border-b-dark-border-line flex justify-between items-center pb-3">
      <div class="flex items-center space-x-2">
        <p class="text-2xl font-bold card-title capitalize">Upload file</p>
        <p class="text-sm opacity-70">(.pdf, .csv, .txt, .md, .docx)</p>
      </div>
      <div class="hover:cursor-pointer" (click)="closeDialog()">
        <ng-icon name="heroXMark" class="text-2xl text-light-text dark:text-dark-text"></ng-icon>
      </div>
    </div>

    <div class="content w-full overflow-auto space-y-4">
      <form [formGroup]="uploadFileForm" enctype="multipart/form-data">
        <div class="flex items-center">
          <button type="button"
                  class="h-[40px] min-w-[120px] mr-3 bg-blue-500 hover:bg-blue-600 px-3 rounded-lg cursor-pointer border-0 text-white"
                  (click)="fileUploadRef.click()">
            Select file
          </button>
          <input #fileUploadRef type="file" class="hidden" accept=".csv,.pdf,.txt,.md,.docx"
                 (change)="uploadFile($event)"/>
          <label class="ml-2">
            {{ documentName() ? documentName() : "No file selected" }}
          </label>
        </div>

        @if (checkFileCsv(documentName()) && fileUpload()) {
          <div class="mt-4">
            <label class="block text-sm font-medium mb-2">Metadata Columns (CSV only)</label>
            <textarea [(ngModel)]="metadataValue"
                      class="w-full p-3 border border-light-border dark:border-dark-border rounded-lg bg-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows="3" placeholder="Enter column names separated by commas (optional)"></textarea>
            <p class="text-sm opacity-70 mt-1">
              Specify which columns contain metadata for better processing
            </p>
          </div>
        }
      </form>
    </div>

    <div class="footer flex justify-end space-x-3 pt-4 border-t border-light-border dark:border-dark-border">
      <button
        class="px-4 py-2 border border-light-border dark:border-dark-border rounded-lg hover:bg-light-hover dark:hover:bg-dark-hover"
        (click)="closeDialog()">
        Cancel
      </button>
      <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
              [disabled]="!fileUpload() || isImportingFile()" (click)="saveFileUpload(upFileDialog)">
        @if (isImportingFile()) {
          <div class="flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Uploading...
          </div>
        } @else {
          Upload
        }
      </button>
    </div>
  </div>
</ng-template>-->

<!-- Import URL Dialog -->

<!-- Rename File Dialog -->
<!--
<ng-template #renameFileDialog>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text">
    <div
      class="header border-b border-light-border dark:border-dark-border pt-6 px-6 flex justify-between items-center pb-3">
      <div class="text-2xl font-bold card-title capitalize">
        Rename File
      </div>
      <div class="hover:cursor-pointer" (click)="closeDialog()">
        <ng-icon name="heroXMark" class="text-2xl text-light-text dark:text-dark-text"></ng-icon>
      </div>
    </div>

    <div class="w-full px-6 pt-6 content overflow-auto">
      <div class="mb-6">
        <label class="block text-sm font-medium mb-2">New Name <span class="text-red-500">*</span></label>
        <input
          class="w-full mt-2 bg-transparent border border-light-border dark:border-dark-border px-4 h-[40px] rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          type="text" placeholder="Enter new file name" [(ngModel)]="fileRename.new_name"
          (keydown.enter)="saveRenameFile()"/>
      </div>
    </div>

    <div class="footer px-6 py-4 border-t border-light-border dark:border-dark-border flex justify-end space-x-3">
      <button
        class="px-4 py-2 border border-light-border dark:border-dark-border rounded-lg hover:bg-light-hover dark:hover:bg-dark-hover"
        (click)="closeDialog()">
        Cancel
      </button>
      <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
              [disabled]="!fileRename.new_name?.trim()" (click)="saveRenameFile()">
        Rename
      </button>
    </div>
  </div>
</ng-template>
-->

<!-- File Info Dialog -->
<!--
<ng-template #viewFileInfoDialog let-data>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text">
    <div
      class="header border-b border-light-border dark:border-dark-border pt-6 px-6 flex justify-between items-center pb-3">
      <div class="text-2xl font-bold card-title capitalize">
        File Information
      </div>
      <div class="hover:cursor-pointer" (click)="closeDialog()">
        <ng-icon name="heroXMark" class="text-2xl text-light-text dark:text-dark-text"></ng-icon>
      </div>
    </div>

    <div class="w-full px-6 pt-6 content overflow-auto max-h-[75vh]">
      @if (data.file) {
        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">File Name</label>
              <p class="text-light-text dark:text-dark-text">{{ data.file.name }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">File Type</label>
              <p class="text-light-text dark:text-dark-text">{{ data.file.ext || 'Unknown' }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Status</label>
              <span class="px-2 py-1 rounded-full text-xs" [class]="data.file.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                          data.file.status === 'IN_PROGRESS' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'">
              {{ data.file.status || 'Unknown' }}
            </span>
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Created</label>
              <p class="text-light-text dark:text-dark-text">{{ data.file.created_at | date:'medium' }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Updated</label>
              <p class="text-light-text dark:text-dark-text">{{ data.file.updated_at | date:'medium' }}</p>
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Size</label>
              <p class="text-light-text dark:text-dark-text">{{ data.file.size || 'Unknown' }}</p>
            </div>
          </div>

          @if (data.file.description) {
            <div>
              <label class="block text-sm font-medium mb-1">Description</label>
              <p class="text-light-text dark:text-dark-text">{{ data.file.description }}</p>
            </div>
          }
        </div>
      }
    </div>

    <div class="footer px-6 py-4 border-t border-light-border dark:border-dark-border flex justify-end space-x-3">
      <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              (click)="viewContentFile(data.file)">
        View Content
      </button>
      <button
        class="px-4 py-2 border border-light-border dark:border-dark-border rounded-lg hover:bg-light-hover dark:hover:bg-dark-hover"
        (click)="closeDialog()">
        Close
      </button>
    </div>
  </div>
</ng-template>
-->

<!-- File Content Dialog -->
<!--
<ng-template #viewContentDialog let-data>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text">
    <div
      class="header border-b border-light-border dark:border-dark-border pt-6 px-6 flex justify-between items-center pb-3">
      <div class="text-2xl font-bold card-title capitalize">
        {{ data.file?.name || 'File Content' }}
      </div>
      <div class="flex items-center space-x-2">
        <button
          class="px-3 py-1 text-sm border border-light-border dark:border-dark-border rounded hover:bg-light-hover dark:hover:bg-dark-hover"
          (click)="toggleEditContent()">
          {{ isEditingContent() ? 'Cancel' : 'Edit' }}
        </button>
        <div class="hover:cursor-pointer" (click)="closeDialog()">
          <ng-icon name="heroXMark" class="text-2xl text-light-text dark:text-dark-text"></ng-icon>
        </div>
      </div>
    </div>

    <div class="w-full px-6 pt-6 content overflow-auto flex-grow">
      @if (isEditingContent()) {
        <textarea #editTextarea [(ngModel)]="fileContentValue"
                  class="w-full h-full min-h-[400px] p-4 border border-light-border dark:border-dark-border rounded-lg bg-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                  placeholder="File content..."></textarea>
      } @else {
        <div #contentDiv
             class="w-full h-full min-h-[400px] p-4 border border-light-border dark:border-dark-border rounded-lg bg-light-background dark:bg-dark-background overflow-auto whitespace-pre-wrap">
          {{ fileContent() || 'No content available' }}
        </div>
      }
    </div>

    <div class="footer px-6 py-4 border-t border-light-border dark:border-dark-border flex justify-end space-x-3">
      @if (isEditingContent()) {
        <button class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600" (click)="saveFileContent()">
          Save Changes
        </button>
      }
      <button
        class="px-4 py-2 border border-light-border dark:border-dark-border rounded-lg hover:bg-light-hover dark:hover:bg-dark-hover"
        (click)="closeDialog()">
        Close
      </button>
    </div>
  </div>
</ng-template>
-->

<!-- Move File Dialog -->
<ng-template #moveFileDialog let-data>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text"
  >
    <div
      class="header border-b border-light-border dark:border-dark-border pt-6 px-6 flex justify-between items-center pb-3"
    >
      <div class="text-2xl font-bold card-title capitalize">
        Move File: {{ data.name }}
      </div>
      <div class="hover:cursor-pointer" (click)="closeDialog()">
        <ng-icon
          name="heroXMark"
          class="text-2xl text-light-text dark:text-dark-text"
        ></ng-icon>
      </div>
    </div>

    <div class="w-full px-6 pt-6 content overflow-auto max-h-[60vh]">
      <!-- Breadcrumb -->
      <div
        class="flex items-center space-x-2 mb-4 p-2 bg-light-background dark:bg-dark-background rounded-lg"
      >
        <button
          class="text-blue-500 hover:text-blue-600 flex items-center space-x-1"
          (click)="goToNextFolder(null)"
        >
          <ng-icon name="heroHome" class="text-lg"></ng-icon>
          <span>Home</span>
        </button>
        @for (breadcrumb of breadcrumbsMoveDialog(); track breadcrumb.id; let i
        = $index) {
        <ng-icon name="heroChevronRight" class="text-sm opacity-50"></ng-icon>
        <button
          class="text-blue-500 hover:text-blue-600"
          (click)="goToBreadcrumbMove(i)"
        >
          {{ breadcrumb.name }}
        </button>
        }
      </div>

      <!-- Loading State -->
      @if (loadingMoveDialog()) {
      <div class="flex justify-center items-center py-8">
        <div
          class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"
        ></div>
      </div>
      }

      <!-- Folder List -->
      @if (!loadingMoveDialog()) {
      <div class="space-y-2">
        @if (foldersCanMoveTo().length === 0) {
        <div
          class="text-center py-8 text-light-text dark:text-dark-text opacity-70"
        >
          No folders available in this location
        </div>
        } @else { @for (folder of foldersCanMoveTo(); track folder.id) {
        <div
          class="flex items-center p-3 border border-light-border dark:border-dark-border rounded-lg cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover"
          [class.bg-blue-100]="selectedFolderMoveId() === folder.id"
          (click)="onFolderMoveClick(folder.id!)"
          (dblclick)="goToNextFolder(folder)"
        >
          <ng-icon
            name="heroFolder"
            class="text-blue-500 text-xl mr-3"
          ></ng-icon>
          <span class="flex-1">{{ folder.name }}</span>
          <button
            class="text-blue-500 hover:text-blue-600 p-1"
            (click)="goToNextFolder(folder); $event.stopPropagation()"
          >
            <ng-icon name="heroChevronRight" class="text-lg"></ng-icon>
          </button>
        </div>
        } }
      </div>
      }
    </div>

    <div
      class="footer px-6 py-4 border-t border-light-border dark:border-dark-border flex justify-end space-x-3"
    >
      <button
        class="px-4 py-2 border border-light-border dark:border-dark-border rounded-lg hover:bg-light-hover dark:hover:bg-dark-hover"
        (click)="closeDialog()"
      >
        Cancel
      </button>
      <button
        class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
        (click)="moveFileToFolder()"
      >
        Move Here
      </button>
    </div>
  </div>
</ng-template>

<ng-template #assignPermissionFolderDialog let-data>
  <div class="text-light-text dark:text-dark-text h-full">
    <div
      class="header border-bottom-gray pt-6 px-6 flex justify-between items-center pb-3"
    >
      <div class="text-2xl font-bold card-title capitalize">
        Share {{ data.countFolder }}
        {{ data.countFolder > 1 ? "folders" : "folder" }}
      </div>
      <div
        id="btn-close-dialog-assign-permissions-1"
        class="hover:cursor-pointer"
      >
        <svg
          (click)="closeDxDialog()"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
            fill="#6F767E"
          />
        </svg>
      </div>
    </div>

    <div
      class="w-full px-6 content overflow-auto max-h-[75vh]"
      [ngClass]="folderSelection.selected.length == 1 ? 'pt-6' : ''"
    >
      @if (!(folderSelection.selected.length > 1)) {
      <dx-select
        class="block my-2"
        [value]="getAssignPermissionsFormArrayValue('user_id')"
        (valueChange)="addAssignPermissions($event)"
        placeholder="Select user"
      >
        <dx-option *ngFor="let user of listUserInAIAllowed()" [value]="user.id">{{user.email}}</dx-option>
      </dx-select>
      <!--<app-search-input
          class="app-search-custom block my-2 create-tool-select background-bold"
          [placeholder]="'Select user'"
          [listOptions]="listUserInAIAllowed" [setOptionValue]="'id'" [setOptionLabel]="'email'"
          [value]="getAssignPermissionsFormArrayValue('user_id')" (valueChange)="addAssignPermissions($event)"
        [setCanBeSearch]="false" [haveTooltip]="false">

        </app-search-input>-->
      }
      <form [formGroup]="assignFolderForm" class="mt-6">
        <h1 class="mb-3">Users with access</h1>
        @for (assignPermission of assignPermissionsFormArray.controls; track
        assignPermission; let i = $index) {
        <div class="w-full mb-4 grid grid-cols-8 gap-5 items-center h-12">
          <div class="col-span-4 h-full flex items-center space-x-3">
            <div
              class="w-10 aspect-square rounded-full bg-[#00BFFF] flex items-center justify-center"
            >
              {{ assignPermission.get("username")?.value | charFirst }}
            </div>
            <div class="flex flex-col space-y-1">
              <div class="text-light-text dark:text-dark-text truncate">
                {{ assignPermission.get("username")?.value || "" }}
              </div>
              @if (assignPermission.get('folder_id')?.value) {
              <div class="flex flex-col items-stretch">
                <div class="flex items-center space-x-2">
                  <div class="flex items-center justify-center">
                    <ng-icon
                      name="faSolidFolderClosed"
                      class="text-gray-500 text-md"
                    ></ng-icon>
                  </div>
                  <div
                    class="w-[calc(100%-26px)] overflow-hidden text-left flex flex-col"
                  >
                    <div class="truncate text-gray-500 text-sm">
                      {{
                        getFolderById(
                          assignPermission.get("folder_id")?.value || 0
                        )?.name || ""
                      }}
                    </div>
                  </div>
                </div>
              </div>
              }
            </div>
          </div>
          <div class="col-span-4 h-full w-full flex items-center">
            <dx-select
              class="w-full block !text-[12px]"
              [value]="getAssignPermissionsFormArrayValueIndex('permissions', i)"
              (valueChange)="selectPermissions($event, i)"
              placeholder="Select permissions"
              [multiple]="true"
            >
              <dx-option *ngFor="let permission of listPermissions" [value]="permission.value">{{permission.label}}</dx-option>
            </dx-select>
            <!--              <app-search-input class="app-search-custom w-full block create-tool-select background-bold !text-[12px]"-->
            <!--                [placeholder]="'Select permissions'" [listOptions]="listPermissions" [setOptionValue]="'value'"-->
            <!--                [setOptionLabel]="'label'" [value]="getAssignPermissionsFormArrayValueIndex('permissions', i)"-->
            <!--                (valueChange)="selectPermissions($event, i)" [setCanBeSearch]="false" [haveTooltip]="false"-->
            <!--                [multiple]="true" [showClear]="true"-->
            <!--              [isDisable]="(folderSelection.selected.length > 1)"></app-search-input>-->
          </div>
        </div>
        }
      </form>
    </div>

    @if (!(folderSelection.selected.length > 1)) {
    <div class="footer p-6 flex justify-end">
      <div class="flex">
        <button
          id="btn-close-dialog-assign-permissions-2"
          class="cursor-pointer h-[40px] min-w-[120px] px-3 rounded-2xl bg-light-secondary-background dark:bg-dark-secondary-background border-light-gray"
          (click)="closeDxDialog()"
        >
          <span>Cancel</span>
        </button>
        <button
          dx-button="filled"
          id="btn-submit-intent-form"
          class="h-[40px] min-w-[120px] bg-light-user-chat-background dark:bg-dark-user-chat-background text-white px-3 rounded-2xl border-light-gray button-disabled hover:cursor-pointer ml-3"
          [disabled]="assignFolderForm.invalid"
          (click)="savePermissionAssign()"
        >
          Save
        </button>
      </div>
    </div>
    }
  </div>
</ng-template>
