// @ts-nocheck
import { BuildFlowState, LayoutState, NodeFlowData, NodeHeaderState } from "@flow-editor/model";
import { useBuildFlowState, useLayoutState, useNodeHeaderState } from "@flow-editor/store";
import * as icons from "@remixicon/react";
import React, { useEffect, useState } from "react";
import { useReactFlow } from "reactflow";
import styled from "styled-components";

const StyledCsNodeHeader = styled.div<{ $theme: string, $isEditing: boolean }>`
  display: flex;
  flex-direction: row;
  align-items: ${props => props.$isEditing ? 'start' : 'center'};
  justify-content: flex-start;
  gap: 8px;
  color: ${(props) => (props.$theme === 'dark' ? '#fff' : '#000')};
  font-weight: 500;
  font-size: 12px;
`;

const StyledNodeMenuIcon = styled.div<{ $backgroundColor: string }>`
  background: ${(props) => props.$backgroundColor || "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 8px;
`;

const NodeMenuIcon = ({ iconName }) => {
  const iconComp = iconName ? icons[iconName] : icons.RiProhibited2Fill;
  return React.createElement(iconComp, {
    size: 12,
    style: {
      color: "white",
    },
  });
};

const StyledLabelDiv = styled.div<{ $theme: string }>`
  width: 100%;
  color: ${(props) => (props.$theme === 'dark' ? '#fff' : '#000')};
  cursor: pointer;
  padding: 4px;
  transition: background-color 0.3s ease;
  text-align: left;
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const StyledInputWrapper = styled.div`
  position: relative;
  display: inline-block;
  width: 150px;
`;

const StyledInput = styled.input`
  font-size: 12px;
  color: ${(props) => (props.$theme === 'dark' ? 'white' : '#000')};
  background-color: ${(props) => (props.$theme === 'dark' ? '#010314' : 'white')};
  border-radius: 8px;
  width: 100%;
  margin-bottom: 12px;
  padding: 2px 4px;
`;

const CharCount = styled.span`
  font-size: 10px;
  color: lightgray;
  position: absolute;
  bottom: -8px;
  right: 0;
  padding: 2px;
`;

const NodeHeader = ({ data, iconName }: { data: NodeFlowData; iconName: string }) => {
  const [value, setLabel] = useState(data.label);
  const { isEditing, setIsEditing } = useNodeHeaderState<NodeHeaderState>((state) => state);
  const {setDirty, setTriggerSave} = useBuildFlowState<BuildFlowState>((state) => state);
  const {theme} = useLayoutState<LayoutState>(state => state);

  const maxChars = 50;
  const { getNodes, setNodes } = useReactFlow();

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = event.target.value.slice(0, 100);
    const nodeSameLabel = getNodes().find(node => node.data.label === newValue)
    if (nodeSameLabel) {
      newValue += ` ${data.id}`
    }
    setLabel(newValue);
    data.label = newValue;

    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.data.goToBlockSource) {
          const goToBlockSourceChangeIndex = node.data.goToBlockSource.findIndex(v => v.blockId === data.id)
          if (goToBlockSourceChangeIndex > -1) {
            node.data.goToBlockSource[goToBlockSourceChangeIndex] = {
              ...node.data.goToBlockSource[goToBlockSourceChangeIndex],
              blockName: newValue
            }
          }
        }
        return node
      })
    )
  };

  useEffect(() => {
    if (data && data.label) {
      setLabel(data.label)
    }
  }, [data.label])

  const openEdit = async () => {
    setTriggerSave();
    setDirty();
    setIsEditing(data.id);
  };

  const closeEdit = () => {
    setIsEditing(null);
  };

  return (
    <StyledCsNodeHeader $theme={theme} $isEditing={isEditing === data.id}>
      <StyledNodeMenuIcon $backgroundColor={data.node_color}>
        <NodeMenuIcon iconName={iconName} />
      </StyledNodeMenuIcon>

      {isEditing === data.id ? (
        // Show input when editing
        <StyledInputWrapper $theme={theme}>
          <StyledInput
            $theme={theme}
            value={value}
            onChange={handleChange}
            onBlur={closeEdit}
            onKeyPress={(event) => {
              if (event.key === "Enter") {
                event.preventDefault();
                closeEdit();
              }
            }}
            autoFocus
            maxLength={maxChars}
          />
          <CharCount $theme={theme}>
            {value.length}/{maxChars}
          </CharCount>
        </StyledInputWrapper>
      ) : (
        // Show div when not editing with hover effect
        <StyledLabelDiv $theme={theme} onClick={openEdit}>
          {value}
        </StyledLabelDiv>
      )}
    </StyledCsNodeHeader>
  );
};

export default NodeHeader;
