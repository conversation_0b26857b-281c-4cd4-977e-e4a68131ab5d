<div class="flex flex-col rounded-3xl">
  <div
    class="w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      Change password
    </div>
    <div class="flex items-center justify-end">
      <ng-icon
        name="heroXMark"
        class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></ng-icon>
    </div>
  </div>

  <div class="px-6 pt-6 pb-[3px]" [formGroup]="formGroupChangePass">
    <!--New password-->
    <div>
      <div class="relative">
        <dx-form-field id="newPassword">
          <dx-label class="text-sm">New Password</dx-label>
          <input
            dxInput
            formControlName="password"
            [type]="showPassword() ? 'text' : 'password'"
            placeholder="Password"
          />
          <ng-icon
            dxSuffix
            [name]="showPassword() ? 'heroEyeSlash' : 'heroEye'"
            class="text-xl !text-neutral-content dark:!text-dark-neutral-content m-3 cursor-pointer"
            (click)="toggleVisibility('password')"
          ></ng-icon>
        </dx-form-field>
        @if (formControlPassword.errors && (formControlPassword.touched ||
        formControlPassword.dirty)) { @if
        (formControlPassword.errors['required']) {
        <dx-error class="error-message">New Password is required.</dx-error>
        } @if (formControlPassword.errors['hasNumber']) {
        <dx-error class="error-message"
          >New Password must contain at least one number.</dx-error
        >
        } @if (formControlPassword.errors['hasCapitalCase']) {
        <dx-error class="error-message"
          >New Password must contain at least one uppercase letter.</dx-error
        >
        } @if (formControlPassword.errors['hasSmallCase']) {
        <dx-error class="error-message"
          >New Password must contain at least one lowercase letter.</dx-error
        >
        } @if (formControlPassword.errors['hasSpecialCharacters']) {
        <dx-error class="error-message"
          >New Password must contain at least one special character.</dx-error
        >
        } }
      </div>
    </div>
    <!--Confirm new password-->
    <div>
      <dx-form-field id="confirmPassword">
        <dx-label class="text-sm required">Confirm New Password</dx-label>
        <input
          dx-input
          formControlName="confirmPassword"
          [type]="showConfirmPassword() ? 'text' : 'password'"
          placeholder="Confirm Password"
          [required]="true"
        />
        <ng-icon
          dxSuffix
          [name]="showConfirmPassword() ? 'heroEyeSlash' : 'heroEye'"
          class="text-xl !text-neutral-content dark:!text-dark-neutral-content m-3 cursor-pointer"
          (click)="toggleVisibility('confirmPassword')"
        ></ng-icon>
        @if (formGroupChangePass.get('confirmPassword')?.errors?.['required'] &&
        formGroupChangePass.touched) {
        <dx-error class="error-message"
          >Confirm New Password is required.</dx-error
        >
        } @if (formControlConfirmPassword.errors?.['mustMatch'] &&
        formControlConfirmPassword.touched) {
        <dx-error class="error-message">Passwords do not match.</dx-error>
        }
      </dx-form-field>
    </div>
  </div>

  <div
    class="flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="closeDialogChangePass()">Close</button>
    <button
      dxLoadingButton="filled"
      [loading]="isLoading()"
      [disabled]="formGroupChangePass.invalid"
      spinColor="white"
      (click)="changePassword()"
    >
      Save
    </button>
  </div>
</div>
