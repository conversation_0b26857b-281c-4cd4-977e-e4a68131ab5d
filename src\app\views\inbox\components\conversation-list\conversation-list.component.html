<div
  class="list-conversation overflow-y-auto border-r border-r-primary-border dark:border-r-dark-primary-border min-w-40"
  infiniteScroll
  (scrolled)="onScroll()"
  [scrollWindow]="false"
  [infiniteScrollDistance]="2"
  [infiniteScrollThrottle]="300"
  [infiniteScrollDisabled]="isLoadingConversation() || !hasMoreData()"
  [ngClass]="{ active: isChatListActive() }"
> 
  @if (conversations() && conversations().length > 0) {
  @for (conversation of conversations(); track trackByFn(i, conversation); let i
  = $index) {
  <div
    #conversationItem
    class="conversation-item border-b border-b-primary-border dark:border-b-dark-primary-border"
    [ngStyle]="{ 'max-width': 'calc(100% - 0px)' }"
    [ngClass]="{ selected: selectedConversationId() === conversation.id }"
    (click)="onSelectConversation(conversation.id)"
  >
    <div class="flex items-start space-x-2 w-full">
      @switch (conversation?.platform_name) { @case (TYPE_INTEGRATION.MESSENGER)
      {
      <div class="w-10 h-10 object-cover flex">
        <img
          src="./assets/icon/icon-messenger.svg"
          alt="This is icon"
          height="104"
          width="104"
        />
      </div>
      } @case (TYPE_INTEGRATION.SLACK) {
      <div class="w-10 h-10 object-cover flex">
        <img src="./assets/icon/icon-slack.svg" alt="This is icon" />
      </div>
      } @case (TYPE_INTEGRATION.WHATSAPP) {
      <div class="w-10 h-10 object-cover flex">
        <img src="./assets/icon/icon-whatsapp.svg" alt="This is icon" />
      </div>
      } @case (TYPE_INTEGRATION.FRESHCHAT) {
      <div class="w-10 h-10 object-cover flex">
        <img src="./assets/icon/icon-freshchat.svg" alt="This is icon" />
      </div>
      } @case (TYPE_INTEGRATION.TELEGRAM) {
      <div class="w-10 h-10 object-cover flex">
        <img src="./assets/icon/icon-telegram.svg" alt="This is icon" />
      </div>
      } @default {
      <div class="bg-primary p-3 flex rounded-full">
        <svg
          class="!text-light-white"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M5.83398 7.49984C5.83398 7.0396 6.20708 6.6665 6.66732 6.6665H13.334C13.7942 6.6665 14.1673 7.0396 14.1673 7.49984C14.1673 7.96007 13.7942 8.33317 13.334 8.33317H6.66732C6.20708 8.33317 5.83398 7.96007 5.83398 7.49984Z"
            fill="white"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M5.83398 10.8333C5.83398 10.3731 6.20708 10 6.66732 10H10.0007C10.4609 10 10.834 10.3731 10.834 10.8333C10.834 11.2936 10.4609 11.6667 10.0007 11.6667H6.66732C6.20708 11.6667 5.83398 11.2936 5.83398 10.8333Z"
            fill="white"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M5.59904 14.553C5.89857 14.3034 6.27612 14.1667 6.66602 14.1667H15.8327C16.2929 14.1667 16.666 13.7936 16.666 13.3333V5C16.666 4.53976 16.2929 4.16667 15.8327 4.16667H4.16602C3.70578 4.16667 3.33268 4.53976 3.33268 5V16.4416L5.59904 14.553ZM6.66602 15.8333H15.8327C17.2134 15.8333 18.3327 14.714 18.3327 13.3333V5C18.3327 3.61929 17.2134 2.5 15.8327 2.5H4.16602C2.7853 2.5 1.66602 3.61929 1.66602 5V16.4416C1.66602 17.8547 3.31411 18.6266 4.39965 17.722L6.66602 15.8333Z"
            fill="white"
          />
        </svg>
      </div>
      } }
      <div
        class="flex flex-col space-y-1.5 w-full"
        style="max-width: calc(100% - 30px)"
      >
        <div class="flex items-center space-x-1 w-full">
          @if (!conversation.isEditName) {
          <span
            class="truncate max-w-[154px] font-semibold text-base-content dark:text-dark-base-content"
            >{{ conversation.name }}</span
          >
          } @if (conversation.isEditName) {
          <dx-form-field
            class="max-w-[14rem]"
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
            (click)="$event.stopPropagation()"
            (clickOutside)="
              onCloseEditNameConversation($event, conversation.id)
            "
          >
            <input
              dxInput
              [ngModel]="nameConversationNew()"
              (ngModelChange)="nameConversationNewChange.emit($event)"
              placeholder="Conversation name"
            />
            <div dxSuffix class="flex space-x-2 items-center mr-3">
              <app-svg-icon
                type="icCheck"
                class="w-5 h-5 flex !text-success dark:!text-dark-success cursor-pointer"
                (click)="onSaveEditNameConversation($event, conversation.id)"
                dxTooltip="Save edit"
                dxTooltipPosition="above"
              ></app-svg-icon>
              <app-svg-icon
                type="icClose"
                class="w-5 h-5 flex !text-error dark:!text-dark-error cursor-pointer"
                (click)="onCloseEditNameConversation($event, conversation.id)"
                dxTooltip="Cancel edit"
                dxTooltipPosition="above"
              ></app-svg-icon>
            </div>
          </dx-form-field>
          <!--<input class="max-w-[12rem] px-1.5 border" type="text" [value]="nameConversationNew()"
                     (input)="onNameInputChange($event)" (click)="$event.stopPropagation()"/>-->
          } @if (selectedConversationId() === conversation.id) { @if
          (!conversation.isEditName) {
          <app-svg-icon
            type="icEdit"
            class="w-5 h-5 flex !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
            (click)="onEditNameConversation($event, conversation.id)"
            dxTooltip="Rename conversation"
            dxTooltipPosition="above"
          ></app-svg-icon>
          } }
        </div>
        <div class="flex w-full">
          <span
            class="text-xs leading-none text-neutral-content dark:text-dark-neutral-content"
            >Last message:
            {{
              conversation.last_message_at + "Z" | date : "dd/MM/yyyy HH:mm"
            }}</span
          >
        </div>
        @if (conversation.assigned) {
        <div class="flex items-center w-full">
          @if (conversation.assigned !== 'Unassigned') {
          <div
            class="bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border px-2 py-0.5 flex rounded-full"
          >
            <span
              class="text-xs text-light-text font-semibold dark:text-dark-text"
              >{{ conversation.assigned }}</span
            >
          </div>
          } @else {
          <div
            class="bg-light-gray dark:bg-dark-gray px-2 py-0.5 flex rounded-full"
          >
            <span
              class="text-xs text-[#676D73] font-semibold dark:text-dark-text"
              >{{ conversation.assigned || "Unassigned" }}</span
            >
          </div>
          }
        </div>
        }
        <!--          list tag-->
        <div
          class="flex flex-wrap listTag !mt-2 gap-2"
          [ngClass]="{
            selected: selectedConversationId() === conversation.id,
            unselected: selectedConversationId() !== conversation.id
          }"
        >
          <!-- Hiển thị tag hoặc placeholder tùy thuộc vào điều kiện -->
          @if (selectedConversationId() === conversation.id) { @for (tag of
          conversation.matchedTags; track tag) {
          <div
            class="rounded-full h-[22px] flex items-center justify-between tag tag-hover h-full tag-mini py-1.5 px-[10px]"
            [ngStyle]="{
              background: getColor(tag.config),
              color: getTextColor(getColor(tag.config)),
              'max-width': 'calc(100% - 20px)'
            }"
            [dxTooltip]="tag.name"
            dxTooltipPosition="below"
          >
            {{ tag.name }}
          </div>
          } } @else { @for (tag of conversation.matchedTags; track tag) {
          <div
            class="block-placeholder"
            [ngStyle]="{
              background: getColor(tag.config)
            }"
          >
            <div
              class="tag-hover w-full h-full rounded-full"
              [dxTooltip]="tag.name"
              dxTooltipPosition="below"
            ></div>
          </div>
          } }
        </div>
      </div>
    </div>
    @if (selectedConversationId() === conversation.id) {
    <div class="absolute right-4 top-4">
      @if (conversation.assigned && conversation.assigned !== 'Unassigned') {
      <button
        dxButton="elevated"
        (click)="onResumeConversation($event, conversation.id)"
        dxTooltip="Resume the conversation"
        dxTooltipPosition="above"
      >
        Resume
      </button>
      } @if (!conversation.assigned || conversation.assigned === 'Unassigned') {
      <button
        dxButton
        (click)="onTakeOverConversation($event, conversation.id)"
        dxTooltip="Take over the conversation"
        dxTooltipPosition="above"
      >
        Take over
      </button>
      }
    </div>
    } @if (conversation.has_unread_message) {
    <div class="absolute right-0 top-0">
      <div class="bg-light-primary p-1 m-4 rounded-full"></div>
    </div>
    }
  </div>
  } @if (isLoadingConversation()) {
  <div class="h-full flex justify-center items-center">
    <mat-spinner diameter="30"></mat-spinner>
  </div>
  }
  } @else {
    <div
      class="p-4 h-full flex justify-center items-center flex-1 text-base-content dark:text-dark-base-content dark:text-dark-base-content"
    >
      Nothing here yet—start chatting!
    </div>
  }
</div>
