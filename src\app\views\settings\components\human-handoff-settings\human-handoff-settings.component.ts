import { DragDropModule } from '@angular/cdk/drag-drop';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { Platform } from '@angular/cdk/platform';
import { CommonModule } from '@angular/common';
import { Component, effect, inject, input } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import {
  DxFormField,
  DxInput,
  DxLabel,
  DxOption,
  DxPrefix,
  DxSelect,
  DxSlideToggle,
  DxSnackBar,
  DxSuffix,
  DxTooltip,
} from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { heroInformationCircle } from '@ng-icons/heroicons/outline';
import { SvgIconComponent } from '@shared/components';
import { SettingsService } from '@shared/services';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  Subject,
  takeUntil,
} from 'rxjs';

@Component({
  selector: 'app-human-handoff-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DxTooltip,
    DxFormField,
    DxLabel,
    DxPrefix,
    DxSuffix,
    DxInput,
    DxSelect,
    DxOption,
    DxSlideToggle,
    SvgIconComponent,
    NgIconsModule,
    DragDropModule,
    MatFormFieldModule,
    MatChipsModule,
  ],
  templateUrl: './human-handoff-settings.component.html',
  providers: [provideIcons({ heroInformationCircle })],
})
export class HumanHandoffSettingsComponent {
  settings = input<any>();

  listTimezone: any[] = [
    {
      value: 'Asia/Bangkok',
      label: 'Asia/Bangkok (UTC +7)',
    },
  ];
  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  private destroy$ = new Subject<void>();

  private platform = inject(Platform);
  private snackBar = inject(DxSnackBar);
  private settingsService = inject(SettingsService);

  // FORM CONTROL
  enabled = new FormControl<number>(0);
  workingHoursEnabled = new FormControl<number>(0);
  workingHoursFrom = new FormControl<string>('');
  workingHoursTo = new FormControl<string>('');
  workingTimezone = new FormControl<string>('');
  workingDays = new FormControl<string[]>([]);
  outsideWorkingHours = new FormControl<string>('');
  question = new FormControl<string>('');
  confirmation = new FormControl<string>('');
  numSentencesBotAnswerNotTopic = new FormControl<number>(0);
  summaryEnabled = new FormControl<boolean>(false);
  onlyHuman = new FormControl<boolean>(false);
  emailNotificationEnabled = new FormControl<boolean>(false);
  emailNotifications = new FormControl<string[]>([]);
  emailNotificationsInput = new FormControl<string>('');

  constructor() {
    effect(() => {
      const currentSettings = this.settings();
      if (currentSettings) {
        this.updateSettingsFormControls(currentSettings);
      }
    });
  }

  ngOnInit(): void {
    this.initializeFormSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  updateTextAreaHeight(id: string) {
    if (this.platform.isBrowser) {
      const textarea = document.getElementById(id);
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 32 + 'px';
      }
    }
  }

  toggleEnable() {
    this.enabled.setValue(this.enabled.value === 1 ? 0 : 1);
  }

  toggleWorkingHoursEnabled() {
    this.workingHoursEnabled.setValue(
      this.workingHoursEnabled.value === 1 ? 0 : 1
    );
  }

  selectWorkingDays(day: string) {
    const days = this.workingDays.value ? [...this.workingDays.value] : [];
    const index = days.indexOf(day);
    if (index > -1) {
      days.splice(index, 1);
    } else {
      days.push(day);
    }
    this.workingDays.setValue(days);
  }

  addEmail() {
    const value = this.emailNotificationsInput.value?.trim();
    if (value) {
      if (/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
        const currentValue = [...(this.emailNotifications.value as string[])];
        if (!currentValue.includes(value)) {
          this.emailNotifications.setValue([...currentValue, value]);
          this.emailNotificationsInput.setValue('');
        } else {
          this.snackBar.open('Email already exists', '', {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        }
      } else {
        this.snackBar.open('Invalid email', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      }
    }
  }

  removeEmail(index: number) {
    const currentValue = [...(this.emailNotifications.value as string[])];
    currentValue.splice(index, 1);
    this.emailNotifications.setValue(currentValue);
  }

  private updateSettingsFormControls(settings: any) {
    this.enabled.setValue(settings?.settings?.human_handoff.enabled, {
      emitEvent: false,
    });
    this.workingHoursEnabled.setValue(
      settings?.settings?.human_handoff?.working_hours?.enabled,
      { emitEvent: false }
    );
    this.workingHoursFrom.setValue(
      settings?.settings?.human_handoff?.working_hours?.hours[0],
      { emitEvent: false }
    );
    this.workingHoursTo.setValue(
      settings?.settings?.human_handoff?.working_hours?.hours[1],
      { emitEvent: false }
    );
    this.workingTimezone.setValue(
      settings?.settings?.human_handoff?.working_hours?.timezone,
      { emitEvent: false }
    );
    this.workingDays.setValue(
      settings?.settings?.human_handoff?.working_hours?.days,
      { emitEvent: false }
    );
    this.outsideWorkingHours.setValue(
      settings?.settings?.human_handoff?.message?.outside_working_hours,
      { emitEvent: false }
    );
    this.question.setValue(
      settings?.settings?.human_handoff?.message?.question,
      { emitEvent: false }
    );
    this.confirmation.setValue(
      settings?.settings?.human_handoff?.message?.confirmation,
      { emitEvent: false }
    );
    this.numSentencesBotAnswerNotTopic.setValue(
      settings?.settings?.human_handoff?.num_sentences_bot_answer_not_topic,
      { emitEvent: false }
    );
    this.summaryEnabled.setValue(
      settings?.settings?.human_handoff?.summary?.enabled,
      { emitEvent: false }
    );
    this.onlyHuman.setValue(
      settings?.settings?.human_handoff?.only_human?.enabled,
      { emitEvent: false }
    );
    this.emailNotificationEnabled.setValue(
      settings?.settings?.human_handoff?.email_notifications?.enabled,
      { emitEvent: false }
    );
    this.emailNotifications.setValue(
      settings?.settings?.human_handoff?.email_notifications?.emails,
      { emitEvent: false }
    );
  }

  private subscribeToFormControlWithHandler(
    control: FormControl,
    handler: (value: any) => void,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    let stream = control.valueChanges.pipe(
      takeUntil(this.destroy$),
      distinctUntilChanged(),
      debounceTime(300)
    );

    if (shouldFilter) {
      stream = stream.pipe(filter(shouldFilter));
    }

    stream.subscribe({ next: handler });
  }

  private subscribeToFormControlSettings(
    control: FormControl,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    this.subscribeToFormControlWithHandler(
      control,
      this.buildUpdatedSettings.bind(this),
      shouldFilter
    );
  }

  private initializeFormSubscriptions() {
    this.subscribeToFormControlSettings(this.enabled);
    this.subscribeToFormControlSettings(this.workingHoursEnabled);
    this.subscribeToFormControlSettings(
      this.workingHoursFrom,
      (value: string) => !!value
    );
    this.subscribeToFormControlSettings(
      this.workingHoursTo,
      (value: string) => !!value
    );
    this.subscribeToFormControlSettings(this.workingTimezone);
    this.subscribeToFormControlSettings(
      this.workingDays,
      (values: string[]) => values.length > 0
    );
    this.subscribeToFormControlSettings(this.outsideWorkingHours);
    this.subscribeToFormControlSettings(this.question);
    this.subscribeToFormControlSettings(this.confirmation);
    this.subscribeToFormControlSettings(
      this.numSentencesBotAnswerNotTopic,
      (value: number) => value >= 0
    );
    this.subscribeToFormControlSettings(this.summaryEnabled);
    this.subscribeToFormControlSettings(this.onlyHuman);
    this.subscribeToFormControlSettings(this.emailNotificationEnabled);
    this.subscribeToFormControlSettings(this.emailNotifications);
  }
  private buildUpdatedSettings(): void {
    const currentSettings = this.settings()?.settings;
    const updatedSettings = {
      ...currentSettings,
      enabled: this.enabled.value,
      human_handoff: {
        ...currentSettings?.human_handoff,
        working_hours: {
          ...currentSettings?.human_handoff?.working_hours,
          enabled: this.workingHoursEnabled.value,
          hours: [this.workingHoursFrom.value, this.workingHoursTo.value],
          timezone: this.workingTimezone.value,
          days: this.workingDays.value,
        },
        message: {
          ...currentSettings?.human_handoff?.message,
          outside_working_hours: this.outsideWorkingHours.value,
          question: this.question.value,
          confirmation: this.confirmation.value,
        },
        num_sentences_bot_answer_not_topic:
          this.numSentencesBotAnswerNotTopic.value,
        summary: {
          ...currentSettings?.human_handoff?.summary,
          enabled: this.summaryEnabled.value,
        },
        only_human: {
          ...currentSettings?.human_handoff?.only_human,
          enabled: this.onlyHuman.value,
        },
        email_notifications: {
          ...currentSettings?.human_handoff?.email_notifications,
          enabled: this.emailNotificationEnabled.value,
          emails: this.emailNotifications.value,
        },
      },
    };
    this.updateSettings(updatedSettings);
  }

  private updateSettings(data: any) {
    this.settingsService.updateSetting(data).subscribe({
      next: (res) => {
        if (res) {
          this.snackBar.open(res.message, '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        }
      },
      error: (err) => {
        this.snackBar.open(err.error.detail, '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
    });
  }
}
