import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  HostListener, input,
  Input,
  OnInit,
  Output,
  TemplateRef,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DxTooltip } from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroChevronDoubleLeftMini,
  heroChevronDoubleRightMini,
  heroChevronDownMini,
  heroChevronLeftMini,
  heroChevronRightMini,
  heroEllipsisHorizontalMini,
} from '@ng-icons/heroicons/mini';
import { ClickOutsideDirective } from '@shared/directives';
import {heroPencilSquare} from '@ng-icons/heroicons/outline';

export interface IColumn {
  columnDef: string;
  headerName?: string;
  alignHeader?: string;
  align?: string;
  flex?: number;
  minWidth?: string;
  maxWidth?: string;
  canSort?: boolean;
  actions?: IActionConfig[];
  cellRenderer?: any;
}

export interface IActionConfig {
  case?: string;
  condition?: boolean | ((row: any) => boolean);
  name?: string;
  title?: string;
  class?: string;
}

export interface IAction {
  type?: string;
  icon?: string;
  color?: string;
  tooltip?: string;
}

@Component({
  selector: 'app-data-table',
  templateUrl: './data-table.component.html',
  styleUrls: ['./data-table.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgIconsModule,
    OverlayModule,
    ClickOutsideDirective,
    DxTooltip,
  ],
  providers: [
    provideIcons({
      heroEllipsisHorizontalMini,
      heroChevronDownMini,
      heroChevronLeftMini,
      heroChevronDoubleLeftMini,
      heroChevronRightMini,
      heroChevronDoubleRightMini,
    }),
  ],
})
export class DataTableComponent implements OnInit {
  @Input() rows: any = [];
  @Input() columns: IColumn[] = [];
  @Input() pageIndex: any = 0;
  @Input() limit: any = 10;
  @Input() count: any = 0;
  @Input() rowTemplate: TemplateRef<any> | null | undefined = null;
  @Input() actionTemplate: TemplateRef<any> | null = null;
  @Input() isShowAction: boolean = true;
  @Input() trackBy: (index: number, item: any) => any = (
    index: number,
    item: any
  ) => index;
  @Input() hiddenPaginator: boolean = false;
  @Output() pageChange = new EventEmitter<any>();
  @Output() action = new EventEmitter<any>();

  listActions: IAction[] = [
    {
      type: 'delete',
      icon: 'heroTrashSolid',
      color: '#FF0000',
      tooltip: 'Delete',
    },
    {
      type: 'tool',
      icon: 'heroBoltMini',
      color: '#7241FF',
      tooltip: 'Tool',
    },
  ];
  displayedActions: (IAction | undefined)[] | undefined = [];

  // Pagination properties
  pageSizeOptions: number[] = [10, 20, 50, 100];

  // Dropdown state
  activeDropdown: any = null;

  // Track if a click is outside the dropdown
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // Check if we have an active dropdown and if the click is outside the dropdown
    if (this.activeDropdown) {
      const clickedElement = event.target as HTMLElement;
      const dropdownElements = document.querySelectorAll('.action-dropdown');

      let clickedInsideDropdown = false;
      dropdownElements.forEach((dropdown) => {
        if (dropdown.contains(clickedElement)) {
          clickedInsideDropdown = true;
        }
      });

      // Also check if clicked on the action button itself
      const actionButtons = document.querySelectorAll('.action-button');
      actionButtons.forEach((button) => {
        if (button.contains(clickedElement)) {
          // Don't close if clicking the button that toggles the dropdown
          return;
        }
      });

      if (!clickedInsideDropdown) {
        this.closeDropdowns();
      }
    }
  }

  get displayedColumns() {
    return this.columns.map((c) => c.columnDef);
  }

  constructor() {}

  ngOnInit() {
    // Initialize displayed actions if there's an action column
    this.getListActions();
  }

  changePage(e: any): void {
    this.pageChange.emit(e);
  }

  onPageSizeChange(size: number): void {
    this.pageChange.emit({ pageIndex: this.pageIndex, pageSize: size });
  }

  onPageChange(page: number): void {
    this.pageChange.emit({ pageIndex: page, pageSize: this.limit });
  }

  // Helper for pagination
  get Math() {
    return Math;
  }

  // Generate page numbers for pagination
  getPageNumbers(): number[] {
    const totalPages = Math.ceil(this.count / this.limit);
    const currentPage = this.pageIndex + 1;

    // Show at most 5 page numbers
    const maxPagesToShow = 5;

    // Simple algorithm for Tailwind CSS 4 compatibility
    let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

    // Adjust start page if we're near the end
    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    // Generate array of page numbers
    return Array.from(
      { length: endPage - startPage + 1 },
      (_, i) => startPage + i
    );
  }

  getRowIndex(row: any): any {
    return this.pageIndex * this.limit + this.rows.indexOf(row);
  }

  calcColumnWidth(column: any): any {
    const totalFlex = this.columns.reduce(
      (total, col) => (col.flex ?? 1) + total,
      0
    );
    return ((column.flex ?? 1) / totalFlex) * 100 + '%';
  }

  getListActions(): any {
    this.displayedActions = this.columns
      .find((col) => col.columnDef === 'action')
      ?.actions?.map((act) => this.listActions.find((a) => a.type === act));
  }

  changeData(row: any): void {
    // Close any open dropdown first
    this.rows.forEach((i: { isActions: boolean }) => (i.isActions = false));

    // Toggle the dropdown for this row
    row.isActions = !row.isActions;
    this.activeDropdown = row.isActions ? row : null;
  }

  closeDropdowns(): void {
    this.rows.forEach((i: { isActions: boolean }) => (i.isActions = false));
    this.activeDropdown = null;
  }

  emitAction(actionType: any, rowData: any): void {
    const data = {
      type: actionType,
      data: rowData,
    };
    this.action.emit(data);
  }

  isActionConditionMet(actionConfig: IActionConfig, row: any): boolean {
    if (typeof actionConfig.condition === 'function') {
      return actionConfig.condition(row);
    }
    return actionConfig.condition !== undefined ? actionConfig.condition : true;
  }

  handleAction(actionCase: string, row: any): void {
    row.isActions = false;
    this.emitAction(actionCase, row);
  }

  /**
   * Gets the original text for a cell to use in tooltip
   * @param row The row data
   * @param column The column definition
   * @returns The original text without truncation
   */
  getOriginalText(row: any, column: IColumn): string {
    if (!row || !column.columnDef) return '';

    // If there's a cell renderer, we need to get the original text
    if (column.cellRenderer) {
      // Try to get the original text from the row data
      const originalValue = row[column.columnDef];
      return originalValue ? String(originalValue) : '';
    }

    // Otherwise just return the value from the row
    return row[column.columnDef] ? String(row[column.columnDef]) : '';
  }
}
