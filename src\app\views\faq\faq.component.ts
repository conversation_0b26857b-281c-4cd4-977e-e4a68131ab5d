import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { Component, inject, On<PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroArrowPath,
  heroCheck,
  heroEllipsisHorizontal,
  heroMagnifyingGlass,
  heroPencilSquare,
  heroPlus,
  heroTrash,
  heroXMark,
} from '@ng-icons/heroicons/outline';
import {
  DataTableComponent,
  IColumn,
} from '@shared/components/data-table/data-table.component';
import { SelectOption } from '@shared/components/select/select.component';
import { debounceTime, Subject, Subscription } from 'rxjs';

import {
  DxButton,
  DxDialog,
  DxFormField,
  DxInput,
  DxOption,
  DxPrefix,
  DxSelect,
  DxSnackBar,
  DxTooltip,
} from '@dx-ui/ui';
import { SvgIconComponent } from '@shared/components';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { ClickOutsideDirective } from '@shared/directives';
import { IFaq, IFile } from '@shared/models';
import {
  FaqService,
  FeedbackService,
  KnowledgeBaseService,
} from '@shared/services';
import { AddOrEditFaqComponent } from '@views/faq/add-or-edit-faq/add-or-edit-faq.component';

@Component({
  selector: 'app-faq',
  standalone: true,
  imports: [
    CommonModule,
    DataTableComponent,
    FormsModule,
    ReactiveFormsModule,
    OverlayModule,
    NgIconsModule,
    DxFormField,
    DxSelect,
    DxOption,
    DxInput,
    DxButton,
    ClickOutsideDirective,
    DxTooltip,
    DxPrefix,
    SvgIconComponent,
  ],
  templateUrl: './faq.component.html',
  styleUrls: ['./faq.component.css'],
  providers: [
    provideIcons({
      heroEllipsisHorizontal,
      heroTrash,
      heroPlus,
      heroPencilSquare,
      heroCheck,
      heroXMark,
      heroArrowPath,
      heroMagnifyingGlass,
    }),
  ],
})
export class FaqComponent implements OnInit, OnDestroy {
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  idFile!: number;
  listFAQ: IFaq[] = [];

  // Helper method to truncate text for display
  truncateText(text: string, maxLength: number = 100): string {
    if (!text) return '';
    return text.length > maxLength
      ? text.substring(0, maxLength) + '...'
      : text;
  }

  columns: IColumn[] = [
    {
      columnDef: 'index',
      flex: 0.1,
      headerName: 'No.',
    },
    {
      columnDef: 'question',
      minWidth: '200px',
      maxWidth: '400px',
      flex: 0.4,
      headerName: 'Question',
      cellRenderer: (row: any) => this.truncateText(row.question, 50),
    },
    {
      columnDef: 'response',
      minWidth: '200px',
      maxWidth: '400px',
      flex: 0.4,
      headerName: 'Response',
      cellRenderer: (row: any) => this.truncateText(row.response, 50),
    },
    {
      columnDef: 'files',
      minWidth: '150px',
      maxWidth: '300px',
      flex: 0.3,
      headerName: 'Sources',
    },
    {
      columnDef: 'faq_status',
      flex: 0.2,
      minWidth: '160px',
      headerName: 'Status',
    },
    {
      columnDef: 'status_note',
      flex: 0.3,
      headerName: 'Status note',
      cellRenderer: (row: any) =>
        row.status_note ? this.truncateText(row.status_note, 30) : '',
    },
    {
      columnDef: 'action',
      flex: 0.1,
      headerName: 'Action',
      actions: [
        {
          case: 'edit',
          condition: true,
          name: 'heroPencilSquareMini',
          title: 'Edit FAQ',
          class: 'text-2xl text-light-orange',
        },
        {
          case: 'changeStatus',
          condition: (row: any) => row.faq_status === 'NOT_READY',
          name: 'heroCheckMini',
          title: 'Change status to ready',
          class: 'text-2xl text-light-green',
        },
        {
          case: 'changeStatus',
          condition: (row: any) => row.faq_status === 'READY',
          name: 'heroArrowPathMini',
          title: 'Change status to not ready',
          class: 'text-2xl text-light-orange',
        },
        {
          case: 'delete',
          condition: true,
          name: 'heroTrashMini',
          title: 'Delete FAQ',
          class: 'text-2xl text-light-red',
        },
      ],
    },
  ];

  // Extend the base searchModel with FAQ specific properties
  searchModel: any = {
    key_word: '',
    status: null,
    page: 0,
    pageSize: 20,
  };
  statusOptions: SelectOption[] = [
    {
      label: 'All Statuses',
      value: 'null',
    },
    {
      label: 'Ready',
      value: 'READY',
    },
    {
      label: 'Not Ready',
      value: 'NOT_READY',
    },
  ];
  page_size: number = 20;
  count!: number;
  pageIndex: number = 0;
  selectedStatus: string = 'null'; // For app-select binding
  faqForm: FormGroup = inject(FormBuilder).group({
    question: [''],
    response: [''],
    file_ids: [],
  });
  listKnowledgeBase: IFile[] = [];
  moreSourceRef!: MatDialogRef<any>;
  knowledgeBaseSelectedDraft: number[] = [];

  get displayedColumns(): any {
    return this.columns.map((c) => c.columnDef);
  }

  private subscription: Subscription = new Subscription();
  knowledgeBaseSelected: number[] = [];
  dialogService = inject(DxDialog);

  // Default colors for tags
  colors = [
    '#baf3db',
    '#f8e6a0',
    '#fedec8',
    '#ffd5d2',
    '#dfd8fd',
    '#4bce97',
    '#f5cd47',
    '#fea362',
    '#f87168',
    '#9f8fef',
    '#1f845a',
    '#946f00',
    '#c25100',
    '#c9372c',
    '#6e5dc6',
    '#cce0ff',
    '#c6edfb',
    '#d3f1a7',
    '#fdd0ec',
    '#dcdfe4',
    '#579dff',
    '#6cc3e0',
    '#94c748',
    '#e774bb',
    '#8590a2',
    '#0c66e4',
    '#227d9b',
    '#5b7f24',
    '#ae4787',
    '#626f86',
  ];
  searchSubject = new Subject<string>();
  private dialog = inject(DxDialog);
  private snackBar = inject(DxSnackBar);
  private FAQService = inject(FaqService);
  private knowledgeBaseService = inject(KnowledgeBaseService);
  private showFeedbackService = inject(FeedbackService);

  ngOnInit() {
    // Explicitly set status to null (All) on initialization
    this.searchModel.status = null;
    this.selectedStatus = 'null'; // Initialize selectedStatus to 'null' string for app-select
    this.changeFilter();
    this.callListKnowledgeBase();
    this.searchSubject.pipe(debounceTime(300)).subscribe(() => {
      this.changeFilter();
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  callListKnowledgeBase() {
    this.knowledgeBaseService.getAllFile().subscribe((res) => {
      this.listKnowledgeBase = res;
    });
  }

  callListFAQ() {
    // Ensure pageIndex is synchronized with searchModel.page
    this.pageIndex = this.searchModel.page;

    // Convert searchModel to API format
    const apiParams = {
      key_word: this.searchModel.key_word,
      status: this.searchModel.status,
      page: this.searchModel.page + 1, // API expects 1-based page index
      page_size: this.searchModel.pageSize,
    };

    this.FAQService.getListFAQ(apiParams).subscribe((res) => {
      this.listFAQ = res.items;
      this.count = res.total;
    });
  }

  deleteFile(id: number) {
    this.idFile = id;
    this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Delete this FAQ',
          content: 'Are you sure delete this FAQ ?',
          isDelete: true,
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value) => {
        if (!!value) {
          this.confirmDeleteFile(id);
        }
      });
  }

  /**
   * Handle pagination events from the data-table component
   * This is called when the user changes the page or page size
   */
  changePage(event: any) {
    this.pageIndex = event.pageIndex; // Update the pageIndex for the data-table component
    this.searchModel.page = event.pageIndex;
    this.searchModel.pageSize = event.pageSize;
    this.callListFAQ();
  }

  changeFilter() {
    this.searchModel.page = 0;
    this.pageIndex = 0; // Update the pageIndex for the data-table component
    // Keep selectedStatus in sync with searchModel.status
    this.selectedStatus =
      this.searchModel.status === null ? 'null' : this.searchModel.status;
    if (this.paginator) {
      this.paginator.pageIndex = 0;
    }
    this.callListFAQ();
  }

  calcColumnWidth(column: any): any {
    if (column.columnWidth) {
      return column.columnWidth;
    }
    const totalFlex = this.columns?.reduce(
      (total, col) => (col.flex ?? 1) + total,
      0
    );
    return (column.flex ?? 1) / totalFlex + '%';
  }

  confirmDeleteFile(id: number) {
    this.FAQService.deleteFAQ(id).subscribe({
      next: (res) => {
        this.showSnackBar('Delete FAQ successful', 'success');
      },
      error: (err) => {
        this.showSnackBar('Delete FAQ failed', 'error');
      },
      complete: () => {
        this.callListFAQ();
      },
    });
  }

  // Handle search input change
  onSearchChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.searchModel.key_word = input.value;
    this.changeFilter();
  }

  // Handle status filter change
  onStatusChange(): void {
    // 'null' string means 'All Statuses'
    const value = this.selectedStatus;
    this.searchModel.status = value === 'null' ? null : value;
    this.searchSubject.next(value);
    // this.changeFilter();
  }

  // The onPageChange method is no longer needed as we're directly using changePage

  /**
   * Get the row index for display in the table
   * This matches the behavior in the data-table component
   */
  getRowIndex(row: any): any {
    return (
      this.pageIndex * this.searchModel.pageSize + this.listFAQ.indexOf(row)
    );
  }

  handleAction(caseName: string, element: any) {
    // Close dropdown if it's open
    if (element.isActions) {
      element.isActions = false;
    }
    switch (caseName) {
      case 'edit':
        /*const initialValues = {
          response: element.response,
          question: element.question,
          file_ids: element.files,
        };
        this.knowledgeBaseSelected = element.files;
        this.faqForm.setValue(initialValues);*/
        this.dialog.open(AddOrEditFaqComponent, {
          data: {
            faq: {
              id: element.id,
              question: element.question,
              response: element.response,
              file_ids: element.files,
            },
            listKnowledgeBase: this.listKnowledgeBase,
            knowledgeBaseSelected: element.files,
            knowledgeBaseSelectedDraft: [],
            isEdit: true,
          },
          width: '60vw',
          minWidth: '400px',
        }).afterClosed().subscribe(() => this.callListFAQ());
        break;
      case 'changeStatus':
        this.dialog
          .open(ConfirmDialogComponent, {
            data: {
              title:
                element.faq_status === 'NOT_READY'
                  ? 'FAQ is ready'
                  : 'FAQ is not ready',
              content:
                element.faq_status === 'NOT_READY'
                  ? 'Are you sure this FAQ is ready?'
                  : 'Are you sure this FAQ is not ready?',
              isDelete: true,
            },
            width: '300px',
          })
          .afterClosed()
          .subscribe((value: any) => {
            if (!!value) {
              this.changeStatus(element);
            }
          });
        break;
      case 'delete':
        this.deleteFile(element.id);
        break;
    }
  }

  onAction(event: any): void {
    const { type, data } = event;
    this.handleAction(type, data);
  }

  openFAQDialog() {
    this.faqForm.reset();
    this.knowledgeBaseSelected = [];
    this.dialog
      .open(AddOrEditFaqComponent, {
        data: {
          faq: {
            question: '',
            response: '',
            file_ids: [],
          },
          listKnowledgeBase: this.listKnowledgeBase,
          knowledgeBaseSelected: [],
          knowledgeBaseSelectedDraft: [],
        },
        width: '60vw',
        minWidth: '400px',
      })
      .afterClosed()
      .subscribe(() => this.callListFAQ());
  }

  private changeStatus(element: IFaq) {
    element.faq_status =
      element.faq_status === 'NOT_READY' ? 'READY' : 'NOT_READY';
    if (element.id === undefined) {
      this.showSnackBar('Cannot update FAQ without ID', 'error');
      return;
    }
    this.FAQService.updateFAQ(element.id, element).subscribe({
      next: (res) => {
        this.showSnackBar('Change status successful', 'success');
        this.callListFAQ();
      },
      error: (err) => {
        this.showSnackBar('Change status failed', 'error');
        this.callListFAQ();
      },
    });
  }

  knowledgeBaseTooltip(knowledgeBase: IFile | null): string {
    if (!knowledgeBase) return 'No information available';
    return `name: ${knowledgeBase.name}\nsource: ${
      knowledgeBase.file_path ?? knowledgeBase.url ?? 'N/A'
    }`;
  }

  getElementById(id: number): IFile | null {
    const found = this.listKnowledgeBase.find((item) => item.id === id);
    return found ?? null; // Trả về tên hoặc null nếu không tìm thấy
  }

  /**
   * Get color from config string
   * @param config JSON string containing color information
   * @returns color string
   */
  getColor(config: string | null): string {
    if (!config) return '#7241FF'; // Default primary color

    try {
      const parsedConfig = JSON.parse(config);
      return parsedConfig.color || '#7241FF';
    } catch (error) {
      console.error('Invalid config:', config);
      return '#7241FF'; // Default color if error
    }
  }

  /**
   * Determine text color based on background color brightness
   * @param bgColor Background color in hex format
   * @returns 'black' or 'white' depending on background brightness
   */
  getTextColor(bgColor: string): string {
    // Convert from hex to RGB
    const r = parseInt(bgColor.slice(1, 3), 16);
    const g = parseInt(bgColor.slice(3, 5), 16);
    const b = parseInt(bgColor.slice(5, 7), 16);

    // Calculate luminance
    const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

    // Decide text color
    return luminance > 128 ? 'black' : 'white';
  }

  /**
   * Get a random color from the colors array
   * @param id ID to use for consistent color selection
   * @returns A color from the colors array
   */
  getRandomColor(id: number): string {
    return this.colors[id % this.colors.length];
  }
  showSnackBar(message: string, type: 'success' | 'error' | 'info') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }
}
