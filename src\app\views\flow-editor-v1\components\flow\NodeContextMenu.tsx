// @ts-nocheck
import { nodeApi } from "@flow-editor-v1/api";
import { goToBlockNode } from "@flow-editor-v1/init";
import { BuildFlowState, NodeFlow } from "@flow-editor-v1/model";
import { NodeDefine } from "@flow-editor-v1/model/bo";
import { useBuildFlowState, useFlowInstanceState, useLayoutState, useMenuState } from "@flow-editor-v1/store";
import { getUniqueNodeId } from "@flow-editor-v1/utils/flow";
import type { MenuProps } from 'antd';
import { Layout, Menu } from "antd";
import React, { memo, useEffect, useState } from "react";
import { useReactFlow } from "reactflow";
import styled from "styled-components";

const {Header, Content, Footer, Sider} = Layout;

type MenuItem = Required<MenuProps>['items'][number];

const NodeContextMenuStyled = styled.div<{ $top: number; $left: number }>`
  position: absolute;
  top: ${(props) => props.$top}px;
  left: ${(props) => props.$left}px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
`;

const PlusStyled = styled.div`
  background: #141414;
  box-shadow: 0 4px 100px 0 rgba(102, 77, 255, 0.3);
  border: 2px solid #7241ff;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  color: white;
  border-radius: 100%;
  cursor: pointer;

  &:hover {
    background: #7241ff;
  }
`;

const StyledNodeItem = styled.div`
  cursor: pointer;
  align-items: center;
  border: 1px solid transparent;
  border-radius: 12px;
  display: flex;
  gap: 16px;
  padding: 16px;
  font-weight: normal;
  margin-right: 16px;

  .node-name {
    margin-bottom: 3px;
    color: white;
    font-size: 14px;
  }

  .node-des {
    color: #94a2b8;
    font-size: 12px;
  }

  &:hover {
    border-radius: 8px;
    border: 1px solid #7241ff;
    background: #010314;
  }

  &.active {
    border-radius: 8px;
    border: 1px solid #7241ff;
    background: #010314;
  }
`;

const StyledNodeItemII = styled.div`
  cursor: move;
  align-items: center;
  border: 1px solid transparent;
  border-radius: 8px;
  display: flex;
  gap: 16px;
  padding: 16px;
  font-weight: normal;
  margin-right: 16px;
  grid-column: span 1 / span 1;

  .node-name {
    margin-bottom: 3px;
    color: white;
    font-size: 14px;
  }

  .node-des {
    color: #94a2b8;
    font-size: 12px;
  }

  &:hover {
    border-radius: 8px;
    border: 1px solid #7241ff;
    background: #010314;
  }
`;


const itemsMappingData: MenuItem[] = [
  {
    label: "Prompts",
    key: "Prompt",
  },
  {
    label: "Message",
    key: "Message",
  },
  {
    label: "Actions",
    key: "Action",
  },
  {
    label: "Logic",
    key: "Logic",
  },
  {
    label: "Others",
    key: "Others"
  }
];

const NodeContextMenu = (props) => {
  const {top, left, paramSourceNode, collapsed} = props;
  const [itemsMapping, setItemsMapping] = useState<MenuItem[]>(itemsMappingData);
  const [nodes, setListNodes] = useState<Array<NodeDefine>>([]);
  const {setDirty} = useBuildFlowState<BuildFlowState>((state: BuildFlowState) => state);
  const {setMenu} = useMenuState(state => state);
  const {flowInstance} = useFlowInstanceState(state => state);
  const {flow} = useBuildFlowState(state => state);
  const {setNodes, setEdges, getNodes, getEdges} = useReactFlow();
  const {theme} = useLayoutState(state => state);

  useEffect(() => {
    if (flow) {
      const fetchNodes = async () => {
        const res: Array<NodeDefine> = await nodeApi.getListNode(flow.trigger_type);
        res.push(goToBlockNode);
        setListNodes(res);
        const groupedNodes = res.reduce((acc, node) => {
          const {type, name, id} = node;
          const menuItem = {label: name, key: name};

          if (!acc[type]) {
            acc[type] = [];
          }
          acc[type].push(menuItem);
          return acc;
        }, {});

        const updatedItemsMapping = itemsMapping.map((item) => ({
          ...item,
          children: groupedNodes[item.key as string] || [] // Cast key to string
        }));

        setItemsMapping(updatedItemsMapping);
      };

      fetchNodes().catch(console.error);
    }
  }, [flow]);

  const handleClickNode = (
    event,
  ) => {
    const position = flowInstance.screenToFlowPosition({
      x: collapsed ? (left + 320) : (left + 580),
      y: top + 134,
    });

    const nodeDataTransfer = nodes.find((node) => node.name === event.key).data;
    if (flow && flow.id && nodeDataTransfer) {
      const nodeData = JSON.parse(nodeDataTransfer)

      if (nodeData && nodeData.node_type === 'goToBlock') {
        const {source, sourceHandle} = paramSourceNode
        if (source.includes('start')) return;

        const sourceNode = getNodes().find((node) => node.id === source);
        if (sourceNode) {
          setNodes((nds) =>
            nds.map((node) => {
              if (node.id === source) {
                const oldGoToBlockSource = node.data.goToBlockSource ? [...node.data.goToBlockSource] : [];
                node.data = {
                  ...node.data,
                  selected: false,
                  debug: false,
                  goToBlockSource: [...oldGoToBlockSource, {
                    sourceHandle,
                    blockName: null,
                    blockId: null
                  }]
                };
              }
              return node;
            })
          )
          setMenu(null);
        }
      } else {
        const newNodeId = getUniqueNodeId(
          nodeData,
          flowInstance.getNodes(),
          flow.id,
        );
        const newNode: NodeFlow = {
          id: newNodeId,
          position,
          type: nodeData.node_type,
          data: {
            ...nodeData,
            id: newNodeId,
            selected: true,
            debug: false
          },
        };
        setNodes((nodesCs) =>
          nodesCs.concat(newNode).map((node) => {
            node.data = {
              ...node.data,
              selected: false,
            };
            return node;
          })
        );

        if (paramSourceNode && paramSourceNode.source && paramSourceNode.sourceHandle) {
          const newNodeTargetHandle = newNodeId + "_target";
          const connection = {
            ...paramSourceNode,
            target: newNodeId,
            targetHandle: newNodeTargetHandle,
          }
          const newEdge = {
            ...connection,
            type: "cs",
            id: `${connection.source}-${connection.sourceHandle}-${connection.target}-${connection.targetHandle}`,
            data: {
              isGoToBlock: false
            }
          };

          const nodeAlreadyConnect = getEdges().find(v => v.source === paramSourceNode.source && v.sourceHandle === paramSourceNode.sourceHandle);
          setEdges((edges) => {
            if (nodeAlreadyConnect) {
              return edges.concat(newEdge).filter((edge) => edge.id !== nodeAlreadyConnect.id).map((edge) => edge)
            }
            return edges.concat(newEdge).map((edge) => edge)
          })
        }
        setMenu(null)
      }
    }
  };

  return (
    <NodeContextMenuStyled $top={top} $left={left}>
      <Content>
        <Menu
          theme={theme}
          onClick={handleClickNode}
          style={{
            width: 256,
            borderRadius: 12
          }}
          mode="vertical"
          items={itemsMapping}
        />
      </Content>
    </NodeContextMenuStyled>
  );
};

export default memo(NodeContextMenu);
