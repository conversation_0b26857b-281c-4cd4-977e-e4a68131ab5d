// @ts-nocheck
import GoTo<PERSON>lockHandle from "@flow-editor/components/flow/GoToBlockHandle";
import CheckboxField from "@flow-editor/components/form/CheckboxField";
import InputField from "@flow-editor/components/form/InputField";
import SelectField from "@flow-editor/components/form/SelectField";
import Icon from "@flow-editor/components/icon/icon";
import NodeHeader from "@flow-editor/components/nodes/NodeHeader";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor/components/styled";
import NodeTooltip from "@flow-editor/components/tooltip/NodeTooltip";
import { COMPARISONS_TYPE, COMPARISONS_TYPE_LIST, STUDIO_STATUS, } from "@flow-editor/constant";
import { useFlowInstance } from "@flow-editor/hook";
import { ConditionNodeData } from "@flow-editor/model";
import {
  useBuildFlowState,
  useFlowInstanceState,
  useLayoutState,
  useStudioState
} from "@flow-editor/store";
import { hexToRgb } from "@flow-editor/utils/util";
import { yupResolver } from "@hookform/resolvers/yup";
import { Divider, Modal, Typography } from "antd";
import _ from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useFieldArray, useForm, useWatch } from "react-hook-form";
import { Handle, Position, useUpdateNodeInternals } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";

const StyledHandleSourceAnchor = styled.div<{ $bgColor; $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => (props.$data ? "white" : `rgba(255, 255, 255, 0.5)`)};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => (props.$data ? "normal" : "italic")};
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const conditionListFormSchema = yup.object().shape({
  if_condition: yup.string(),
  comparison: yup.string(),
  value: yup.string(),
  use_text: yup.boolean(),
  condition_text: yup.string(),
});

const conditionNodeDataFormSchema = yup.object().shape({
  condition: yup
    .array()
    .of(conditionListFormSchema)
    .required("Condition is required")
    .default([
      {
        if_condition: "",
        comparison: COMPARISONS_TYPE.EQUAL_TO,
        value: "",
        use_text: false,
        condition_text: "",
      },
    ]),
});

const IfElseNode = ({data}: { data: ConditionNodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const {flowInstance} = useFlowInstanceState((state) => state);
  const {flow, setDirty} = useBuildFlowState(state => state);
  const {status} = useStudioState((state) => state);
  const {deleteEdge, resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const previousUseTextValues = useRef<(boolean | undefined)[]>([]);
  const updateNodeInternals = useUpdateNodeInternals();
  const {theme} = useLayoutState((state) => state);
  const {control, setValue, handleSubmit, reset} = useForm({
    resolver: yupResolver(conditionNodeDataFormSchema),
  });

  const {
    fields: conditionFields,
    append: conditionAppend,
    remove: conditionRemove,
    replace: conditionReplace,
  } = useFieldArray({
    control,
    name: "condition",
  });

  const conditionFormArray = useWatch({
    control,
    name: "condition",
  });

  const handleAddCondition = () => {
    const isUseText =
      conditionFormArray &&
      conditionFormArray.find((v) => v.use_text) &&
      conditionFormArray.find((v) => v.use_text).use_text;
    conditionAppend({
      if_condition: "",
      comparison: COMPARISONS_TYPE.EQUAL_TO,
      value: "",
      use_text: isUseText,
      condition_text: "",
    });
  };

  const handleDeleteCondition = (index: number) => {
    deleteEdge(`${data.id}_source#${index + 1}`);
    const nodeEdgesCur = flowInstance
      .getEdges()
      .filter((edge) => edge.source === data.id);
    const deletingEdge = nodeEdgesCur.find(
      (ed) =>
        ed.sourceHandle && ed.sourceHandle === `${data.id}_source#${index + 1}`
    );
    if (deletingEdge) {
      const deleteSourceHandle = Number(
        deletingEdge.sourceHandle.split("#")[1]
      );
      const edgesKeep = nodeEdgesCur.filter(
        (edge) => Number(edge.sourceHandle.split("#")[1]) < deleteSourceHandle
      );
      const edgesChange = nodeEdgesCur
        .filter(
          (edge) => Number(edge.sourceHandle.split("#")[1]) > deleteSourceHandle
        )
        .map((edge) => ({
          ...edge,
          sourceHandle: `${data.id}_source#${
            Number(edge.sourceHandle.split("#")[1]) - 1
          }`,
        }));
      flowInstance.setEdges(
        flowInstance
          .getEdges()
          .filter((edge) => edge.source !== data.id)
          .concat(edgesKeep, edgesChange)
      );
    }
    conditionRemove(index);
  };

  const resetForm = () => {
    conditionReplace([
      {
        if_condition: "",
        comparison: COMPARISONS_TYPE.EQUAL_TO,
        value: "",
        use_text: false,
        condition_text: "",
      },
    ]);
  };

  const buildLogic = (conditions) => {
    const conditionCount = conditions.length;
    let logic: string;
    for (let i = 0; i < conditionCount; i++) {
      const conditionForm = conditions[i];
      let condition;
      if (!conditionForm.condition_text) {
        condition = `${conditionForm.if_condition} ${
          COMPARISONS_TYPE_LIST.find(
            (v) => v.value === conditionForm.comparison
          ).def
        } ${conditionForm.value}`;
      } else {
        condition = conditionForm.condition_text;
      }
      if (i === 0) {
        logic = `if ${condition}: result = '${data.id}_source#${i + 1}'\n`;
      }
      if (i > 0 && i < conditionCount) {
        logic += `elif ${condition}: result = '${data.id}_source#${i + 1}'\n`;
      }
    }
    return (logic += `else: result = '${data.id}_source#${
      conditionCount + 1
    }'`);
  };

  const onSubmit = (formValue) => {
    data.condition = buildLogic(_.cloneDeep(formValue.condition));
    data.conditionSchema = _.cloneDeep(formValue.condition);
    data.output_key = formValue.output_key;

    resetForm();
    setModalOpen(false);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  };

  useEffect(() => {
    resetForm();
    if (
      data.condition &&
      Array.isArray(data.conditionSchema) &&
      data.conditionSchema.length > 0 &&
      data.conditionSchema.every(item =>
        item &&
        typeof item === 'object' &&
        item.hasOwnProperty('if_condition') && item.if_condition !== undefined && item.if_condition !== null &&
        item.hasOwnProperty('comparison') && item.comparison !== undefined && item.comparison !== null &&
        item.hasOwnProperty('value') && item.value !== undefined && item.value !== null &&
        item.hasOwnProperty('use_text') && item.use_text !== undefined && item.use_text !== null &&
        item.hasOwnProperty('condition_text') && item.condition_text !== undefined && item.condition_text !== null
      )
    ) {
      conditionReplace(data.conditionSchema);
      data.condition = buildLogic(_.cloneDeep(data.conditionSchema));
    } else {
      conditionReplace([
        {
          if_condition: "",
          comparison: COMPARISONS_TYPE.EQUAL_TO,
          value: "",
          use_text: false,
          condition_text: "",
        },
      ]);
    }
    data.output_key && setValue('output_key', data.output_key);
    updateNodeInternals(data.id);
  }, [JSON.stringify(data), modalOpen]);

  useEffect(() => {
    if (conditionFormArray && conditionFormArray.length > 0) {
      _.cloneDeep(conditionFormArray).forEach((field, index) => {
        if (previousUseTextValues.current[index] !== field.use_text) {
          setValue(`condition.${index}`, {
            if_condition: "",
            comparison: COMPARISONS_TYPE.EQUAL_TO,
            value: "",
            use_text: field.use_text,
            condition_text: "",
          })
        }
      });
      previousUseTextValues.current = _.cloneDeep(conditionFormArray).map(field => field.use_text);
    }
  }, [conditionFormArray, setValue]);

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    setModalOpen(true);
  }, []);

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $theme={theme}>
      <div className="absolute left-0 text-[10px]" style={{top: -20, color: '#D4D9E2'}}>{data.id}</div>
      {status && status === STUDIO_STATUS.DEV && (
        <NodeTooltip data={data} selected={data.selected}/>
      )}
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id={`${data.id}_target`}
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
        {data.condition ? (
          <div>
            {data.conditionSchema.filter(v => (v.if_condition && v.comparison && v.value) || (v.use_text && v.condition_text)).map((condition, index) => {
              if (index === 0) {
                return (
                  <StyledHandleSourceAnchor
                    key={index}
                    $bgColor={data.node_color}
                    $data={data.condition}
                    onDoubleClick={handleOpenModal}
                  >
                    {!(condition.condition_text && condition.use_text) ? (
                      <div className="w-full flex items-center justify-start space-x-3">
                        <div style={{fontSize: 8}} className="font-normal">
                          IF
                        </div>
                        <div className="flex-shrink-0 w-full" style={{fontSize: 8}}>{`${
                          condition.if_condition
                        } ${
                          COMPARISONS_TYPE_LIST.find(
                            (v) => v.value === condition.comparison
                          ).def
                        } ${condition.value}`}</div>
                      </div>
                    ) : (
                      <div className="w-full flex items-center justify-start space-x-3">
                        <div style={{fontSize: 8}} className="font-normal">
                          IF
                        </div>
                        <div
                          className="flex-shrink-0 w-full"
                          style={{fontSize: 8}}
                        >{`${condition.condition_text}`}</div>
                      </div>
                    )}
                  </StyledHandleSourceAnchor>
                );
              }
              if (index > 0) {
                return (
                  <StyledHandleSourceAnchor
                    key={index}
                    $bgColor={data.node_color}
                    $data={data.condition}
                    onDoubleClick={handleOpenModal}
                  >
                    {!(condition.condition_text && condition.use_text) ? (
                      <div className="w-full flex items-center justify-start space-x-3">
                        <div style={{fontSize: 8}} className="font-normal">
                          ELSE IF
                        </div>
                        <div style={{fontSize: 8}}>{`${
                          condition.if_condition
                        } ${
                          COMPARISONS_TYPE_LIST.find(
                            (v) => v.value === condition.comparison
                          ).def
                        } ${condition.value}`}</div>
                      </div>
                    ) : (
                      <div className="w-full flex items-center justify-start space-x-3">
                        <div style={{fontSize: 8}} className="font-normal">
                          ELSE IF
                        </div>
                        <div
                          style={{fontSize: 8}}
                        >{`${condition.condition_text}`}</div>
                      </div>
                    )}
                  </StyledHandleSourceAnchor>
                );
              }
            })}
            <StyledHandleSourceAnchor
              $bgColor={data.node_color}
              $data={data.condition}
              onDoubleClick={handleOpenModal}
            >
              <div className="w-full flex items-center justify-start space-x-3">
                <div style={{fontSize: 8}} className="font-normal col-span-1">
                  ELSE
                </div>
                <div></div>
              </div>
            </StyledHandleSourceAnchor>
          </div>
        ) : (
          <div>
            <StyledHandleSourceAnchor
              $bgColor={data.node_color}
              $data={data.condition}
              onDoubleClick={handleOpenModal}
            >
              <div className="w-full flex items-center justify-start space-x-3">
                <div style={{fontSize: 8}} className="font-normal">
                  IF
                </div>
                <div style={{fontSize: 8}}>Configure</div>
              </div>
            </StyledHandleSourceAnchor>
            <StyledHandleSourceAnchor
              $bgColor={data.node_color}
              $data={data.condition}
              onDoubleClick={handleOpenModal}
            >
              <div className="w-full flex items-center justify-start space-x-3">
                <div style={{fontSize: 8}} className="font-normal">
                  ELSE
                </div>
                <div></div>
              </div>
            </StyledHandleSourceAnchor>
          </div>
        )}
        <div>
          {data.conditionSchema &&
            data.conditionSchema.length && !data.goToBlockSource &&
            data.conditionSchema.filter(v => (v.if_condition && v.comparison && v.value) || (v.use_text && v.condition_text)).map((_, index) => (
              <Handle
                key={`${data.id}_source#${index + 1}`}
                type="source"
                position={Position.Right}
                id={`${data.id}_source#${index + 1}`}
                style={{
                  height: 8,
                  width: 8,
                  top: `${58 + 34 * index}px`,
                  backgroundColor: theme === 'dark' ? 'white' : 'black',
                }}
              />
            ))}

          {data.conditionSchema &&
            data.conditionSchema.length && data.goToBlockSource && data.goToBlockSource.length &&
            data.conditionSchema.filter(v => (v.if_condition && v.comparison && v.value) || (v.use_text && v.condition_text)).map((_, index) => {
              const key = `${data.id}_source#${index + 1}`
              const goToBlock = data.goToBlockSource.find(v => v.sourceHandle === key)
              if (goToBlock) {
                return <GoToBlockHandle
                  key={key}
                  data={data}
                  sourceHandle={key}
                  style={{
                    height: 14,
                    width: 84,
                    top: `${58 + 34 * index}px`,
                    right: -80,
                    backgroundColor: theme === 'dark' ? 'white' : 'black',
                    fontSize: 10,
                    color: theme === 'dark' ? 'black' : 'white',
                    border: 'none',
                    borderRadius: 10,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                />
              }

              return <Handle
                key={key}
                type="source"
                position={Position.Right}
                id={key}
                style={{
                  height: 8,
                  width: 8,
                  top: `${58 + 34 * index}px`,
                  backgroundColor: theme === 'dark' ? 'white' : 'black',
                }}
              />
            })}

          {data.conditionSchema && data.conditionSchema.length && !data.goToBlockSource && (
            <Handle
              key={`${data.id}_source#${data.conditionSchema.length + 1}`}
              type="source"
              position={Position.Right}
              id={`${data.id}_source#${data.conditionSchema.length + 1}`}
              style={{
                height: 8,
                width: 8,
                top: `${58 + 34 * data.conditionSchema.length}px`,
                backgroundColor: theme === 'dark' ? 'white' : 'black',
              }}
            />
          )}
          {data.conditionSchema && data.conditionSchema.length && data.goToBlockSource && data.goToBlockSource.length && (() => {
            const key = `${data.id}_source#${data.conditionSchema.length + 1}`
            const goToBlock = data.goToBlockSource.find(v => v.sourceHandle === key)
            if (goToBlock) {
              return <GoToBlockHandle
                data={data}
                sourceHandle={key}
                style={{
                  height: 14,
                  width: 84,
                  top: `${58 + 34 * data.conditionSchema.length}px`,
                  right: -80,
                  backgroundColor: theme === 'dark' ? 'white' : 'black',
                  fontSize: 10,
                  color: theme === 'dark' ? 'black' : 'white',
                  border: 'none',
                  borderRadius: 10,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              />
            } else {
              return <Handle
                key={key}
                type="source"
                position={Position.Right}
                id={key}
                style={{
                  height: 8,
                  width: 8,
                  top: `${58 + 34 * data.conditionSchema.length}px`,
                  backgroundColor: theme === 'dark' ? 'white' : 'black',
                }}
              />
            }
          })()}
        </div>
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-col space-y-4 mt-6"
        >
          <div className="flex flex-col space-y-4">
            {conditionFields.map((field, index) => (
              <div key={field.id} className="flex flex-col space-y-3">
                {index > 0 && <Divider className="!border-primary-border dark:!border-dark-primary-border"/>}
                <div className="w-full flex flex-col items-stretch space-y-3">
                  <div className="flex items-center justify-between">
                    <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                      {index === 0 ? "IF CONDITION" : "ELSE IF CONDITION"}
                    </Typography.Text>
                    {index > 0 && status && status === STUDIO_STATUS.DEV && (
                      <div
                        className="hover:cursor-pointer"
                        onClick={() => handleDeleteCondition(index)}
                      >
                        <Icon
                          iconName={"RiDeleteBinLine"}
                          size={24}
                          style={{color: "#6F767E"}}
                        />
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col items-stretch space-y-3">
                    {conditionFormArray &&
                      conditionFormArray[index] &&
                      !conditionFormArray[index].use_text && (
                        <>
                          <InputField
                            style={{flexGrow: 1}}
                            type={"input"}
                            name={`condition[${index}].if_condition`}
                            control={control}
                            disabled={status && status === STUDIO_STATUS.LIVE}
                            setValue={setValue}
                          />
                          <SelectField
                            style={{width: "50%", color: "white !important"}}
                            name={`condition[${index}].comparison`}
                            control={control}
                            disabled={status && status === STUDIO_STATUS.LIVE}
                            options={COMPARISONS_TYPE_LIST}
                          />
                          <InputField
                            style={{flexGrow: 1}}
                            type={"input"}
                            name={`condition[${index}].value`}
                            control={control}
                            disabled={status && status === STUDIO_STATUS.LIVE}
                            setValue={setValue}
                          />
                        </>
                      )}
                    <div className="flex items-center space-x-2">
                      <CheckboxField
                        name={`condition[${index}].use_text`}
                        control={control}
                        disabled={status && status === STUDIO_STATUS.LIVE}
                      />
                      <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                        Use text
                      </Typography.Text>
                    </div>
                    {conditionFormArray &&
                      conditionFormArray[index] &&
                      conditionFormArray[index].use_text && (
                        <div className="flex flex-col space-y-2">
                          <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                            Condition
                          </Typography.Text>
                          <InputField
                            style={{flexGrow: 1}}
                            type={"input"}
                            name={`condition[${index}].condition_text`}
                            control={control}
                            disabled={status && status === STUDIO_STATUS.LIVE}
                            setValue={setValue}
                          />
                        </div>
                      )}
                    {/* <div>{JSON.stringify(conditionFormArray[index])}</div> */}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <Divider className="!border-primary-border dark:!border-dark-primary-border"/>

          <div
            className="flex gap-3 items-center hover:cursor-pointer"
            onClick={handleAddCondition}
          >
            <Icon
              iconName={"RiAddCircleFill"}
              size={18}
              style={{color: data.node_color}}
            />
            <div style={{color: data.node_color}}>Add another condition</div>
          </div>

          <Divider className="!border-primary-border dark:!border-dark-primary-border"/>

          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
              Output key <span style={{color: 'red'}}>*</span>
            </Typography.Text>
            <InputField
              type={"input"}
              name={`output_key`}
              control={control}
              setValue={setValue}
            />
          </div>

          <Divider className="!border-primary-border dark:!border-dark-primary-border"/>

          <div
            className="w-full flex justify-end items-center space-x-4"
          >
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() => setModalOpen(false)}
            >
              Cancel
            </StyledButtonModal>
            <StyledButtonModal
              type="submit"
              key="save"
              $bgColor={"#7F75CF"}
              $color={"white"}
              disabled={status && status === STUDIO_STATUS.LIVE}
            >
              Save
            </StyledButtonModal>
          </div>
        </form>
      </Modal>
    </StyledNodeFlow>
  );
};

export default IfElseNode;
