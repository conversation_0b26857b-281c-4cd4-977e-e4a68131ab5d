<div class="pr-3 h-full flex flex-col overflow-hidden">
  <div class="h-full flex flex-col flex-grow overflow-hidden" (clickOutside)="removeSelectedFolderOrFile()">
    <!-- Folders Section -->
    @if (folderList().length > 0) {
    <div class="flex flex-col items-stretch justify-start space-y-2"
      style="height: 40%; min-height: 200px; max-height: 40%">
      <div class="w-full px-5 h-14 flex items-center">
        <div class="text-lg text-light-text dark:text-dark-text">
          Folders ({{ folderList().length }})
        </div>
        <button (click)="refreshVirtualScrollViewports()"
          class="ml-2 text-sm text-light-text dark:text-dark-text hover:text-blue-500">
          Refresh
        </button>
      </div>
      <cdk-virtual-scroll-viewport #folderViewport id="folderViewport" itemSize="50" minBufferPx="200" maxBufferPx="400"
        style="height: calc(100% - 56px); overflow: auto" class="h-full">
        <div
          class="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 pl-5 file-folder-list-wrapper">
          <ng-container *cdkVirtualFor="
              let folder of folderList();
              let i = index;
              trackBy: trackByFolderId
            ">
            <div (dblclick)="onFolderDoubleClick(folder)" (click)="onFolderClick(folder)" [ngClass]="
                folderSelection().isSelected(folder)
                  ? 'bg-light-gray dark:bg-dark-gray text-[#39207f]'
                  : 'hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover'
              " [attr.data-folder-id]="folder.id"
              class="col-span-1 w-full rounded-2xl bg-light-gray dark:bg-dark-gray flex flex-col items-stretch folder-item">
              <div class="flex flex-grow-0 flex-shrink-0 basis-14 items-center file-folder-wrapper">
                <div class="w-[50px] flex items-center justify-center">
                  <svg width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg"
                    (dblclick)="
                  $event.stopPropagation();
                  onFolderDoubleClick(folder)
                ">
                    <path
                      d="M3 18H17C18.6569 18 20 16.6569 20 15V5C20 3.34315 18.6569 2 17 2H9.41421C9.149 2 8.89464 1.89464 8.70711 1.70711L7.87868 0.87868C7.31607 0.316071 6.55301 0 5.75736 0H3C1.34315 0 0 1.34315 0 3V15C0 16.6569 1.34315 18 3 18Z"
                      fill="#DAB674" />
                  </svg>
                </div>
                <div class="w-[calc(100%-56px)] overflow-hidden text-left flex flex-col">
                  <div class="truncate folder-name" (dblclick)="
                      $event.stopPropagation();
                      onFolderDoubleClick(folder)
                    " [dxTooltip]="folder?.name ?? ''" dxTooltipPosition="above" [ngClass]="
                      folderSelection().isSelected(folder)
                        ? 'text-[#c2e7ff]'
                        : 'text-light-text dark:text-dark-text'
                    ">
                    {{ folder.name }}
                  </div>
                </div>
                <div class="flex items-center">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                    (click)="$event.stopPropagation()" [matMenuTriggerFor]="folderMenu"
                    class="hover:cursor-pointer menu-trigger">
                    <path
                      d="M8 12C8 13.1046 7.10457 14 6 14C4.89543 14 4 13.1046 4 12C4 10.8954 4.89543 10 6 10C7.10457 10 8 10.8954 8 12Z"
                      fill="#6F767E" />
                    <path
                      d="M14 12C14 13.1046 13.1046 14 12 14C10.8954 14 10 13.1046 10 12C10 10.8954 10.8954 10 12 10C13.1046 10 14 10.8954 14 12Z"
                      fill="#6F767E" />
                    <path
                      d="M20 12C20 13.1046 19.1046 14 18 14C16.8954 14 16 13.1046 16 12C16 10.8954 16.8954 10 18 10C19.1046 10 20 10.8954 20 12Z"
                      fill="#6F767E" />
                  </svg>
                  <mat-menu #folderMenu="matMenu" xPosition="after" class="my-menu">
                    <button mat-menu-item (click)="onFolderRename(folder)"
                      class="!h-10 text-light-text dark:text-dark-text">
                      <div class="flex items-center">
                        <ng-icon name="heroPencilSquare" class="text-orange-500 text-2xl mr-3"></ng-icon>
                        <span class="text-light-text dark:text-dark-text">Rename</span>
                      </div>
                    </button>
                    <button mat-menu-item (click)="onFolderDelete(folder)"
                      class="!h-10 text-light-text dark:text-dark-text">
                      <div class="flex items-center">
                        <ng-icon name="heroTrash" class="text-red-500 text-2xl mr-3"></ng-icon>
                        <span class="text-light-text dark:text-dark-text">Delete</span>
                      </div>
                    </button>
                  </mat-menu>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </cdk-virtual-scroll-viewport>
    </div>
    }

    <!-- Files Section -->
    @if (fileList().length > 0) {
    <div class="flex flex-col items-stretch justify-start space-y-2 mt-4 flex-grow"
      style="height: 60%; min-height: 200px">
      <div class="w-full px-5 h-14 flex items-center">
        <div class="text-lg text-light-text dark:text-dark-text">
          Files ({{ fileList().length }})
        </div>
      </div>
      <cdk-virtual-scroll-viewport #fileViewport id="fileViewport" itemSize="50" minBufferPx="200" maxBufferPx="400"
        style="height: calc(100% - 56px); overflow: auto" class="h-full">
        <div
          class="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 pl-5 file-folder-list-wrapper">
          <ng-container *cdkVirtualFor="
              let file of fileList();
              trackBy: trackByFileId
            ">
            <div (dblclick)="onFileDoubleClick(file)" (click)="onFileClick(file)" [ngClass]="
                fileSelection().isSelected(file)
                  ? 'bg-light-primary dark:bg-dark-primary text-white'
                  : 'opacity-80 hover:cursor-pointer hover:opacity-100 bg-light-hover dark:bg-dark-hover'
              " draggable="true" [attr.data-file-id]="file.id"
              class="col-span-1 h-52 md:h-52 lg:h-56 xl:h-52 2xl:h-56 w-full rounded-2xl flex flex-col items-stretch mb-2">
              <div class="flex flex-grow-0 flex-shrink-0 basis-12 items-center file-folder-wrapper">
                <!-- File Status Icon -->
                @if (file.status === 'COMPLETED') {
                <div class="w-[50px] flex items-center justify-center">
                  <ng-icon [name]="getFileIcon(file)" [style.color]="getFileTypeColor(file)" class="text-xl"></ng-icon>
                </div>
                }
                @if (file.status === 'IN_PROGRESS') {
                <div class="w-[50px] flex items-center justify-center">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                </div>
                }
                @if (file.status === 'ERROR') {
                <div class="w-[50px] flex items-center justify-center">
                  <ng-icon name="heroXMark" class="text-red-500 text-xl"></ng-icon>
                </div>
                }
                @if (file.status === 'PENDING') {
                <div class="w-[50px] flex items-center justify-center">
                  <ng-icon name="heroEllipsisHorizontal" class="text-yellow-400 text-xl"></ng-icon>
                </div>
                }

                <div class="w-[calc(100%-56px)] overflow-hidden text-left flex flex-col">
                  <div class="truncate" [dxTooltip]="file?.name ?? ''" dxTooltipPosition="above" [ngClass]="
                      fileSelection().isSelected(file)
                        ? 'text-[#c2e7ff]'
                        : 'text-light-text dark:text-dark-text'
                    ">
                    {{ file.name }}
                  </div>
                </div>
                <div class="flex items-center pr-3">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                    (click)="$event.stopPropagation()" [matMenuTriggerFor]="fileMenu"
                    class="hover:cursor-pointer menu-trigger">
                    <path
                      d="M8 12C8 13.1046 7.10457 14 6 14C4.89543 14 4 13.1046 4 12C4 10.8954 4.89543 10 6 10C7.10457 10 8 10.8954 8 12Z"
                      fill="#6F767E" />
                    <path
                      d="M14 12C14 13.1046 13.1046 14 12 14C10.8954 14 10 13.1046 10 12C10 10.8954 10.8954 10 12 10C13.1046 10 14 10.8954 14 12Z"
                      fill="#6F767E" />
                    <path
                      d="M20 12C20 13.1046 19.1046 14 18 14C16.8954 14 16 13.1046 16 12C16 10.8954 16.8954 10 18 10C19.1046 10 20 10.8954 20 12Z"
                      fill="#6F767E" />
                  </svg>
                  <mat-menu #fileMenu="matMenu" xPosition="after"
                    class="my-menu bg-light-background dark:bg-dark-background">
                    <button mat-menu-item (click)="onFileInfo(file)" class="!h-10 text-light-text dark:text-dark-text">
                      <div class="flex items-center">
                        <ng-icon name="heroInformationCircle" class="text-blue-500 text-2xl mr-3"></ng-icon>
                        <span class="text-light-text dark:text-dark-text">Info</span>
                      </div>
                    </button>
                    <button mat-menu-item (click)="onFileRename(file)"
                      class="!h-10 text-light-text dark:text-dark-text">
                      <div class="flex items-center">
                        <ng-icon name="heroPencilSquare" class="text-orange-500 text-2xl mr-3"></ng-icon>
                        <span class="text-light-text dark:text-dark-text">Rename</span>
                      </div>
                    </button>
                    <button mat-menu-item (click)="onFileMove(file)" class="!h-10 text-light-text dark:text-dark-text">
                      <div class="flex items-center">
                        <svg class="text-blue-500 mr-3" width="24px" height="24px" viewBox="0 0 24 24" focusable="false"
                          fill="#3B82F6">
                          <path fill="none" d="M0 0h24v24H0V0z"></path>
                          <path
                            d="M20 6h-8l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 12H4V8h16v10zm-8.01-9l-1.41 1.41L12.16 12H8v2h4.16l-1.59 1.59L11.99 17 16 13.01 11.99 9z">
                          </path>
                        </svg>
                        <span class="text-light-text dark:text-dark-text">Move</span>
                      </div>
                    </button>
                    <button mat-menu-item (click)="onFileDelete(file)"
                      class="!h-10 text-light-text dark:text-dark-text">
                      <div class="flex items-center">
                        <ng-icon name="heroTrash" class="text-red-500 text-2xl mr-3"></ng-icon>
                        <span class="text-light-text dark:text-dark-text">Delete</span>
                      </div>
                    </button>
                  </mat-menu>
                </div>
              </div>

              <!-- File Details -->
              <div class="flex-grow p-3 pt-0">
                <div class="text-xs text-light-text dark:text-dark-text opacity-70 mb-1">
                  {{ file.ext || 'Unknown' }}
                </div>
                @if (file.size) {
                <div class="text-xs text-light-text dark:text-dark-text opacity-70 mb-1">
                  {{ formatFileSize(file.size) }}
                </div>
                }
                <div class="text-xs px-2 py-1 rounded-full inline-block"
                  [style.background-color]="getStatusColor(file.status) + '20'"
                  [style.color]="getStatusColor(file.status)">
                  {{ file.status }}
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </cdk-virtual-scroll-viewport>
    </div>
    }
  </div>
</div>