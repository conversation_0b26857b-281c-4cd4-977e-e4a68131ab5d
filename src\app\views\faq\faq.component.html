<div class="h-full flex flex-col overflow-hidden">
  <div
    class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
  >
    Frequently Asked Questions
  </div>
  <div
    class="mt-6 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
  >
    <div class="flex flex-wrap items-center justify-between">
      <div class="flex items-center justify-between space-x-4 flex-wrap">
        <!-- Search Input -->
        <div class="w-full md:w-96">
          <dx-form-field
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <app-svg-icon
              dxPrefix
              type="icSearch"
              class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
            ></app-svg-icon>
            <input
              dxInput
              [(ngModel)]="searchModel.key_word"
              (ngModelChange)="searchSubject.next($event)"
              [type]="'text'"
              placeholder="Search by Question/Response"
            />
          </dx-form-field>
        </div>

        <!-- Status Filter -->
        <div class="w-full md:w-64">
          <dx-form-field
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <dx-select
              [(ngModel)]="selectedStatus"
              (ngModelChange)="onStatusChange()"
            >
              @for (status of statusOptions; track $index) {
              <dx-option [value]="status.value">{{ status.label }}</dx-option>
              }
            </dx-select>
          </dx-form-field>
        </div>
      </div>

      <!-- Add FAQ Button -->
      <div class="flex items-center justify-end">
        <button dxButton="filled" (click)="openFAQDialog()" class="px-4 py-2">
          <div class="flex items-center justify-between space-x-2">
            <!--            <ng-icon name="heroPlus" class="text-2xl"></ng-icon>-->
            <div class="text-sm font-medium">Add FAQ</div>
          </div>
        </button>
      </div>
    </div>

    <div class="flex-1 flex overflow-hidden">
      <app-data-table
        class="w-full"
        [rows]="listFAQ"
        [columns]="columns"
        [pageIndex]="pageIndex"
        [limit]="searchModel.pageSize"
        [count]="count"
        [rowTemplate]="rowTemplate"
        [actionTemplate]="actionTemplate"
        (pageChange)="changePage($event)"
        (action)="onAction($event)"
      ></app-data-table>
    </div>
  </div>
</div>

<!-- Custom row template -->
<ng-template #rowTemplate let-row="row" let-column="column">
  @switch (column.columnDef) {
  <!-- Index column -->
  @case ('index') {
  <div class="flex items-center justify-center">
    <span>{{ getRowIndex(row) + 1 }}</span>
  </div>
  }
  <!-- Question column -->
  @case ('question') {
  <div
    class="truncate"
    [ngStyle]="{ 'max-width': column.maxWidth, 'min-width': column.minWidth }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  }
  <!-- Response column -->
  @case ('response') {
  <div
    class="truncate"
    [ngStyle]="{ 'max-width': column.maxWidth, 'min-width': column.minWidth }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  }
  <!-- Sources column -->
  @case ('files') {
  <div
    class="flex flex-wrap gap-1"
    [ngStyle]="{ 'max-width': column.maxWidth, 'min-width': column.minWidth }"
  >
    @if (row[column.columnDef]?.length > 0) { @for (sourceId of
    row[column.columnDef]; track sourceId; let i = $index; let last = $last) {
    <div
      class="tag px-4 py-1 rounded-xl text-xs"
      [ngStyle]="{
        'background-color': getRandomColor(sourceId),
        color: getTextColor(getRandomColor(sourceId))
      }"
      [dxTooltip]="knowledgeBaseTooltip(getElementById(sourceId))"
      dxTooltipPosition="below"
    >
      {{ getElementById(sourceId)?.name | titlecase }}
    </div>
    } } @else {
    <span class="text-gray-500 text-xs italic">No sources</span>
    }
    <ng-template #noSources>
      <span class="text-gray-500 text-xs italic">No sources</span>
    </ng-template>
  </div>
  }
  <!-- Status column -->
  @case ('faq_status') { @switch (row[column.columnDef]) { @case ('REVIEW') {
  <p
    class="px-4 bg-info text-info-content rounded-full"
    [dxTooltip]="'Status: Review'"
    dxTooltipPosition="above"
  >
    Review
  </p>
  } @case ('NOT_READY') {
  <p
    class="px-4 bg-warning text-warning-content rounded-full"
    [dxTooltip]="'Status: Not Ready'"
    dxTooltipPosition="above"
  >
    Not Ready
  </p>
  } @default {
  <p
    class="px-4 bg-success text-success-content rounded-full"
    [dxTooltip]="'Status: Ready'"
    dxTooltipPosition="above"
  >
    Ready
  </p>
  } } }
  <!-- Status note column -->
  @case ('status_note') {
  <div
    class="truncate"
    [ngStyle]="{ 'max-width': column.maxWidth, 'min-width': column.minWidth }"
    [dxTooltip]="row[column.columnDef] || ''"
    dxTooltipPosition="below"
  >
    <p class="px-4 bg-warning text-warning-content rounded-full">
      {{ row[column.columnDef] ? "Has been changed" : "" }}
    </p>
  </div>
  }
  <!-- Default for any other columns -->
  @default {
  <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
    {{ row[column.columnDef] }}
  </div>
  } }
</ng-template>

<!-- Custom action template -->
<ng-template #actionTemplate let-row>
  <div class="flex justify-center items-center">
    <button
      class="flex cursor-pointer hover:opacity-80"
      (click)="row.isActions = !row.isActions"
      cdkOverlayOrigin
      #trigger="cdkOverlayOrigin"
    >
      <ng-icon
        name="heroEllipsisHorizontal"
        size="24"
        class="flex items-center justify-center"
      ></ng-icon>
    </button>
    <ng-template
      cdkConnectedOverlay
      [cdkConnectedOverlayOrigin]="trigger"
      [cdkConnectedOverlayOpen]="row.isActions"
      [cdkConnectedOverlayPush]="true"
      [cdkConnectedOverlayPositions]="[
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'top',
          offsetY: 10,
        },
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'bottom',
          offsetY: 10,
        },
      ]"
    >
      <ul
        class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
        (clickOutside)="row.isActions = false; row.isContextMenu = false"
      >
        <li
          class="border-b border-primary-border dark:border-dark-primary-border"
        >
          <button
            (click)="
              $event.stopPropagation();
              handleAction('changeStatus', row);
              row.isActions = false
            "
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg"
            [ngClass]="[
              row.faq_status === 'NOT_READY'
                ? '!text-success dark:!text-dark-success'
                : '!text-error dark:!text-dark-error'
            ]"
            title="{{
              row.faq_status === 'NOT_READY'
                ? 'Change status to ready'
                : 'Change status to not ready'
            }}"
          >
            <ng-icon
              [name]="
                row.faq_status === 'NOT_READY' ? 'heroCheck' : 'heroXMark'
              "
              class="text-2xl flex items-center justify-center"
              [style.color]="
                row.faq_status === 'NOT_READY' ? '#4CAF50' : '#FF9800'
              "
            ></ng-icon>
            <div
              class="flex items-center justify-between text-[16px] font-medium"
            >
              {{ row.faq_status === "NOT_READY" ? "Ready" : "Not Ready" }}
            </div>
          </button>
        </li>
        <li>
          <button
            (click)="
              $event.stopPropagation();
              handleAction('edit', row);
              row.isActions = false
            "
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg"
            title="Edit FAQ"
          >
            <ng-icon
              name="heroPencilSquare"
              class="text-2xl flex items-center justify-center"
            ></ng-icon>
            <div
              class="flex items-center justify-between text-[16px] font-medium"
            >
              Edit
            </div>
          </button>
        </li>
        <li>
          <button
            (click)="
              $event.stopPropagation();
              handleAction('delete', row);
              row.isActions = false
            "
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg"
            title="Delete FAQ"
          >
            <ng-icon
              name="heroTrash"
              class="text-2xl flex items-center justify-center"
            ></ng-icon>
            <div
              class="flex items-center justify-between text-[16px] font-medium"
            >
              Delete
            </div>
          </button>
        </li>
      </ul>
    </ng-template>
  </div>
</ng-template>
