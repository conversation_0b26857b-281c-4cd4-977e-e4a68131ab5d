import { CommonModule } from '@angular/common';
import { Component, effect, inject } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { STUDIO_STATUS, STUDIO_STATUS_TYPE } from '@core/constants';
import { StudioStore } from '@core/stores';
import {
  DxFormField,
  DxOption,
  DxPrefix,
  DxSelect,
  DxSnackBar,
} from '@dx-ui/ui';

@Component({
  selector: 'app-flow-env-select',
  templateUrl: './flow-env-select.component.html',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DxFormField,
    DxPrefix,
    DxSelect,
    DxOption,
  ],
})
export class FlowEnvSelectComponent {
  studioStatus = new FormControl<STUDIO_STATUS_TYPE | null>(null);

  studioStore = inject(StudioStore);
  private snackbar = inject(DxSnackBar);

  readonly STUDIO_STATUS = STUDIO_STATUS;

  constructor() {
    effect(() => {
      const status = this.studioStore.statusComputed();
      this.studioStatus.setValue(status);
    });
  }

  selectFlowEnv(status: STUDIO_STATUS_TYPE) {
    if (this.studioStore.status() !== status) {
      this.snackbar.open('Studio environment changed', '', {
        duration: 2000,
        horizontalPosition: 'right',
        verticalPosition: 'top',
        panelClass: 'dx-snack-bar-success',
      });
    }
    this.studioStore.setStudioStatus(status);
    document.getElementById('dropdownSelectFlowEnvButton')?.click();
  }
}
