<div>
  <button
    class="bg-light-primary h-[40px] text-white px-6 rounded-lg w-full min-w-24"
    (click)="onOpenExportOptionsChange(!openExportOptions)"
    cdkOverlayOrigin
    #exportOptions="cdkOverlayOrigin"
  >
    <p>Export</p>
  </button>
  <ng-template
    cdkConnectedOverlay
    [cdkConnectedOverlayOrigin]="exportOptions"
    [cdkConnectedOverlayOpen]="openExportOptions"
    [cdkConnectedOverlayWidth]="
      exportOptions.elementRef.nativeElement.offsetWidth
    "
    [cdkConnectedOverlayPositions]="[
      {
        originX: 'center',
        originY: 'bottom',
        overlayX: 'center',
        overlayY: 'top',
        offsetY: 5
      }
    ]"
  >
    <ul
      class="bg-light-white w-full p-3 border rounded-lg shadow-sm flex flex-col space-y-3"
      (clickOutside)="onClickOutside()"
    >
      <li
        class="w-full p-2 cursor-pointer rounded-lg hover:bg-gray-300"
        (click)="onExportUsingS3()"
      >
        AWS S3
      </li>
      <li class="w-full p-2 rounded-lg text-gray-300">SFTP</li>
    </ul>
  </ng-template>
</div> 