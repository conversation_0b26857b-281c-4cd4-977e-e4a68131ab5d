import {
  AfterViewInit,
  Component,
  computed, effect,
  ElementRef,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  Renderer2,
  signal,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DxFormField, DxInput, DxLabel, DxSnackBar } from '@dx-ui/ui';
import { environment } from '@env/environment';
import { NgIconsModule } from '@ng-icons/core';
import { TrimStringDirective } from '@shared/directives';
import hljs from 'highlight.js/lib/core';
import xml from 'highlight.js/lib/languages/xml';
import {UIStore} from '@core/stores';
import {NgClass} from '@angular/common';

hljs.registerLanguage('html', xml);
@Component({
  selector: 'app-embed-form',
  standalone: true,
  imports: [
    FormsModule,
    DxInput,
    DxFormField,
    DxLabel,
    NgIconsModule,
    TrimStringDirective,
    NgClass,
  ],
  templateUrl: './embed-form.component.html',
})
export class EmbedFormComponent implements OnInit, AfterViewInit {
  @Input() initialData!: any;
  @Input() aiId!: string;
  @Input() isEnabled!: boolean;
  @Output() configChange = new EventEmitter<any>();

  @ViewChild('previewContainer', { static: false })
  previewContainer!: ElementRef;

  private renderer = inject(Renderer2);
  private snackBar = inject(DxSnackBar);
  private uiStore = inject(UIStore);
  protected isHandset = computed(() => this.uiStore.isHandset());
  embedUrl = environment.EMBED_URL;

  bubble = signal({
    bgColor: '#7241FF',
    color: '#FFFFFF',
    // content: `${environment.EMBED_URL}/assets/img/bubble-chat.png`,
    content: `${environment.SERVER_URL}/assets/img/bubble-chat.png`,
    padding: '0px',
    borderRadius: '50%',
  });

  widget = signal({
    bgColor: '#FFFFFF',
    color: '#000000',
    contentImg: '',
    contentText: 'Nhân viên tư vấn',
    isReplyComment: false,
  });

  chatAssistant = signal({
    bgColor: '#F3F4F6',
    color: '#000000',
  });

  chatUser = signal({
    bgColor: '#7241FF',
    color: '#FFFFFF',
  });

  poweredBy = signal({
    text: 'DxGPT',
    link: 'https://dxconnect.lifesup.ai',
  });

  textScript = signal('');

  highlightedScript = computed(
    () => hljs.highlight(this.textScript(), { language: 'html' }).value
  );

  ngOnInit() {
    if (this.initialData) {
      // Merge with default values to ensure no undefined
      this.bubble.set({
        bgColor: this.initialData.bubble?.bgColor || '#7241FF',
        color: this.initialData.bubble?.color || '#FFFFFF',
        content:
          this.initialData.bubble?.content ||
          `${environment.EMBED_URL}/assets/img/bubble-chat.png`,
        padding: this.initialData.bubble?.padding || '0px',
        borderRadius: this.initialData.bubble?.borderRadius || '50%',
      });

      this.widget.set({
        bgColor: this.initialData.widget?.bgColor || '#FFFFFF',
        color: this.initialData.widget?.color || '#000000',
        contentImg: this.initialData.widget?.contentImg || '',
        contentText: this.initialData.widget?.contentText || 'Nhân viên tư vấn',
        isReplyComment: this.initialData.widget?.isReplyComment || false,
      });

      this.chatAssistant.set({
        bgColor: this.initialData.chatAssistant?.bgColor || '#F3F4F6',
        color: this.initialData.chatAssistant?.color || '#000000',
      });

      this.chatUser.set({
        bgColor: this.initialData.chatUser?.bgColor || '#7241FF',
        color: this.initialData.chatUser?.color || '#FFFFFF',
      });

      this.poweredBy.set({
        text: this.initialData.poweredBy?.text || 'DxGPT',
        link:
          this.initialData.poweredBy?.link || 'https://dxconnect.lifesup.ai',
      });
    }

    this.changeScript();
  }

  ngAfterViewInit() {
    this.renderPreviewIframe();
  }

  changeScript() {
    this.textScript.set(`
<script src="${this.embedUrl}/assets/loader/loader.min.js"
  data-link="${this.embedUrl}/"
  data-id="${this.aiId}"
  data-bubble-background-color="${this.bubble().bgColor || ''}"
  data-bubble-color="${this.bubble().color || ''}"
  data-bubble-content="${this.bubble().content || ''}"
  data-bubble-padding="${this.bubble().padding || ''}"
  data-bubble-border-radius="${this.bubble().borderRadius || ''}"
  data-widget-background-color="${this.widget().bgColor || ''}"
  data-widget-color="${this.widget().color || ''}"
  data-content-img="${this.widget().contentImg || ''}"
  data-content-text="${this.widget().contentText || ''}"
  data-assistant-background="${this.chatAssistant().bgColor || ''}"
  data-assistant-color="${this.chatAssistant().color || ''}"
  data-user-background="${this.chatUser().bgColor || ''}"
  data-user-color="${this.chatUser().color || ''}"
  data-powered-by="${this.poweredBy().text || ''}"
  data-powered-link="${this.poweredBy().link || ''}">
</script>`);

    this.configChange.emit({
      bubble: this.bubble(),
      widget: this.widget(),
      chatAssistant: this.chatAssistant(),
      chatUser: this.chatUser(),
      poweredBy: this.poweredBy(),
    });

    this.renderPreviewIframe();
  }

  async copyText(content: string) {
    try {
      await navigator.clipboard.writeText(content);
      this.snackBar.open('Copied successfully', '', {
        panelClass: 'dx-snack-bar-success',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    } catch {
      this.snackBar.open('Copy failed', '', {
        panelClass: 'dx-snack-bar-error',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }
  }

  renderPreviewIframe() {
    if (!this.previewContainer) return;

    // Xóa nội dung cũ
    this.previewContainer.nativeElement.innerHTML = '';

    // Tạo iframe
    const iframe: HTMLIFrameElement = this.renderer.createElement('iframe');
    iframe.style.width = '166%';
    iframe.style.height = '166%';
    iframe.style.border = 'none';
    iframe.style.backgroundColor = 'transparent';
    iframe.style.transform = 'scale(0.6)';
    iframe.style.transformOrigin = 'top left';

    // Điều chỉnh lại kích thước container để phù hợp scale
    this.previewContainer.nativeElement.style.overflow = 'hidden';
    this.renderer.appendChild(this.previewContainer.nativeElement, iframe);
    // Tạo nội dung HTML
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Demo Widget</title>
        <style>
          body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        </style>
      </head>
      <body>
        <script
          src="${this.embedUrl}/assets/loader/loader.min.js"
          data-link="${this.embedUrl}/"
          data-id="${this.aiId}"
          data-bubble-background-color="${this.bubble().bgColor || ''}"
          data-bubble-color="${this.bubble().color || ''}"
          data-bubble-content="${this.bubble().content || ''}"
          data-bubble-padding="${this.bubble().padding || ''}"
          data-bubble-border-radius="${this.bubble().borderRadius || ''}"
          data-widget-background-color="${this.widget().bgColor || ''}"
          data-widget-color="${this.widget().color || ''}"
          data-content-img="${this.widget().contentImg || ''}"
          data-content-text="${this.widget().contentText || ''}"
          data-assistant-background="${this.chatAssistant().bgColor || ''}"
          data-assistant-color="${this.chatAssistant().color || ''}"
          data-user-background="${this.chatUser().bgColor || ''}"
          data-user-color="${this.chatUser().color || ''}"
          data-powered-by="${this.poweredBy().text || ''}"
          data-powered-link="${this.poweredBy().link || ''}">
        </script>
      </body>
      </html>
    `;

    // Append iframe vào DOM trước
    this.renderer.appendChild(this.previewContainer.nativeElement, iframe);

    // Sử dụng onload event thay vì setTimeout
    iframe.onload = () => {
      const doc = iframe.contentDocument || iframe.contentWindow?.document;
      if (doc) {
        doc.open();
        doc.write(htmlContent);
        doc.close();
      }
    };

    // Fallback: Set src để trigger onload
    iframe.src = 'about:blank';
  }
}
