<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      FAQ
    </div>
    <div class="flex items-center justify-end">
      <ng-icon
        name="heroXMark"
        class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="closeDialog()"
      ></ng-icon>
    </div>
  </div>
  <div class="flex-1 overflow-auto mt-18 mb-20">
    <div [formGroup]="faqForm" class="p-6 flex flex-col space-x-4">
      <dx-form-field class="w-full" id="question">
        <dx-label class="text-sm">Question</dx-label>
        <input
          dx-input
          formControlName="question"
          [type]="'text'"
          placeholder="Question"
        />
        @if (faqForm.get('question')?.errors &&
        (faqForm.get('question')?.touched || faqForm.get('question')?.dirty)) {
        <dx-error>Question is required.</dx-error>
        }
      </dx-form-field>
      <dx-form-field class="w-full" id="response">
        <dx-label class="text-sm">Response</dx-label>
        <textarea
          dx-input
          formControlName="response"
          autoResize
          class="!resize-none"
          rows="2"
          [type]="'text'"
          placeholder="Response"
        ></textarea>
        @if (faqForm.get('response')?.errors &&
        (faqForm.get('response')?.touched || faqForm.get('response')?.dirty)) {
        <dx-error>Response is required.</dx-error>
        }
      </dx-form-field>

      <div class="w-full flex flex-col space-y-4">
        <dx-label class="text-sm">Source</dx-label>
        <button
          dxButton="elevated"
          class="w-full"
          (click)="addSource()"
          type="button"
        >
          Add source
        </button>
        @if (knowledgeBaseSelectedDraft.length > 0 || knowledgeBaseSelected.length > 0) {
        <div class="flex flex-1 overflow-auto flex-col space-y-3">
          @for (source of knowledgeBaseSelectedDraft; track source) {
          <div
            class="tag mx-1 cursor-pointer rounded-md px-4 py-1"
            [ngStyle]="{
              'max-width': 'calc(100% - 0px)',
              'background-color': getRandomColor(source),
              color: getTextColor(getRandomColor(source))
            }"
            [dxTooltip]="knowledgeBaseTooltip(getElementById(source))"
            dxTooltipPosition="below"
            (click)="addSource()"
          >
            <span>{{ getElementById(source)?.name }}</span>
          </div>
          }
        </div>
        }
      </div>
    </div>
  </div>

  <!-- Actions -->
  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="closeDialog()">Close</button>
    <button
      dxLoadingButton="filled"
      [loading]="isCreatingFAQ"
      [disabled]="this.faqForm.invalid"
      (click)="saveFAQ(data.faq.id)"
    >
      Save
    </button>
  </div>
</div>
