import {Component, Injector, OnInit} from '@angular/core';
import {BaseComponent} from "@core/base.component";
import {ActivatedRoute, Router} from "@angular/router";
import {SettingsService} from "../../shared/services/settings.service";

@Component({
  selector: 'app-invite-join',
  templateUrl: './invite-join.component.html',
  styleUrls: ['./invite-join.component.scss']
})
export class InviteJoinComponent extends BaseComponent implements OnInit {
  invitedSuccess: boolean | null = null;
  countdown: number = 5;

  constructor(
    private settingsService: SettingsService,
    private route: ActivatedRoute,
    private router: Router,
  ) {
    super();
  }

  ngOnInit(): void {
    // Extract the activation_id from the route parameters
    const inviteToken = this.route.snapshot.paramMap.get('invite_token');

    if (inviteToken) {
      this.settingsService.activeInvite(inviteToken).subscribe({
        next: (response) => {
          // Handle successful activation
          this.invitedSuccess = true;
          this.startCountdown();
        },
        error: (error) => {
          // Handle activation error
          console.error('Invited error', error);
          this.invitedSuccess = false;
        }
      });
    }
  }

  startCountdown() {
    const interval = setInterval(() => {
      this.countdown -= 1;
      if (this.countdown === 0) {
        clearInterval(interval);
        this.router.navigate(['/auth/sign-in']); // Redirect when countdown is 0
      }
    }, 1000); // Decrease countdown every second
  }
}
