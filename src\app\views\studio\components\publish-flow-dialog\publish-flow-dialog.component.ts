import { Component, inject, Input, OnInit, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { StudioStore } from '@core/stores';
import {
  DIALOG_DATA,
  DxButton,
  DxDialogRef,
  DxError,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxOption,
  DxSelect,
  DxSnackBar,
} from '@dx-ui/ui';
import { provideIcons } from '@ng-icons/core';
import { heroChevronDown } from '@ng-icons/heroicons/outline';
import { heroInformationCircleSolid } from '@ng-icons/heroicons/solid';
import { FLOW_STATUS } from '@shared/app.constant';
import {
  CustomIconComponent,
  SelectOption,
  SvgIconComponent,
} from '@shared/components';
import { IFlowDev, IFlowDevFilter } from '@shared/models';
import { FlowDevService, FlowVersionService } from '@shared/services';
import { CustomValidators } from '@shared/validators';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-publish-flow-dialog',
  imports: [
    ReactiveFormsModule,
    CustomIconComponent,
    SvgIconComponent,
    DxButton,
    DxLoadingButton,
    DxError,
    DxFormField,
    DxInput,
    DxLabel,
    DxOption,
    DxSelect,
  ],
  providers: [
    provideIcons({
      heroInformationCircleSolid,
      heroChevronDown,
    }),
  ],
  templateUrl: './publish-flow-dialog.component.html',
  styleUrl: './publish-flow-dialog.component.css',
})
export class PublishFlowDialogComponent implements OnInit {
  @Input() isMultiple: boolean = false;
  @Input() flowId!: number;
  @Input() trigger_type?: string;

  listFlowDev: IFlowDev[] = [];
  listFlowDevSelect: SelectOption[] = [];
  showStatusSelection = true;
  isPublishing = false;

  readonly STATUS_LIST = [
    {
      value: FLOW_STATUS.INACTIVE,
      label: 'Inactive',
    },
    {
      value: FLOW_STATUS.ACTIVE,
      label: 'Active',
    },
  ];
  protected readonly Boolean = Boolean;
  isDropdownFlowDevOpen: boolean = false;
  isStatusDropdownOpen: boolean = false;
  selectedFlowDev = signal<IFlowDev | null>(null);
  selectedStatus = signal<FLOW_STATUS | null>(null);

  formGroup: FormGroup = new FormGroup({});
  fb = inject(FormBuilder);
  private dialogRef = inject(DxDialogRef<PublishFlowDialogComponent>);
  private data: {
    isMultiple: boolean;
    flow_id: number;
    trigger_type: string;
  } = inject(DIALOG_DATA);
  private snackBar = inject(DxSnackBar);
  private studioStore = inject(StudioStore);
  private flowDevService: FlowDevService = inject(FlowDevService);
  private flowVersionService: FlowVersionService = inject(FlowVersionService);

  ngOnInit(): void {
    this.formGroup = this.fb.group({
      id: [null],
      flow_dev_ids: [[], Validators.required],
      version_name: [
        null,
        [Validators.required, CustomValidators.noWhitespaceValidator()],
      ],
      status: [FLOW_STATUS.INACTIVE, Validators.required],
    });

    if (this.data && this.data.flow_id) {
      this.flowId = this.data.flow_id;
    }
    if (this.data && this.data.isMultiple) {
      this.isMultiple = this.data.isMultiple;
    }
    if (this.data && this.data.trigger_type) {
      this.trigger_type = this.data.trigger_type;
    }

    this.getListFlowDev({ trigger_type: this.trigger_type });
    this.detailFlowDev(this.flowId);
    if (!this.isMultiple && this.flowId) {
      this.formGroup.patchValue({
        flow_dev_ids: this.flowId,
      });
    }
  }

  publishFlow() {
    this.formGroup.markAllAsTouched();
    if (this.formGroup.invalid) return;
    if (this.formGroup.value.flow_dev_ids.length === 0) return;

    const flow_dev_ids = [this.formGroup.value.flow_dev_ids];

    const publishFlow$ = flow_dev_ids.filter(Boolean).map((flow_dev_id) =>
      this.flowVersionService.publishFlow({
        ...this.formGroup.value,
        flow_dev_id,
      })
    );

    if (publishFlow$ && publishFlow$.length > 0) {
      this.isPublishing = true;
      forkJoin(publishFlow$).subscribe({
        next: () => {
          this.showSnackBar('Publish flow successfully', 'success');
          this.isPublishing = false;
          this.close();
        },
        error: () => (this.isPublishing = false),
        complete: () => (this.isPublishing = false),
      });
    }
  }

  close() {
    this.dialogRef.close();
  }

  private getListFlowDev(body: IFlowDevFilter) {
    this.flowDevService.getListFlowDev(body).subscribe({
      next: (res = []) => {
        const hasData = res.length > 0;

        this.listFlowDev = hasData ? res : [];

        if (hasData) {
          this.listFlowDev.forEach((value) => {
            if (value && value.id) {
              this.listFlowDevSelect.push({
                label: value.name,
                value: value.id,
              });
            }
          });
        }
        if (!hasData) {
          this.formGroup.reset();
          this.showSnackBar(
            'The current flow cannot be published for some reason',
            'error'
          );
          this.close();
        }
      },
      error: () => {
        this.listFlowDev = [];
        this.formGroup.reset();
        this.showSnackBar('Failed to load flow list', 'error');
        this.close();
      },
    });
  }

  private detailFlowDev(flowId: number) {
    if (!flowId) {
      this.showSnackBar(
        'The current flow cannot be published for some reason',
        'error'
      );
      this.formGroup.reset();
      this.close();
      return;
    }

    this.flowDevService.getDetailFlowDev(flowId).subscribe({
      next: (res) => {
        if (res.flow_versions.length === 0) {
          this.formGroup.get('status')?.setValue(FLOW_STATUS.ACTIVE);
          this.showStatusSelection = false;
        }
        if (this.trigger_type === 'EVENT') {
          if (!res.event_id) {
            this.formGroup.reset();
            this.showSnackBar(
              'The flow currently has no event for publication',
              'error'
            );
            this.close();
          }
        } else {
          if (!res.is_start && res.name !== 'Fallback' && !res.intent_dev_id) {
            this.formGroup.reset();
            this.showSnackBar(
              'The flow currently has no intent for publication',
              'error'
            );
            this.close();
          }
        }
        const version_name = res.flow_versions
          .filter((flv) => flv.status === 'ACTIVE')
          .map((flv) => flv.version_name)
          .join(', ');
        this.formGroup.get('version_name')?.setValue(version_name);
      },
    });
  }

  private showSnackBar(message: string, type: 'success' | 'error' | 'info') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }
}
