import { Platform } from '@angular/cdk/platform';
import { CommonModule } from '@angular/common';
import {
  Component,
  effect,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { TRIGGER_KEYS } from '@core/constants';
import { TriggerService } from '@core/services';
import {
  DxFormField,
  DxInput,
  DxLabel,
  DxPrefix,
  DxSnackBar,
  DxSuffix,
  DxTooltip,
} from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { heroInformationCircle } from '@ng-icons/heroicons/outline';
import { heroXCircleSolid } from '@ng-icons/heroicons/solid';
import { SvgIconComponent } from '@shared/components';
import { SettingsService } from '@shared/services';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  Subject,
  takeUntil,
} from 'rxjs';

@Component({
  selector: 'app-widget-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DxTooltip,
    DxFormField,
    DxPrefix,
    DxSuffix,
    DxLabel,
    DxInput,
    SvgIconComponent,
    NgIconsModule,
  ],
  templateUrl: './widget-settings.component.html',
  providers: [provideIcons({ heroInformationCircle, heroXCircleSolid })],
})
export class WidgetSettingsComponent implements OnInit, OnDestroy {
  settings = input<any>();

  private destroy$ = new Subject<void>();

  private platform = inject(Platform);
  private snackBar = inject(DxSnackBar);
  private settingsService = inject(SettingsService);
  private triggerService = inject(TriggerService);

  // FORM CONTROL
  logo_url = new FormControl<string>('');
  header = new FormControl<string>('');
  welcome_message = new FormControl<string>('');
  input_placeholder = new FormControl<string>('');
  whitelist = new FormControl<string[]>([]);
  whitelistInput = new FormControl<string>('');

  constructor() {
    effect(() => {
      const currentSettings = this.settings();
      if (currentSettings) {
        this.updateSettingsFormControls(currentSettings);
      }
    });
  }

  ngOnInit(): void {
    this.initializeFormSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  updateTextAreaHeight(id: string) {
    if (this.platform.isBrowser) {
      const textarea = document.getElementById(id);
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 32 + 'px';
      }
    }
  }

  addWhitelistUrl() {
    const value = this.whitelistInput.value?.trim();
    if (value) {
      const match = value.match(/^(?:https?:\/\/)?(?:www\.)?([^\/]+)/i);
      const domain = match ? match[1] : '';
      if (domain) {
        const currentValue = [...(this.whitelist.value as string[])];
        if (!currentValue.includes(domain)) {
          this.whitelist.setValue([...currentValue, domain]);
          this.whitelistInput.setValue('');
        } else {
          this.snackBar.open('Domain already exists in whitelist', '', {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        }
      } else {
        this.snackBar.open('Invalid URL', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      }
    }
  }

  removeWhitelistUrl(index: number) {
    const currentValue = [...(this.whitelist.value as string[])];
    currentValue.splice(index, 1);
    this.whitelist.setValue(currentValue);
  }

  private updateSettingsFormControls(settings: any) {
    this.logo_url.setValue(settings?.settings?.widget?.logo_url, {
      emitEvent: false,
    });
    this.header.setValue(settings?.settings?.widget?.header, {
      emitEvent: false,
    });
    this.welcome_message.setValue(settings?.settings?.widget?.welcome_message, {
      emitEvent: false,
    });
    this.input_placeholder.setValue(
      settings?.settings?.widget?.input_placeholder,
      { emitEvent: false }
    );
    this.whitelist.setValue(settings?.settings?.widget?.whitelist ?? [], {
      emitEvent: false,
    });
  }

  private subscribeToFormControlWithHandler(
    control: FormControl,
    handler: (value: any) => void,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    let stream = control.valueChanges.pipe(
      takeUntil(this.destroy$),
      distinctUntilChanged(),
      debounceTime(300)
    );

    if (shouldFilter) {
      stream = stream.pipe(filter(shouldFilter));
    }

    stream.subscribe({ next: handler });
  }

  private subscribeToFormControlSettings(
    control: FormControl,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    this.subscribeToFormControlWithHandler(
      control,
      this.buildUpdatedSettings.bind(this),
      shouldFilter
    );
  }

  private initializeFormSubscriptions() {
    this.subscribeToFormControlSettings(
      this.logo_url,
      (value: string) => !!value
    );
    this.subscribeToFormControlSettings(this.header);
    this.subscribeToFormControlSettings(this.welcome_message);
    this.subscribeToFormControlSettings(this.input_placeholder);
    this.subscribeToFormControlSettings(this.whitelist);
  }

  private buildUpdatedSettings(): void {
    const currentSettings = this.settings()?.settings;
    const updatedSettings = {
      ...currentSettings,
      widget: {
        logo_url: this.logo_url.value,
        header: this.header.value,
        welcome_message: this.welcome_message.value,
        input_placeholder: this.input_placeholder.value,
        whitelist: this.whitelist.value,
      },
    };
    this.updateSettings(updatedSettings);
  }

  private updateSettings(data: any) {
    this.settingsService.updateSetting(data).subscribe({
      next: (res) => {
        if (res) {
          this.snackBar.open(res.message, '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
          this.triggerService.trigger(
            TRIGGER_KEYS.RE_CALL_SETTINGS,
            this.triggerService.get(TRIGGER_KEYS.RE_CALL_SETTINGS)() === null
              ? 1
              : this.triggerService.get(TRIGGER_KEYS.RE_CALL_SETTINGS)() + 1
          );
        }
      },
      error: (err) => {
        this.snackBar.open(err.error.detail, '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
    });
  }
}
