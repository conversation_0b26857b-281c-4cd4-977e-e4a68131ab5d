import { Routes } from '@angular/router';
import { APP_ROUTES } from '@core/constants';
import { mobileGuard, noAuthGuard } from '@core/guards';
import { authGuard } from '@core/guards/auth.guard';
import { roleAuthGuard } from '@core/guards/role-auth.guard';
import { AuthLayoutComponent, MainLayoutComponent } from '@layouts/index';
import { DashboardComponent } from '@views/dashboard/dashboard.component';

export const routes: Routes = [
  {
    path: '',
    component: MainLayoutComponent,
    canActivate: [authGuard],
    children: [
      {
        path: APP_ROUTES.DASHBOARD,
        canActivate: [roleAuthGuard],
        component: DashboardComponent,
      },
      {
        path: APP_ROUTES.STUDIO,
        loadChildren: () =>
          import('./views/studio/studio.routes').then((m) => m.STUDIO_ROUTES),
      },
      {
        path: APP_ROUTES.INBOX,
        canActivate: [roleAuthGuard],
        loadComponent: () =>
          import('./views/inbox/inbox.component').then((m) => m.InboxComponent),
      },
      {
        path: APP_ROUTES.KNOWLEDGE_BASE,
        canActivate: [roleAuthGuard],
        loadChildren: () =>
          import('./views/knowledge-base/knowledge-base.routes').then(
            (m) => m.KNOWLEDGE_BASE_ROUTES
          ),
      },
      {
        path: APP_ROUTES.INTEGRATIONS,
        canActivate: [roleAuthGuard],
        loadComponent: () =>
          import('./views/integrations/integrations.component').then(
            (m) => m.IntegrationsComponent
          ),
      },
      {
        path: APP_ROUTES.TAG,
        canActivate: [roleAuthGuard],
        loadComponent: () =>
          import('./views/tag/tag.component').then((m) => m.TagComponent),
      },
      {
        path: APP_ROUTES.LEADS,
        canActivate: [roleAuthGuard],
        loadComponent: () =>
          import('./views/leads/leads.component').then((m) => m.LeadsComponent),
      },
      {
        path: APP_ROUTES.FAQ,
        canActivate: [roleAuthGuard],
        loadComponent: () =>
          import('./views/faq/faq.component').then((m) => m.FaqComponent),
      },
      {
        path: APP_ROUTES.SETTINGS,
        canActivate: [roleAuthGuard],
        loadComponent: () =>
          import('./views/settings/settings.component').then(
            (m) => m.SettingsComponent
          ),
      },
      {
        path: APP_ROUTES.PREVIEW,
        canActivate: [roleAuthGuard, mobileGuard],
        loadComponent: () =>
          import('./views/preview/preview.component').then(
            (m) => m.PreviewComponent
          ),
      },
      {
        path: APP_ROUTES.ADMIN,
        canActivate: [roleAuthGuard],
        loadChildren: () =>
          import('./views/admin/admin.routes').then((m) => m.ADMIN_ROUTES),
      },
      {
        path: '',
        redirectTo: APP_ROUTES.DASHBOARD,
        pathMatch: 'full',
      },
    ],
  },
  {
    path: APP_ROUTES.AUTH,
    component: AuthLayoutComponent,
    canActivate: [noAuthGuard],
    children: [
      {
        path: '',
        loadChildren: () =>
          import('./views/auth/auth.routes').then((m) => m.AUTH_ROUTES),
      },
    ],
  },
  {
    path: '**',
    loadComponent: () =>
      import('./views/error/error.component').then((m) => m.ErrorComponent),
  },
];
