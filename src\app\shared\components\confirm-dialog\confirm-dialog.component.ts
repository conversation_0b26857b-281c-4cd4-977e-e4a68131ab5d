import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { DIALOG_DATA, DxButton, DxDialogRef } from '@dx-ui/ui';

export interface ConfirmDialogData {
  title: string;
  content: string;
  isDelete?: boolean;
  confirmText?: string;
  cancelText?: string;
}

@Component({
  selector: 'app-confirm-dialog',
  standalone: true,
  imports: [CommonModule, DxButton],
  template: `
    <div class="p-6 bg-base-200 dark:bg-dark-base-200">
      <h2
        class="text-[28px] font-bold mb-4 text-base-content dark:text-dark-base-content"
      >
        {{ data.title }}
      </h2>
      <p class="mb-6  text-neutral-content dark:text-dark-neutral-content">
        {{ data.content }}
      </p>

      <div class="flex justify-end gap-3">
        <button dxButton="elevated" type="button" (click)="onCancel()">
          {{ data.cancelText || 'Cancel' }}
        </button>
        <button
          type="button"
          dxButton="filled"
          (click)="onConfirm()"
          [ngStyle]="data.isDelete ? {
            '--dx-button-filled-container-color': '#E07272',
            '--dx-button-filled-hover-container-color': '#E38080',
            '--dx-button-filled-disabled-container-color': '#E8BBBB'
          } : {}"
        >
          {{ data.confirmText || (data.isDelete ? 'Delete' : 'Confirm') }}
        </button>
      </div>
    </div>
  `,
  styles: [],
})
export class ConfirmDialogComponent {
  dialogRef = inject(DxDialogRef<ConfirmDialogComponent>);
  data: {
    title?: string;
    content?: string;
    isDelete?: boolean;
    confirmText?: string;
    cancelText?: string;
  } = inject(DIALOG_DATA);

  onConfirm(): void {
    this.dialogRef.close(true);
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
