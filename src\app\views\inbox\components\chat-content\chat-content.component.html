@if (!isLoadingContentConversation()) { @if (showMessageInbox()) {
<div class="messages-wrapper flex-1 flex flex-col">
  <div
    class="w-full border-b border-primary-border dark:border-dark-primary-border flex px-6 py-4 gap-4"
  >
    <div
      class="flex flex-wrap gap-2 items-center"
      [ngStyle]="{ 'max-width': 'calc(100% - 52px)' }"
    >
      @if (listTagSelectedInConversation().length > 0) {
      <div class="mr-3 flex flex-wrap gap-2">
        @for (tag of listTagSelectedInConversation(); track tag) {
        <div
          class="tag tag-full font-semibold cursor-pointer flex-shrink-0"
          [ngStyle]="{
            background: getColor(tag.config),
            color: getTextColor(getColor(tag.config))
          }"
          (click)="onAddTag()"
        >
          {{ tag.name }}
        </div>
        }
      </div>
        <svg
          class=""
          width="2"
          height="12"
          viewBox="0 0 2 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M1 0V12" stroke="#D2D6DD" />
        </svg>

      }

      <div
        class="pl-3 cursor-pointer flex gap-2 items-center justify-center font-semibold text-primary dark:text-dark-primary"
        (click)="onAddTag()"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9 1C9 0.447715 8.55229 0 8 0C7.44772 0 7 0.447715 7 1V7H1C0.447715 7 0 7.44772 0 8C0 8.55229 0.447715 9 1 9H7V15C7 15.5523 7.44771 16 8 16C8.55228 16 9 15.5523 9 15V9H15C15.5523 9 16 8.55229 16 8C16 7.44772 15.5523 7 15 7H9V1Z"
            fill="#7F75CF"
          />
        </svg>

        Add Tag
      </div>
    </div>
  </div>
  <div class="w-full flex-1 flex flex-col">
    <div class="flex-1 flex flex-col">
      <div class="messages flex-1 overflow-auto relative" #scrollContainer>
        <div class="chat-content-container pl-4 py-4 mb-12 w-full font-normal">
          <!-- chat message -->
          @if (chatMessages() && chatMessages().length != 0) {
          <div class="w-full pr-4">
            @for (msg of chatMessages(); track msg; let i = $index) { @if
            (msg.role === 'assistant' || msg.role === 'human_operator') {
            <div class="mb-4 w-full md:pr-12">
              <div class="block md:flex">
                <div
                  class="flex-none hidden md:flex flex-col items-center space-y-1 mr-4"
                >
                  <img
                    src="./assets/img/logo-chat.png"
                    alt="This is logo"
                    class="w-10 h-10"
                  />
                </div>
                <div class="relative" style="width: calc(100% - 28px)">
                  @if (msg.typeMessage == 'text') {
                  <div
                    #childDiv
                    [ngClass]="{
                      'bg-base-100 dark:bg-dark-base-100 text-base-content dark:text-dark-base-content':
                        msg.role === 'assistant',
                      'bg-green-100': msg.role === 'human_operator'
                    }"
                    class="msg-bubble msg-bubble-assistant !block overflow-x-auto text-[15px] font-normal"
                    [dxTooltip]="
                      (msg.created_at + 'Z' | date : 'dd/MM/yyyy HH:mm:ss') ||
                      ''
                    "
                    [dxTooltipPosition]="'below'"
                  >
                    @if (msg.typeMessage == 'text') {
                    <div [innerHTML]="msg.content | safeHtml"></div>
                    } @if (msg.showSource && msg.listTextSources &&
                    msg.listTextSources.length > 0) {
                    <br />
                    <div class="grid grid-cols-3">
                      <div
                        class="mb-2 p-3 border border-gray-200 bg-gray-50 rounded-2xl grid gap-4"
                        [ngClass]="{
                          'col-span-1 grid-cols-1':
                            msg.listTextSources &&
                            msg.listTextSources.length === 1,
                          'col-span-2 grid-cols-2':
                            msg.listTextSources &&
                            msg.listTextSources.length === 2,
                          'col-span-3 grid-cols-3':
                            msg.listTextSources &&
                            msg.listTextSources.length >= 3
                        }"
                      >
                        @for (source of msg.listTextSources; track source) {
                        <a
                          class="col-span-1 flex items-center justify-between space-x-1 px-2 py-1 bg-[#d4c6ff] text-black rounded-xl cursor-pointer hover:bg-[#f0ecff] hover:text-light-primary hover:underline"
                          [dxTooltip]="source ? source?.source : ''"
                          [href]="source?.source"
                          target="_blank"
                          dxTooltipPosition="below"
                        >
                          <div class="flex-grow truncate">
                            {{ source?.title }}
                          </div>
                          <div class="flex items-center justify-center">
                            <ng-icon
                              name="faSolidShareFromSquare"
                              class="text-right"
                            ></ng-icon>
                          </div>
                        </a>
                        }
                      </div>
                    </div>
                    }
                  </div>
                  } @if (msg.typeMessage == 'gallery') {
                  <app-image-gallery
                    style="
                      --max-width: 364px;
                      --img-width: 110px;
                      --img-height: 110px;
                      --gap: 10px;
                      --label-space: 10px;
                    "
                    [imageSrcs]="msg.listImages"
                  ></app-image-gallery>
                  } @if (msg.typeMessage == 'grid') {
                  <app-image-grid
                    style="--col: 2"
                    [imageSrcs]="msg.listImages"
                  ></app-image-grid>
                  } @if (msg.user) {
                  <span class="absolute left-0 -top-3 text-[0.5rem]">{{
                    msg.user.email
                  }}</span>
                  } @if (msg.summary) {
                  <div class="mt-4 relative">
                    <div
                      [innerHTML]="msg.summary"
                      class="msg-bubble bg-gray-200 !block"
                    ></div>
                    <span
                      class="absolute left-0 -top-3 text-[0.5rem] text-light-primary"
                      >Summarized conversation</span
                    >
                  </div>
                  }
                </div>
                <div class="h-full">
                  @if (msg.listTextSources && msg.listTextSources.length > 0) {
                  <ng-icon
                    name="faSolidFloppyDisk"
                    size="20"
                    class="text-gray-500 ml-2 flex justify-center cursor-pointer"
                    (click)="onOpenSaveFAQDialog(msg)"
                  ></ng-icon>
                  }
                </div>
              </div>
            </div>
            } @if (msg.role === 'user') {
            <div
              class="flex w-full md:max-w-[60vw] ml-auto items-center flex-row-reverse mb-2 gap-4"
            >
              <div
                [innerHTML]="msg.content"
                class="bg-primary dark:bg-dark-primary text-white msg-bubble msg-bubble-user relative"
                [dxTooltip]="
                  (msg.created_at + 'Z' | date : 'dd/MM/yyyy HH:mm:ss') || ''
                "
                [dxTooltipPosition]="'left'"
              ></div>
            </div>
            } } @if (isTyping()) {
            <div class="flex items-center mb-4">
              <div
                class="typing-animation bg-white p-2 rounded-lg mb-2 relative"
              >
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
              </div>
            </div>
            }
          </div>
          }
        </div>
      </div>
      @if (isChatWithAI()) {
        <div class="flex-shrink-0 px-6 my-4">
        <dx-form-field
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
          class="w-full"
        >
          <textarea
            id="chat"
            (keydown)="onKeyDown($event)"
            dxInput
            autoResize
            rows="1"
            [disabled]="isTyping()"
            class="resize-none"
            placeholder="Type your message here..."
            [ngModel]="message()"
            (ngModelChange)="onMessageChange($event); updateTextAreaHeight()"
          >
          </textarea>
          @if (!isSendingMessage()) {
              <app-svg-icon
                dxSuffix
                type="icSend"
                class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content mr-3 cursor-pointer"
                [ngClass]="{
                  '!text-primary dark:!text-dark-primary': message().trim().length > 0
                }"
                (click)="!isSendingMessage() && onSendMessage()"
              >
              </app-svg-icon>
          } @else {
            <dx-progress-spinner dxSuffix [diameter]="24"></dx-progress-spinner>
          }
        </dx-form-field>
        </div>
      }
    </div>
  </div>
</div>
} } @if (isLoadingContentConversation()) {
<div class="h-full flex flex-1 justify-center items-center">
  <mat-spinner diameter="30"></mat-spinner>
</div>
} @if (!showMessageInbox()) {
<div
  class="p-4 h-full flex justify-center items-center flex-1 text-base-content dark:text-dark-base-content dark:text-dark-base-content"
>
  To display a conversation, please select one from the list of conversations on
  the left side.
</div>
}
