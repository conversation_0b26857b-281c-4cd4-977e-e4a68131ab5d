<dx-drawer-container [backdrop]="true" (backdropClick)="backdropClick()">
  <dx-drawer class="m-dx-drawer" #drawer mode="over" position="end">
    <div class="w-full flex flex-col">
      <div class="w-full flex items-center p-4">
        <ng-icon name="heroChevronLeft"></ng-icon>
        <div>More</div>
      </div>
      <div class="w-full h-full"></div>
    </div>
  </dx-drawer>
  <dx-drawer-content>
    <nav
      class="fixed z-[9999] bottom-0 w-full h-18 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200 flex"
    >
      <a
        routerLink="/dashboard"
        class="flex flex-col space-y-1 justify-center items-center flex-1 h-full px-1 overflow-hidden text-neutral-content dark:text-dark-neutral-content font-medium"
        routerLinkActive="!text-primary font-semibold"
        [routerLinkActiveOptions]="{ exact: false }"
      >
        <app-svg-icon type="icDashboard" class="w-7 h-7"></app-svg-icon>
        <div class="text-[13px]">Dashboard</div>
      </a>

      <a
        routerLink="/inbox"
        class="flex flex-col space-y-1 justify-center items-center flex-1 h-full px-1 overflow-hidden text-neutral-content dark:text-dark-neutral-content font-medium"
        routerLinkActive="!text-primary font-semibold"
        [routerLinkActiveOptions]="{ exact: false }"
      >
        <app-svg-icon type="icInbox" class="w-7 h-7"></app-svg-icon>
        <div class="text-[13px]">Inbox</div>
      </a>

      <a
        routerLink="/knowledge-base"
        class="flex flex-col space-y-1 justify-center items-center flex-1 h-full px-1 overflow-hidden text-neutral-content dark:text-dark-neutral-content font-medium"
        routerLinkActive="!text-primary !font-semibold"
        [routerLinkActiveOptions]="{ exact: false }"
      >
        <app-svg-icon type="icKnowledgeBase" class="w-7 h-7"></app-svg-icon>
        <div class="text-[13px]">Knowledge</div>
      </a>

      <a
        routerLink="/account"
        class="flex flex-col space-y-1 justify-center items-center flex-1 h-full px-1 overflow-hidden text-neutral-content dark:text-dark-neutral-content font-medium"
        routerLinkActive="!text-primary !font-semibold"
        [routerLinkActiveOptions]="{ exact: false }"
      >
        <app-svg-icon type="icAdmin" class="w-7 h-7"></app-svg-icon>
        <div class="text-[13px]">Account</div>
      </a>

      <a
        class="flex flex-col space-y-1 justify-center items-center flex-1 h-full px-1 overflow-hidden text-neutral-content dark:text-dark-neutral-content font-medium"
        (click)="drawer.toggle()"
      >
        <app-svg-icon type="icIntegration" class="w-7 h-7"></app-svg-icon>
        <div class="text-[13px]">More</div>
      </a>
    </nav>
  </dx-drawer-content>
</dx-drawer-container>
