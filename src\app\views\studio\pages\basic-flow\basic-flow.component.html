<div
  class="h-full flex flex-col overflow-hidden text-light-text dark:text-dark-text"
>
  <div class="flex items-start justify-between">
    <h1
      class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
    >
      Basic Flow
    </h1>
    <app-flow-env-select></app-flow-env-select>
  </div>

  <!-- Search and Filter Section -->
  <div
    class="mt-6 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
  >
    <div class="flex flex-wrap items-center justify-between">
      <div class="flex items-center space-x-4 flex-wrap">
        <dx-form-field
          class="w-full md:w-72"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <app-svg-icon
            dxPrefix
            type="icSearch"
            class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
          ></app-svg-icon>
          <input
            dxInput
            [(ngModel)]="searchModel.key_word"
            (ngModelChange)="searchSubject.next($event)"
            [type]="'text'"
            placeholder="Search by Name"
          />
        </dx-form-field>
        <dx-form-field
          class="w-full md:w-48"
          id="role-filter"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <dx-select
            [(ngModel)]="searchModel.trigger_type"
            (selectionChange)="doSearch()"
          >
            @for (type of listTriggerType; track $index) {
            <dx-option [value]="type.value">{{ type.label }}</dx-option>
            }
          </dx-select>
        </dx-form-field>
      </div>

      @if (studioStore.status() === STUDIO_STATUS.DEV) {
      <div class="flex items-center justify-end">
        <button dxButton="filled" (click)="openCreateDialog()">Add Flow</button>
      </div>
      }
    </div>

    <!-- Data Table -->
    <div class="flex-1 flex overflow-hidden">
      <app-data-table
        [rows]="flows()"
        [columns]="columns()"
        [pageIndex]="0"
        [limit]="100"
        [count]="totalFlows()"
        (action)="onAction($event)"
        [rowTemplate]="rowTemplate"
        [actionTemplate]="actionTemplate"
        class="w-full"
      >
      </app-data-table>
    </div>
  </div>
</div>

<ng-template #rowTemplate let-row="row" let-column="column">
  @switch (column.columnDef) { @case ('name') {
  <div class="flex cursor-pointer hover:underline" (click)="onEditFlow(row)">
    {{ row[column.columnDef] }}
  </div>
  } @case ('version') {
  <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
    {{ getVersionLabel(row.id) }}
  </div>
  } @default {
  <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
    {{ row[column.columnDef] }}
  </div>
  } }
</ng-template>

<ng-template #actionTemplate let-row let-column>
  <div class="flex justify-center items-center">
    <button
      class="flex cursor-pointer hover:opacity-80"
      (click)="row.isActions = !row.isActions"
      cdkOverlayOrigin
      #trigger="cdkOverlayOrigin"
    >
      <ng-icon
        name="heroEllipsisHorizontalMini"
        size="24"
        class="flex items-center justify-center"
      ></ng-icon>
    </button>
    <ng-template
      cdkConnectedOverlay
      [cdkConnectedOverlayOrigin]="trigger"
      [cdkConnectedOverlayOpen]="row.isActions"
      [cdkConnectedOverlayPush]="true"
      [cdkConnectedOverlayPositions]="[
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'top',
          offsetY: 10,
        },
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'bottom',
          offsetY: 10,
        },
      ]"
    >
      <ul
        class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
        (clickOutside)="row.isActions = false; row.isContextMenu = false"
      >
        <!--@if (allowClone()) {
          <li>
            <button
              (click)="$event.stopPropagation(); onClone(row)"
              class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
            >
              <app-svg-icon
                type="icCopy"
                class="w-6 h-6 flex items-center justify-center"
              ></app-svg-icon>
              <div
                class="flex items-center justify-between text-[16px] font-medium"
              >
                Clone
              </div>
            </button>
          </li>
        }-->
        @if (allowPublish()) {
        <li>
          <button
            (click)="$event.stopPropagation(); onPublish(row)"
            class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
          >
            <ng-icon
              name="heroBolt"
              class="text-2xl flex items-center justify-center"
            ></ng-icon>
            <div
              class="flex items-center justify-between text-[16px] font-medium"
            >
              Publish
            </div>
          </button>
        </li>
        } @if (allowEdit()) {
        <li>
          <button
            (click)="$event.stopPropagation(); onEdit(row)"
            class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
          >
            <app-svg-icon
              type="icEdit"
              class="w-6 h-6 flex items-center justify-center"
            ></app-svg-icon>
            <div
              class="flex items-center justify-between text-[16px] font-medium"
            >
              Edit
            </div>
          </button>
        </li>
        }
        <li>
          <button
            (click)="$event.stopPropagation(); onDelete(row)"
            class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
          >
            <app-svg-icon
              type="icTrash"
              class="w-6 h-6 flex items-center justify-center"
            ></app-svg-icon>
            <div
              class="flex items-center justify-between text-[16px] font-medium"
            >
              Delete
            </div>
          </button>
        </li>
      </ul>
    </ng-template>
  </div>
</ng-template>
