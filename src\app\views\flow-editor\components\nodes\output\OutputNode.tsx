// @ts-nocheck
import InputField from "@flow-editor/components/form/InputField";
import SelectField from "@flow-editor/components/form/SelectField";
import NodeHeader from "@flow-editor/components/nodes/NodeHeader";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor/components/styled";
import NodeTooltip from "@flow-editor/components/tooltip/NodeTooltip";
import { STUDIO_STATUS } from "@flow-editor/constant";
import { useFlowInstance } from "@flow-editor/hook";
import { LayoutState, OutputNodeData } from "@flow-editor/model";
import {
  useBuildFlowState,
  useFlowInstanceState,
  useLayoutState,
  useStudioState
} from "@flow-editor/store";
import { isValidConnection } from "@flow-editor/utils/flow";
import { hexToRgb } from "@flow-editor/utils/util";
import { yupResolver } from "@hookform/resolvers/yup";
import { Divider, Modal, Typography } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Handle, Position, useReactFlow } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";

const StyledHandleSourceAnchor = styled.div<{ $bgColor, $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => props.$data ? 'white' : `rgba(255, 255, 255, 0.5)`};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => props.$data ? 'normal' : 'italic'};
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const FORMAT_OPTIONS = [
  {
    id: 'text',
    name: 'Text',
  },
  {
    id: 'markdown',
    name: 'Markdown',
  },
  {
    id: 'json',
    name: 'JSON',
  },
  {
    id: 'html',
    name: 'HTML',
  },
]

const outputNodeDataFormSchema = yup.object().shape({
  output_key: yup.string().required('Output key is required'),
  value: yup.string().required('Value is required'),
  format: yup.string(FORMAT_OPTIONS[0].id).required('Format is required'),
});

const OutputNode = ({data}: { data: OutputNodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const {flowInstance} = useFlowInstanceState(state => state);
  const {flow, setDirty} = useBuildFlowState(state => state);
  const {status} = useStudioState(state => state);
  const [selectedItems, setSelectedItems] = useState([]);
  const {getEdges, setEdges} = useReactFlow();
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const {theme} = useLayoutState<LayoutState>(state => state);
  const {
    control,
    setValue,
    handleSubmit,
    reset
  } = useForm({
    resolver: yupResolver(outputNodeDataFormSchema)
  })

  const resetForm = () => {
    reset()
  }

  const onSubmit = (formValue) => {
    data.output_key = formValue?.output_key;
    data.value = formValue?.value;
    data.format = formValue?.format;

    resetForm();
    setModalOpen(false);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  }

  useEffect(() => {
    resetForm();
    data.output_key && setValue('output_key', data.output_key);
    data.value && setValue('value', data.value);
    data.format && setValue('format', data.format);
  }, [JSON.stringify(data)]);

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    setModalOpen(true);
  }, []);

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $theme={theme}>
      <div className="absolute left-0 text-[10px]" style={{top: -20, color: '#D4D9E2'}}>{data.id}</div>
      {
        status && status === STUDIO_STATUS.DEV && (
          <NodeTooltip data={data} selected={data.selected}/>
        )
      }
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black'
          }}
        />
        <StyledHandleSourceAnchor $bgColor={data.node_color} $data={!!(data.value)} onDoubleClick={handleOpenModal}>
          {
            data.value ? data.value : 'Configure'
          }
        </StyledHandleSourceAnchor>
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col space-y-4 mt-6">
          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
              Value <span style={{color: 'red'}}>*</span>
            </Typography.Text>
            <InputField
              type={"textarea"}
              name={`value`}
              control={control}
              setValue={setValue}
            />
          </div>

          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
              Format <span style={{color: 'red'}}>*</span>
            </Typography.Text>
            <SelectField name={'format'} disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                         options={FORMAT_OPTIONS.map(v => ({
                           ...v,
                           key: v.id,
                           value: v.id,
                           label: v.name
                         }))}/>
          </div>

          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
              Output key <span style={{color: 'red'}}>*</span>
            </Typography.Text>
            <InputField
              type={"input"}
              name={`output_key`}
              control={control}
              setValue={setValue}
            />
          </div>

          <Divider className="!border-primary-border dark:!border-dark-primary-border"/>

          <div className="w-full flex justify-end items-center space-x-4">
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() => setModalOpen(false)}
            >
              Cancel
            </StyledButtonModal>
            <StyledButtonModal
              type="submit"
              key="save"
              $bgColor={"#7F75CF"}
              $color={"white"}
              disabled={status && status === STUDIO_STATUS.LIVE}
            >
              Save
            </StyledButtonModal>
          </div>
        </form>
      </Modal>
    </StyledNodeFlow>
  );
};

export default OutputNode;
