<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      {{ data.isCreate ? "Create function" : "Edit function" }}
    </div>
    <div class="flex items-center justify-end space-x-4">
      <app-svg-icon
        type="icClose"
        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></app-svg-icon>
    </div>
  </div>
  <div class="flex-1 overflow-y-auto mt-18 mb-20">
    <div
      [formGroup]="formGroup"
      class="px-6 pt-6 pb-[3px] flex flex-col space-x-4"
    >
      <dx-form-field class="w-full" id="name">
        <dx-label class="text-sm">Name</dx-label>
        <input
          dxInput
          formControlName="name"
          [type]="'text'"
          placeholder="Enter name"
        />
        @if (formGroup.get('name')?.errors &&
        formGroup.get('name')?.errors?.['required'] &&
        (formGroup.get('name')?.touched || formGroup.get('name')?.dirty)) {
        <dx-error>Name is required.</dx-error>
        }
      </dx-form-field>
    </div>
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Cancel</button>
    <button
      dxLoadingButton="filled"
      [loading]="isSubmitting()"
      [disabled]="formGroup.invalid"
      (click)="saveFunction()"
    >
      {{ !data.isCreate ? "Update" : "Create" }}
    </button>
  </div>
</div>

<!--
<ng-template #createFunctionDialog let-data>
            <div
              class="flex flex-col h-full bg-light-background dark:bg-dark-background text-light-text dark:text-dark-text dark:border dark:border-dark-border-line">
              <div
                class="header border-b border-b-light-border-line dark:border-b-dark-border-line pt-6 px-6 flex justify-between items-center pb-3">
                <div class="text-2xl font-bold card-title capitalize">
                  {{ data.isCreate ? 'Create function' : 'Edit function' }}
                </div>
                <div>
                  <svg (click)="closeDialogCreateOrUpdateFunction()" width="24" height="24" viewBox="0 0 24 24"
                       fill="none"
                       xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
                      fill="#6F767E"/>
                  </svg>
                </div>
              </div>

              <div class="w-full px-6 pt-6 content overflow-y-auto max-h-[75vh]">
                <form [formGroup]="formGroup">
                  <div
                    class="border border-light-border-line dark:border-dark-border-line rounded-2xl bg-light-secondary-background dark:bg-dark-secondary-background p-4 mb-6">
                    <div class="mb-6">
                      <mat-label class="required" for="name">Name</mat-label>
                      <input
                        class="w-full mt-2 bg-light-background dark:bg-dark-background border border-light-border-line dark:border-dark-border-line px-4 h-[40px] rounded-lg"
                        type="text" id="name" formControlName="name">
                      <app-error-message
                        [control]="formGroup.get('name')"
                        [name]="'Function name'"
                      ></app-error-message>
                    </div>
                  </div>
                </form>
              </div>
              <div class="footer p-6 flex justify-between">
                <div class="flex">
                  <button
                    class="cursor-pointer h-[40px] min-w-[120px] px-3 rounded-2xl bg-light-secondary-background dark:bg-dark-secondary-background border border-light-border-line dark:border-dark-border-line"
                    (click)="closeDialogCreateOrUpdateFunction()">
                    <span>Cancel</span>
                  </button>
                  <button
                    class="cursor-pointer h-[40px] min-w-[120px] bg-light-primary text-white px-3 rounded-2xl border border-light-border-line dark:border-dark-border-line button-disabled"
                    [disabled]="formGroup.invalid" style="margin-left: 12px;"
                    (click)="saveFunction()"
                  >
                    <span>Save</span>
                  </button>
                </div>
              </div>
            </div>
          </ng-template>
-->
