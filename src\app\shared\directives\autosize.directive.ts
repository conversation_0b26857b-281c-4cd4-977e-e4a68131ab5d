import {Directive, ElementRef, HostListener, inject, Input} from '@angular/core';

@Directive({
  selector: 'textarea[autoResize]'
})
export class AutosizeDirective {
  private element = inject<ElementRef<HTMLTextAreaElement>>(ElementRef);

  @Input() set ngModel(_: any) {
    this.resize(); // gọi lại khi ngModel thay đổi
  }

  @HostListener('input')
  onInput(): void {
    this.resize();
  }
  ngOnChanges(): void {
    this.resize();
  }

  ngAfterViewInit(): void {
    setTimeout(() => this.resize()); // đảm bảo resize sau khi view render xong
  }

  private resize() {
    const ta = this.element.nativeElement;
    ta.style.height = 'auto';
    ta.style.height = `${ta.scrollHeight}px`;
  }
}
