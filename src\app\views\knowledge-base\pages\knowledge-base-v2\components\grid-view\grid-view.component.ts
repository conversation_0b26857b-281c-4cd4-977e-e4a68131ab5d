import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  input,
  output,
  ViewChild,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { CdkVirtualScrollViewport, ScrollingModule } from '@angular/cdk/scrolling';
import { NgIconComponent, provideIcons } from '@ng-icons/core';
import {
  heroArrowDown,
  heroArrowUp,
  heroDocumentText,
  heroEllipsisHorizontal,
  heroEllipsisVertical,
  heroEye,
  heroFolder,
  heroFolderOpen,
  heroInformationCircle,
  heroPencil,
  heroPencilSquare,
  heroTrash,
  heroXMark,
} from '@ng-icons/heroicons/outline';
import {
  faSolidFile,
  faSolidFolder,
  faSolidFolderClosed,
} from '@ng-icons/font-awesome/solid';
import { SelectionModel } from '@angular/cdk/collections';
import { IFile, IFolder, ISearchModel } from '@shared/models';
import { DxTooltip } from '@dx-ui/ui';
import {SvgIconComponent} from '@shared/components';


@Component({
  selector: 'app-grid-view',
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    ScrollingModule,
    NgIconComponent,
    DxTooltip,
    SvgIconComponent,
  ],
  providers: [
    provideIcons({
      heroArrowDown,
      heroArrowUp,
      heroDocumentText,
      heroEllipsisHorizontal,
      heroEllipsisVertical,
      heroEye,
      heroFolder,
      heroFolderOpen,
      heroInformationCircle,
      heroPencil,
      heroPencilSquare,
      heroTrash,
      heroXMark,
      faSolidFile,
      faSolidFolder,
      faSolidFolderClosed,
    }),
  ],
  templateUrl: './grid-view.component.html',
  styleUrl: './grid-view.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GridViewComponent {
  private cdr = inject(ChangeDetectorRef);

  // Signal-based inputs
  folderList = input<IFolder[]>([]);
  fileList = input<IFile[]>([]);
  searchModel = input.required<ISearchModel>();
  folderSelection = input.required<SelectionModel<IFolder>>();
  fileSelection = input.required<SelectionModel<IFile>>();

  // Signal-based outputs
  folderSelected = output<IFolder>();
  fileSelected = output<IFile>();
  folderDoubleClick = output<IFolder>();
  fileDoubleClick = output<IFile>();
  folderContextMenu = output<{ folder: IFolder, event: MouseEvent }>();
  fileContextMenu = output<{ file: IFile, event: MouseEvent }>();

  // New specific action outputs
  folderRename = output<any>();
  folderDelete = output<any>();
  fileInfo = output<any>();
  fileRename = output<any>();
  fileMove = output<any>();
  fileDelete = output<any>();
  refreshRequested = output<void>();

  @ViewChild('folderViewport', { static: false })
  folderViewport!: CdkVirtualScrollViewport;
  @ViewChild('fileViewport', { static: false })
  fileViewport!: CdkVirtualScrollViewport;

  // Event handlers
  onFolderClick(folder: IFolder) {
    this.folderSelected.emit(folder);
  }

  onFileClick(file: IFile) {
    this.fileSelected.emit(file);
  }

  onFolderDoubleClick(folder: IFolder) {
    this.folderDoubleClick.emit(folder);
  }

  onFileDoubleClick(file: IFile) {
    this.fileDoubleClick.emit(file);
  }

  onFolderContextMenu(folder: IFolder, event: MouseEvent) {
    this.folderContextMenu.emit({ folder, event });
  }

  onFileContextMenu(file: IFile, event: MouseEvent) {
    this.fileContextMenu.emit({ file, event });
  }

  // New context menu action methods
  onFolderRename(folder: IFolder) {
    this.folderRename.emit(folder);
  }

  onFolderDelete(folder: IFolder) {
    this.folderDelete.emit(folder);
  }

  onFileInfo(file: IFile) {
    this.fileInfo.emit(file);
  }

  onFileRename(file: IFile) {
    this.fileRename.emit(file);
  }

  onFileMove(file: IFile) {
    this.fileMove.emit(file);
  }

  onFileDelete(file: IFile) {
    this.fileDelete.emit(file);
  }

  onRefreshClick() {
    this.refreshRequested.emit();
  }

  // Utility methods
  trackByFolderId(_index: number, folder: IFolder): number {
    return folder.id || 0;
  }

  trackByFileId(_index: number, file: IFile): number {
    return file.id || 0;
  }

  removeSelectedFolderOrFile() {
    this.folderSelection().clear();
    this.fileSelection().clear();
    this.cdr.detectChanges();
  }

  refreshVirtualScrollViewports() {
    if (this.folderViewport) {
      this.folderViewport.checkViewportSize();
      this.folderViewport.scrollToIndex(0);
    }

    if (this.fileViewport) {
      this.fileViewport.checkViewportSize();
      this.fileViewport.scrollToIndex(0);
    }

    this.cdr.detectChanges();
  }

  getFileIcon(file: IFile): string {
    const extension = file.ext?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'heroDocumentText';
      case 'csv':
      case 'txt':
      case 'md':
        return 'heroDocumentText';
      default:
        return 'faSolidFile';
    }
  }

  getFileTypeColor(file: IFile): string {
    const extension = file.ext?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return '#ef4444'; // red
      case 'csv':
        return '#10b981'; // green
      case 'txt':
        return '#6b7280'; // gray
      case 'md':
        return '#8b5cf6'; // purple
      default:
        return '#3b82f6'; // blue
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'COMPLETED':
        return '#10b981'; // green
      case 'IN_PROGRESS':
        return '#f59e0b'; // yellow
      case 'PENDING':
        return '#6b7280'; // gray
      case 'FAILED':
        return '#ef4444'; // red
      default:
        return '#6b7280'; // gray
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
