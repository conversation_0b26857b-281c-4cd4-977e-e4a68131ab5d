import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { UIStore, UserAiStore } from '@core/stores';
import {
  DxButton,
  DxDialog,
  DxFormField,
  DxInput,
  DxOption,
  DxSelect,
  DxSnackBar,
} from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { heroInformationCircle, heroXMark } from '@ng-icons/heroicons/outline';
import { ROLE, STATUS } from '@shared/app.constant';
import { ConfirmDialogComponent, SvgIconComponent } from '@shared/components';
import { IUserAi } from '@shared/models';
import { SettingsService } from '@shared/services';

@Component({
  selector: 'app-user-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DxFormField,
    DxInput,
    DxSelect,
    DxOption,
    DxButton,
    NgIconsModule,
    SvgIconComponent,
  ],
  templateUrl: './user-settings.component.html',
  styleUrls: ['./user-settings.component.css'],
  providers: [provideIcons({ heroInformationCircle, heroXMark })],
})
export class UserSettingsComponent implements OnInit {
  selectedTab = signal(1);
  listUserInAI = signal<IUserAi[]>([]);

  readonly listRoleInvite = [
    { value: 'ADMIN', label: 'Admin (Full access)' },
    { value: 'EDITOR', label: 'Editor (Inbox & Knowledge Base)' },
    { value: 'SUPPORT', label: 'Support (Inbox only)' },
  ];
  readonly STATUS = STATUS;
  readonly ROLE = ROLE;

  uiStore = inject(UIStore);
  userAiStore = inject(UserAiStore);
  private settingsService = inject(SettingsService);
  private dialog = inject(DxDialog);
  private snackBar = inject(DxSnackBar);

  email = new FormControl<string>('');
  role = new FormControl<string>('');

  ngOnInit(): void {
    this.getListUserInAi();
  }

  sendMailInvite() {
    const body = {
      email: this.email.value,
      role: this.role.value,
    };
    this.settingsService.sendMailInvite(body).subscribe({
      next: (res: any) => {
        this.email.setValue('');
        this.snackBar.open(res.message, '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        this.getListUserInAi();
      },
      error: (err) => {
        this.snackBar.open(err.error.detail, '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
    });
  }

  deleteUserInAI(userAI: IUserAi) {
    this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Delete user in AI',
          content: `Are you sure you want to delete this user in AI?`,
          confirmText: 'Delete',
          cancelText: 'Cancel',
          isDelete: true,
        },
        width: '360px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (value) {
          let params;
          if (userAI.user_id) {
            params = {
              user_id: userAI.user_id,
            };
          } else if (userAI.invitation_id) {
            params = {
              invitation_id: userAI.invitation_id,
            };
          }
          this.settingsService.deleteUserInAI(params).subscribe({
            next: (res) => {
              this.snackBar.open(res.message, '', {
                panelClass: 'dx-snack-bar-success',
                duration: 5000,
                verticalPosition: 'top',
                horizontalPosition: 'right',
              });
              this.getListUserInAi();
            },
          });
        }
      });
  }

  private getListUserInAi() {
    this.settingsService.getListUserInAI().subscribe({
      next: (res) => {
        this.listUserInAI.set(res);
      },
    });
  }
}
