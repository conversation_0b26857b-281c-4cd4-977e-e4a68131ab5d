import {Component, inject, signal} from '@angular/core';
import {<PERSON><PERSON><PERSON>er, FormGroup, FormsModule, ReactiveFormsModule} from "@angular/forms";
import {SvgIconComponent} from '@shared/components';
import {
  DIALOG_DATA,
  DxButton,
  DxDialog,
  DxDialogRef,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxSnackBar
} from '@dx-ui/ui';
import {AutosizeDirective} from '@shared/directives';
import {KnowledgeBaseService} from "@shared/services";

@Component({
  selector: 'app-upload-file',
  imports: [
    FormsModule,
    SvgIconComponent,
    DxButton,
    DxLoadingButton,
    ReactiveFormsModule,
    DxFormField,
    DxLabel,
    DxInput,
    AutosizeDirective,
  ],
  templateUrl: './upload-file.component.html',
  styleUrl: './upload-file.component.css'
})
export class UploadFileComponent {
  isImportingFile = signal(false);
  documentName = signal<any>(null);
  fileUpload = signal<any>(null);
  metadata = signal<any>(null);
  fileURL = signal<any>(null);

  dialogRef = inject(DxDialogRef<UploadFileComponent>);
  formGroup:FormGroup = inject(FormBuilder).group({
    timeKeepImport: [],
    file: [],
  });
  snackBar = inject(DxSnackBar);
  dialog = inject(DxDialog);
  data: {
    parent_id: number;
  } = inject(DIALOG_DATA);
  private KnowledgeBaseService = inject(KnowledgeBaseService);

  ngOnInit(): void {}

  uploadFile(event: any): void {
    const reader = new FileReader();
    const file = event.target.files[0];
    if (file) {
      const allowedFileTypes = ['csv', 'pdf', 'txt', 'md', 'docx'];
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (allowedFileTypes.includes(fileExtension!)) {
        reader.readAsDataURL(file);
        this.fileUpload.set(file);
        this.documentName.set(file.name);
        reader.onload = () => {
          this.fileURL.set(reader.result);
        };
      } else {
        this.showSnackBar(
          'Invalid file format. Please select a CSV, PDF, TXT or Markdown file.',
          'error'
        );
      }
    }
  }
  showSnackBar(message: string, type: 'success' | 'error' | 'info') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }
  saveFileUpload() {
    const formData = new FormData();
    formData.append('file', this.fileUpload());
    const params: any = {
      metadata_columns:
        this.checkFileCsv(this.documentName()) &&
        this.metadata() &&
        this.metadata().trim() !== ''
          ? this.metadata().trim()
          : '',
    };
    if (this.data.parent_id) {
      params.folder_id = this.data.parent_id;
    }
    this.isImportingFile.set(true);
    this.KnowledgeBaseService.createFAQ(formData, params).subscribe({
      next: () => {
        this.showSnackBar('Upload file successfully', 'success');
        this.isImportingFile.set(false);
        this.dialogRef.close(true);
      },
      error: (error) => {
        this.showSnackBar(error.error.detail, 'error');
        this.isImportingFile.set(false);
      }
    });
  }
  checkFileCsv(fileName: string): boolean {
    if (!fileName) {
      return false;
    }
    return fileName.toLowerCase().endsWith('.csv');
  }
}
