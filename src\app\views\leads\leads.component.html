<div class="h-full flex flex-col overflow-hidden">
  <h1
    class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
  >
    Leads Management
  </h1>
  <div
    class="mt-6 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
  >
    <div class="flex flex-wrap items-center justify-between">
      <div class="flex items-center space-x-4 flex-wrap">
        <dx-form-field
          class="w-full md:w-96"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <app-svg-icon
            dxPrefix
            type="icSearch"
            class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
          ></app-svg-icon>
          <input
            dxInput
            [(ngModel)]="searchModel.key_word"
            (ngModelChange)="applyFilters()"
            [type]="'text'"
            placeholder="Search by Name/Email/Phone..."
          />
        </dx-form-field>
        <dx-form-field
          class="w-full md:w-48"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <dx-select
            [(ngModel)]="selectedStatus"
            (ngModelChange)="onStatusChange()"
          >
            @for (status of statusOptions; track $index) {
            <dx-option [value]="status.value">{{ status.label }}</dx-option>
            }
          </dx-select>
        </dx-form-field>
      </div>
      <div class="flex items-center justify-end">
        <button dx-button="filled" (click)="createNewLead()" class="px-4 py-2">
          <div class="flex items-center justify-between space-x-2">
            <!--            <ng-icon name="heroPlus" class="text-2xl"></ng-icon>-->
            <span class="text-sm font-medium">Add Lead</span>
          </div>
        </button>
      </div>
    </div>

    <div class="flex-1 flex overflow-hidden">
      @if (isLoading()) {
      <div class="flex justify-center items-center h-64">
        <div
          class="animate-spin rounded-full h-8 w-8 border-b-2 border-light-primary dark:border-dark-primary"
        ></div>
      </div>
      } @else {
      <app-data-table
        [rows]="listLeads()"
        [columns]="columns"
        [pageIndex]="pageIndex()"
        [limit]="searchModel.pageSize"
        [count]="count()"
        (pageChange)="changePage($event)"
        (action)="onAction($event)"
        [rowTemplate]="rowTemplate"
        [actionTemplate]="actionTemplate"
        class="w-full"
      >
        <!-- ... existing templates ... -->
      </app-data-table>
      }
    </div>
  </div>
</div>

<!-- Custom row template -->
<ng-template #rowTemplate let-row="row" let-column="column">
  @switch (column.columnDef) {
  <!-- Index column -->
  @case ('index') {
  <div class="flex items-center justify-center">
    <span>{{ getRowIndex(row) + 1 }}</span>
  </div>
  }
  <!-- User name column -->
  @case ('user_name') {
  <div
    class="truncate"
    [ngStyle]="{ 'max-width': column.maxWidth, 'min-width': column.minWidth }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  }
  <!-- Phone number column -->
  @case ('phone_number') {
  <div
    class="truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  }
  <!-- Email column -->
  @case ('email') {
  <div
    class="truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  }
  <!-- Status column -->
  @case ('status') {
  <div
    class="truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
  >
    <span
      class="px-2 rounded-full"
      [ngClass]="getStatusClass(row[column.columnDef])"
    >
      {{ getStatusLabel(row[column.columnDef]) }}
    </span>
  </div>
  }
  <!-- User ID column -->
  @case ('user_id') {
  <div
    class="truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  }
  <!-- Update Time column -->
  @case ('update_time') {
  <div
    class="truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="formatDateTime(row[column.columnDef])"
    dxTooltipPosition="below"
  >
    {{ formatDateTime(row[column.columnDef]) }}
  </div>
  }
  <!-- Default for any other columns -->
  @default {
  <div
    class="truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  } }
</ng-template>

<!-- Custom action template -->
<ng-template #actionTemplate let-row>
  <div class="flex justify-center items-center">
    <button
      class="flex cursor-pointer hover:opacity-80"
      (click)="row.isActions = !row.isActions"
      cdkOverlayOrigin
      #trigger="cdkOverlayOrigin"
    >
      <ng-icon
        name="heroEllipsisHorizontal"
        size="24"
        class="flex items-center justify-center"
      ></ng-icon>
    </button>
    <ng-template
      cdkConnectedOverlay
      [cdkConnectedOverlayOrigin]="trigger"
      [cdkConnectedOverlayOpen]="row.isActions"
      [cdkConnectedOverlayPush]="true"
      [cdkConnectedOverlayPositions]="[
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'top',
          offsetY: 10,
        },
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'bottom',
          offsetY: 10,
        },
      ]"
    >
      <ul
        class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
        (clickOutside)="row.isActions = false; row.isContextMenu = false"
      >
        <li>
          <button
            (click)="
              $event.stopPropagation();
              handleAction('view', row);
              row.isActions = false
            "
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg w-full hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover cursor-pointer"
            title="View lead details"
          >
            <ng-icon
              name="heroEye"
              class="text-2xl flex items-center justify-center"
            ></ng-icon>
            <div
              class="flex items-center justify-between text-[16px] font-medium"
            >
              View details
            </div>
          </button>
        </li>
        <li>
          <button
            (click)="
              $event.stopPropagation();
              handleAction('edit', row);
              row.isActions = false
            "
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg w-full hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover cursor-pointer"
            title="Edit lead"
          >
            <ng-icon
              name="heroPencilSquare"
              class="text-2xl flex items-center justify-center"
            ></ng-icon>
            <div
              class="flex items-center justify-between text-[16px] font-medium"
            >
              Edit
            </div>
          </button>
        </li>
        <li>
          <button
            (click)="
              $event.stopPropagation();
              handleAction('delete', row);
              row.isActions = false
            "
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg w-full hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover cursor-pointer"
            title="Delete lead"
          >
            <ng-icon
              name="heroTrash"
              class="text-2xl flex items-center justify-center"
            ></ng-icon>
            <div
              class="flex items-center justify-between text-[16px] font-medium"
            >
              Delete
            </div>
          </button>
        </li>
      </ul>
    </ng-template>
  </div>
</ng-template>

<!-- View Lead Details Dialog Template -->
<ng-template #viewLeadDialog let-data>
  <div
    class="flex flex-col h-full text-light-text dark:text-dark-text p-6 bg-light-background dark:bg-dark-background"
  >
    <!-- Dialog Header -->
    <div
      class="header pb-3 border-b flex justify-between items-center bg-light-background dark:bg-dark-background"
    >
      <div
        class="text-2xl font-bold card-title capitalize text-light-text dark:text-dark-text"
      >
        Lead Details
      </div>
      <div class="cursor-pointer">
        <svg
          (click)="closeLeadDialog()"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
            class="fill-light-text dark:fill-dark-text"
          />
        </svg>
      </div>
    </div>

    <!-- Dialog Content -->
    <div
      class="content w-full max-h-[70vh] mt-2 text-light-text dark:text-dark-text bg-light-background dark:bg-dark-background overflow-auto"
    >
      <div class="space-y-6">
        <!-- Basic Information -->
        <div
          class="bg-light-secondary-background dark:bg-dark-secondary-background p-4 rounded-lg"
        >
          <h3 class="text-lg font-semibold mb-4">Basic Information</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Name</label>
              <div
                class="p-2 bg-light-background dark:bg-dark-background rounded border"
              >
                {{ data.lead.user_name || "N/A" }}
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Status</label>
              <div class="p-2">
                <span
                  class="px-4 rounded-full"
                  [ngClass]="getStatusClass(data.lead.status)"
                >
                  {{ getStatusLabel(data.lead.status) }}
                </span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Phone Number</label>
              <div
                class="p-2 bg-light-background dark:bg-dark-background rounded border"
              >
                {{ data.lead.phone_number || "N/A" }}
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Email</label>
              <div
                class="p-2 bg-light-background dark:bg-dark-background rounded border"
              >
                {{ data.lead.email || "N/A" }}
              </div>
            </div>

            <div class="md:col-span-2">
              <label class="block text-sm font-medium mb-1">User ID</label>
              <div
                class="p-2 bg-light-background dark:bg-dark-background rounded border"
              >
                {{ data.lead.user_id || "N/A" }}
              </div>
            </div>

            <div class="md:col-span-2">
              <label class="block text-sm font-medium mb-1">Update Time</label>
              <div
                class="p-2 bg-light-background dark:bg-dark-background rounded border"
              >
                {{ formatDateTime(data.lead.update_time) || "N/A" }}
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Data -->
        @if (data.lead.data_info && data.lead.data_info !== '{}') {
        <div
          class="bg-light-secondary-background dark:bg-dark-secondary-background p-4 rounded-lg"
        >
          <h3 class="text-lg font-semibold mb-4">Data info</h3>
          <div
            class="p-4 bg-light-background dark:bg-dark-background rounded border"
          >
            <pre class="whitespace-pre-wrap text-sm">{{
              formatDataInfo(data.lead.data_info)
            }}</pre>
          </div>
        </div>
        }
      </div>
    </div>

    <!-- Dialog Footer -->
    <div
      class="footer mt-6 flex justify-end gap-4 bg-light-background dark:bg-dark-background"
    >
      <button
        class="bg-transparent cursor-pointer h-[40px] min-w-[100px] text-light-text dark:text-dark-text px-3 rounded-full"
        (click)="closeLeadDialog()"
      >
        <span>Close</span>
      </button>
    </div>
  </div>
</ng-template>
