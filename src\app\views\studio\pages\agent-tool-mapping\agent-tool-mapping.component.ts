import {
  CdkDragDrop,
  CdkDragEnd,
  CdkDragStart,
  DragDropModule,
} from '@angular/cdk/drag-drop';

import { CdkConnectedOverlay, CdkOverlayOrigin } from '@angular/cdk/overlay';
import { NgClass } from '@angular/common';
import {
  Component,
  computed,
  effect,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { Router } from '@angular/router';
import {
  AGENT_FLOW_PATH,
  APP_ROUTES,
  STUDIO_PATH,
  STUDIO_STATUS,
} from '@core/constants';
import { StudioStore } from '@core/stores';
import {
  DxButton,
  DxDialog,
  DxFormField,
  DxInput,
  DxSnackBar,
} from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroPlus<PERSON>ini,
  heroTrashMini,
  heroXCircleMini,
} from '@ng-icons/heroicons/mini';
import {
  heroArrowRight,
  heroChevronDown,
  heroChevronUp,
  heroEllipsisHorizontal,
  heroWrench,
  heroXMark,
} from '@ng-icons/heroicons/outline';
import {
  ConfirmDialogComponent,
  FlowEnvSelectComponent,
  SvgIconComponent,
} from '@shared/components';
import { ClickOutsideDirective } from '@shared/directives';
import {
  IAgent,
  IAgentDev,
  IAgentToolDev,
  ITool,
  IToolDev,
} from '@shared/models';
import {
  AgentDevService,
  AgentService,
  AgentToolDevService,
  ToolDevService,
  ToolService,
} from '@shared/services';
import { AddOrEditAgentComponent } from '@views/studio/pages/agent/add-or-edit-agent/add-or-edit-agent.component';
import { DetailAgentComponent } from '@views/studio/pages/agent/detail-agent/detail-agent.component';
import { AddToolComponent } from '@views/studio/pages/tool/add-tool/add-tool.component';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-agent-tool-mapping',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    NgIconsModule,
    DragDropModule,
    DxFormField,
    DxInput,
    SvgIconComponent,
    NgClass,
    DxButton,
    CdkOverlayOrigin,
    CdkConnectedOverlay,
    ClickOutsideDirective,
    FlowEnvSelectComponent,
  ],
  templateUrl: './agent-tool-mapping.component.html',
  styleUrl: './agent-tool-mapping.component.css',
  providers: [
    provideIcons({
      heroPlusMini,
      heroXCircleMini,
      heroTrashMini,
      heroArrowRight,
      heroChevronDown,
      heroChevronUp,
      heroWrench,
      heroEllipsisHorizontal,
      heroXMark,
    }),
  ],
})
export class AgentToolMappingComponent implements OnInit {
  agents = signal<Array<(IAgentDev | IAgent) & { tools?: IToolDev[] }>>([]);
  tools = signal<Array<IToolDev | ITool>>([]);
  agentToolDevs = signal<Array<IAgentToolDev>>([]);
  searchAgentTerm = signal<string>('');
  searchToolTerm = signal<string>('');
  selectingAgentId = signal<number | null>(null);
  isDragging = signal<boolean>(false);
  connectedDropLists = signal<string[]>([]);
  openToolAction = signal<number | undefined>(undefined);
  openToolAgentAction = signal<number | undefined>(undefined);
  openAgentAction = signal<number | undefined>(undefined);
  aiModels = signal<any[]>([
    { value: 'gpt-4o-mini', type: 'openai', label: 'gpt-4o-mini' },
    { value: 'gpt-4o', type: 'openai', label: 'gpt-4o' },
    { value: 'gpt-4.1-mini', type: 'openai', label: 'gpt-4.1-mini' },
    { value: 'gpt-4.1-nano', type: 'openai', label: 'gpt-4.1-nano' },
    { value: 'gpt-4.1', type: 'openai', label: 'gpt-4.1' },
    { value: 'gemini-1.5-flash', type: 'gemini', label: 'gemini-1.5-flash' },
    { value: 'gemini-2.0-flash', type: 'gemini', label: 'gemini-2.0-flash' },
  ]);

  filteredAgents = computed(() => {
    return this.agents().filter((agent) =>
      agent.name.toLowerCase().includes(this.searchAgentTerm().toLowerCase())
    );
  });
  filteredTools = computed(() => {
    return this.tools().filter((tool) =>
      tool.name.toLowerCase().includes(this.searchToolTerm().toLowerCase())
    );
  });

  modelConfig: { name: string; temperature: number } = {
    name: 'openai',
    temperature: 0,
  };
  readonly STUDIO_STATUS = STUDIO_STATUS;
  private deleteZoneId = 'delete-zone';
  private messageListener!: (event: MessageEvent) => void;

  studioStore = inject(StudioStore);
  private router = inject(Router);
  private agentDevService = inject(AgentDevService);
  private toolDevService = inject(ToolDevService);
  private agentToolDevService = inject(AgentToolDevService);
  private toolService = inject(ToolService);
  private agentService = inject(AgentService);
  private dialog = inject(DxDialog);
  private snackBar = inject(DxSnackBar);

  constructor() {
    effect(() => {
      const agentIds = this.agents().map((agent) => `agent-drop-${agent.id}`);
      this.connectedDropLists.set([...agentIds, this.deleteZoneId]);
    });

    effect(() => {
      const status = this.studioStore.status();
      this.loadData();
    });
  }

  ngOnInit(): void {
    this.studioStore.setStudioStatus(STUDIO_STATUS.DEV);
  }

  loadData(): void {
    if (this.studioStore.status() === STUDIO_STATUS.DEV) {
      forkJoin({
        agents: this.agentDevService.getAll(),
        tools: this.toolDevService.getAll(),
        agentToolDevs: this.agentToolDevService.getAll(),
      }).subscribe({
        next: ({ agents, tools, agentToolDevs }) => {
          const toolMap = new Map<number, IToolDev>();
          tools.forEach((tool) => toolMap.set(tool.id as number, tool));

          const agentToolMap = new Map<number, IToolDev[]>();
          for (const agentToolDev of agentToolDevs) {
            const tool = toolMap.get(agentToolDev.tool_dev_id);
            if (tool) {
              if (!agentToolMap.has(agentToolDev.agent_dev_id)) {
                agentToolMap.set(agentToolDev.agent_dev_id, []);
              }
              agentToolMap.get(agentToolDev.agent_dev_id)!.push(tool);
            }
          }

          const enrichedAgents = agents.map((agent) => ({
            ...agent,
            tools: agentToolMap.get(agent.id as number) || [],
          }));

          this.agents.set(enrichedAgents);
          this.tools.set(tools);
          this.agentToolDevs.set(agentToolDevs);
        },
        error: () => {
          this.snackBar.open('Failed to load data', '', {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        },
      });
    }

    if (this.studioStore.status() === STUDIO_STATUS.LIVE) {
      // TODO: Get agents, tools and agentToolVersions from API
    }
  }

  selectAgent(agentId: number | undefined): void {
    if (agentId) {
      this.selectingAgentId.update((current) =>
        current === agentId ? null : agentId
      );
    }
  }

  onDragStarted(event: CdkDragStart): void {
    this.isDragging.set(true);
    const deleteZone = document.getElementById(this.deleteZoneId);
    if (deleteZone) {
      deleteZone.classList.remove('hidden');
    }
  }

  onDragEnded(event: CdkDragEnd): void {
    this.isDragging.set(false);
    setTimeout(() => {
      const deleteZone = document.getElementById(this.deleteZoneId);
      if (deleteZone) {
        deleteZone.classList.add('hidden');
      }
    }, 300);
  }

  onDrop(event: CdkDragDrop<any>, agentId: number): void {
    // If dropping to delete zone
    if (event.container.id === this.deleteZoneId) {
      // Only handle when dragging from an agent, not from available tools
      if (event.previousContainer.id.startsWith('agent-drop-')) {
        const tool = event.previousContainer.data[event.previousIndex];
        const prevAgentId = parseInt(
          event.previousContainer.id.replace('agent-drop-', '')
        );

        this.removeToolFromAgent(prevAgentId, tool.id || 0);
      }
      return;
    }

    // Only handle when dragging from Available Tools (tool-source) to an agent
    if (event.previousContainer.id === 'tool-source') {
      const tool = event.previousContainer.data[event.previousIndex];

      if (!tool?.id) {
        this.snackBar.open('Invalid tool', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        return;
      }

      // Check if tool is already assigned to this agent
      const agent = this.agents().find((a) => a.id === agentId);
      if (agent?.tools?.some((t: ITool) => t.id === tool.id)) {
        this.snackBar.open('This tool is already assigned to this agent', '', {
          panelClass: 'dx-snack-bar-warning',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        return;
      }

      // Call API to assign tool to agent
      this.agentToolDevService.assign(agentId, tool.id).subscribe({
        next: () => {
          // Update UI - add tool to agent
          this.agents.update((agents) => {
            return agents.map((a) => {
              if (a.id === agentId) {
                // Clone the agent to avoid direct state mutation
                const updatedAgent = { ...a };
                if (!updatedAgent.tools) {
                  updatedAgent.tools = [];
                }

                // Clone tool to avoid reference issues
                const toolClone = { ...tool };

                // Add to agent tools at specified position
                updatedAgent.tools = [...updatedAgent.tools];
                updatedAgent.tools.splice(event.currentIndex, 0, toolClone);
                return updatedAgent;
              }
              return a;
            });
          });

          this.snackBar.open('Tool assigned to agent successfully', '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        },
      });
      return;
    }

    // Drag and drop between different agents
    if (
      event.previousContainer.id !== event.container.id &&
      event.previousContainer.id.startsWith('agent-drop-') &&
      event.container.id.startsWith('agent-drop-')
    ) {
      const tool = event.previousContainer.data[event.previousIndex];
      const prevAgentId = parseInt(
        event.previousContainer.id.replace('agent-drop-', '')
      );

      // Check if tool is already assigned to target agent
      const targetAgent = this.agents().find((a) => a.id === agentId);
      if (targetAgent?.tools?.some((t: ITool) => t.id === tool.id)) {
        this.snackBar.open(
          'This tool is already assigned to target agent',
          '',
          {
            panelClass: 'dx-snack-bar-warning',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          }
        );
        return;
      }

      // Remove from old agent
      this.agentToolDevService.unassign(prevAgentId, tool.id || 0).subscribe({
        next: () => {
          // Update UI - remove from old agent
          this.agents.update((agents) => {
            return agents.map((a) => {
              if (a.id === prevAgentId) {
                const updatedAgent = { ...a };
                if (updatedAgent.tools) {
                  updatedAgent.tools = updatedAgent.tools.filter(
                    (t: ITool) => t.id !== tool.id
                  );
                }
                return updatedAgent;
              }
              return a;
            });
          });

          // Add to new agent
          this.agentToolDevService.assign(agentId, tool.id).subscribe({
            next: () => {
              // Update UI - add to new agent
              this.agents.update((agents) => {
                return agents.map((a) => {
                  if (a.id === agentId) {
                    const updatedAgent = { ...a };
                    if (!updatedAgent.tools) {
                      updatedAgent.tools = [];
                    }

                    // Clone tool to avoid reference issues
                    const toolClone = { ...tool };

                    // Add to agent tools at specified position
                    updatedAgent.tools = [...updatedAgent.tools];
                    updatedAgent.tools.splice(event.currentIndex, 0, toolClone);
                    return updatedAgent;
                  }
                  return a;
                });
              });
              this.snackBar.open('Tool moved to new agent', '', {
                panelClass: 'dx-snack-bar-success',
                duration: 5000,
                verticalPosition: 'top',
                horizontalPosition: 'right',
              });
            },
            error: (_) => {
              this.snackBar.open('Unable to move tool to new agent', '', {
                panelClass: 'dx-snack-bar-error',
                duration: 5000,
                verticalPosition: 'top',
                horizontalPosition: 'right',
              });
            },
          });
        },
        error: (_) => {
          this.snackBar.open('Unable to move tool', '', {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        },
      });
      return;
    }
  }

  onEditFlowTool(tool: IToolDev | ITool) {
    void this.router.navigate([
      `${APP_ROUTES.STUDIO}/${STUDIO_PATH.AGENT_FLOW}/${AGENT_FLOW_PATH.TOOL}/${tool.id}`,
    ]);
  }

  removeToolFromAgent(agentId: number, toolId: number): void {
    this.agentToolDevService.unassign(agentId, toolId).subscribe({
      next: () => {
        this.agents.update((agents) => {
          return agents.map((a) => {
            if (a.id === agentId) {
              const updatedAgent = { ...a };
              if (updatedAgent.tools) {
                updatedAgent.tools = updatedAgent.tools.filter(
                  (tool: ITool) => tool.id !== toolId
                );
              }
              return updatedAgent;
            }
            return a;
          });
        });

        this.snackBar.open('Tool removed from agent successfully', '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
      error: (_) => {
        this.snackBar.open('Unable to remove tool from agent', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
    });
  }

  openCreateToolDialog() {
    this.dialog
      .open(AddToolComponent, {
        width: '50vw',
        data: { isEdit: false },
      })
      .afterClosed()
      .subscribe(() => {
        this.loadData();
      });
  }

  openCreateAgentDialog() {
    this.dialog
      .open(AddOrEditAgentComponent, {
        height: '80dvh',
        width: '50dvw',
        data: { isEdit: false },
      })
      .afterClosed()
      .subscribe((val) => {
        this.loadData();
      });
  }

  onPublishTool(tool: IToolDev) {
    // chưa có hàm này ở màn tools
    console.log('Publish tool:', tool);
  }

  onEditTool(tool: IToolDev) {
    this.dialog
      .open(AddToolComponent, {
        width: '50vw',
        data: {
          id: tool.id,
          name: tool.name,
          description: tool.description,
          parameters: tool.parameters,
          flow_data: tool.flow_data,
          isEdit: true,
        },
      })
      .afterClosed()
      .subscribe(() => this.loadData());
  }

  onDeleteTool(tool: IToolDev) {
    if (tool.id) {
      const id = tool.id;
      this.dialog
        .open(ConfirmDialogComponent, {
          data: {
            title: 'Delete Tool',
            content: `Are you sure you want to delete tool "${tool.name}"?`,
            confirmText: 'Delete',
            cancelText: 'Cancel',
            isDelete: true,
          },
        })
        .afterClosed()
        .subscribe((result: any) => {
          if (!!result && this.studioStore.status() === STUDIO_STATUS.DEV) {
            this.toolDevService.delete(id).subscribe({
              next: () => {
                this.snackBar.open('Tool deleted successfully', '', {
                  panelClass: 'dx-snack-bar-success',
                  duration: 5000,
                  verticalPosition: 'top',
                  horizontalPosition: 'right',
                });
                this.loadData();
              },
            });
          }
          if (result && this.studioStore.status() === STUDIO_STATUS.LIVE) {
            this.toolService.delete(id).subscribe({
              next: () => {
                this.snackBar.open('Tool deleted successfully', '', {
                  panelClass: 'dx-snack-bar-success',
                  duration: 5000,
                  verticalPosition: 'top',
                  horizontalPosition: 'right',
                });
                this.loadData();
              },
            });
          }
        });
    }
  }

  onViewAgent(agent: IAgentDev | IAgent): void {
    this.dialog.open(DetailAgentComponent, {
      width: '50dvw',
      data: { agent },
    });
  }

  onEditAgent(agent: IAgentDev) {
    let temperature = 0;
    let modelConfigName = '';

    try {
      if (agent.model_config) {
        const modelCfg = JSON.parse(agent.model_config);
        this.modelConfig = {
          name: modelCfg.name || this.aiModels()[0].value,
          temperature: modelCfg.temperature || 0,
        };
        temperature = modelCfg.temperature || 0;
        modelConfigName = modelCfg.name || '';
      } else {
        this.modelConfig = {
          name: this.aiModels()[0].value,
          temperature: 0,
        };
      }
    } catch (e) {
      temperature = 0;
      modelConfigName = '';
      this.modelConfig = {
        name: this.aiModels()[0].value,
        temperature: 0,
      };
    }

    // Find the model using model_config.name instead of llm_type
    const modelForName = this.aiModels().find(
      (model) => model.value === modelConfigName
    );
    let selectedValue = modelForName?.value || this.aiModels()[0].value;

    this.dialog
      .open(AddOrEditAgentComponent, {
        width: '50vw',
        data: {
          id: agent.id,
          ai_id: agent.ai_id,
          name: agent.name,
          description: agent.description,
          llm_type: agent.llm_type,
          instruction: agent.instruction,
          model_config: this.modelConfig,
          rule: agent.rule,
          temperature: temperature,
          selectedModel: selectedValue,
          isEdit: true,
        },
      })
      .afterClosed()
      .subscribe((val) => {
        this.loadData();
      });
  }

  onDeleteAgent(agent: IAgentDev | IAgent): void {
    if (agent.id) {
      const id = agent.id;
      this.dialog
        .open(ConfirmDialogComponent, {
          data: {
            title: 'Delete Agent',
            content: `Are you sure you want to delete agent "${agent.name}"?`,
            confirmText: 'Delete',
            cancelText: 'Cancel',
            isDelete: true,
          },
        })
        .afterClosed()
        .subscribe((result: any) => {
          if (!!result && this.studioStore.status() === STUDIO_STATUS.DEV) {
            this.agentDevService.delete(id).subscribe({
              next: () => {
                this.snackBar.open('Agent deleted successfully', '', {
                  panelClass: 'dx-snack-bar-success',
                  duration: 5000,
                  verticalPosition: 'top',
                  horizontalPosition: 'right',
                });
                this.loadData();
              },
            });
          }
          if (result && this.studioStore.status() === STUDIO_STATUS.LIVE) {
            this.agentService.delete(id).subscribe({
              next: () => {
                this.snackBar.open('Agent deleted successfully', '', {
                  panelClass: 'dx-snack-bar-success',
                  duration: 5000,
                  verticalPosition: 'top',
                  horizontalPosition: 'right',
                });
                this.loadData();
              },
            });
          }
        });
    }
  }
}
