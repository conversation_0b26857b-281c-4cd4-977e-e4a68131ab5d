import { CommonModule } from '@angular/common';
import { Component, ElementRef, input, output, OnChanges, SimpleChanges, ViewChild, AfterViewInit, inject, ChangeDetectorRef } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgIconsModule } from '@ng-icons/core';
import {DxFormField, DxInput, DxProgressSpinner, DxSuffix, DxTooltip} from '@dx-ui/ui';
import {BaseComponent, ImageGalleryComponent, ImageGridComponent, SvgIconComponent} from '@shared/components';
import { IMessage } from '@shared/models';
import { MessagesService } from '@shared/services';
import { SafeHtmlPipe } from '@shared/pipes';
import {AutosizeDirective} from '@shared/directives';

@Component({
  selector: 'app-chat-content',
  templateUrl: './chat-content.component.html',
  styleUrl: './chat-content.component.css',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    DxTooltip,
    NgIconsModule,

    SafeHtmlPipe,
    ImageGalleryComponent,
    ImageGridComponent,
    DxInput,
    AutosizeDirective,
    DxFormField,
    SvgIconComponent,
    DxProgressSpinner,
    DxSuffix
  ],
})
export class ChatContentComponent extends BaseComponent implements OnChanges, AfterViewInit {
  conversationId = input.required<string>();
  showMessageInbox = input<boolean>(false);
  isLoadingContentConversation = input<boolean>(false);
  chatMessages = input<IMessage[]>([]);
  isTyping = input<boolean>(false);
  isChatWithAI = input<boolean>(false);
  message = input<string>('');
  isSendingMessage = input<boolean>(false);
  listTagSelectedInConversation = input<any[]>([]);
  conversationStatusInfo = input<{ action: 'takeOver' | 'resume', isChatWithAI: boolean } | null>(null);

  // Simplified outputs - only emit essential events that parent needs
  messageChange = output<string>();
  addTag = output<void>();
  openSaveFAQDialog = output<IMessage>();
  messagesUpdated = output<IMessage[]>();
  sendingStatusChange = output<boolean>();
  reloadChatMessages = output<void>();

  @ViewChild('scrollContainer', { static: false }) scrollContainer!: ElementRef;

  private messagesService = inject(MessagesService);
  public override cdr = inject(ChangeDetectorRef);

  ngAfterViewInit(): void {
    // ViewChild will be available here
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['conversationId'] && this.conversationId()) {
      // Clear message input when conversation changes
      this.messageChange.emit('');

      // Reset textarea height to minimum
      setTimeout(() => {
        const textarea = document.getElementById('chat') as HTMLTextAreaElement;
        if (textarea) {
          textarea.style.height = '40px';
        }
      }, 0);

      // Load messages when conversationId changes
      setTimeout(() => {
        this.scrollToBottomOfNgScrollBar();
      }, 100);
    }

    const chatMessages = this.chatMessages();
    if (changes['chatMessages'] && chatMessages && chatMessages.length > 0) {
      // Normalize messages to ensure all required properties exist
      const normalizedMessages = chatMessages.map(msg => ({
        ...msg,
        listImages: msg.listImages || [],
        listTextSources: msg.listTextSources || []
      }));

      setTimeout(() => {
        this.scrollToBottomOfNgScrollBar();
      }, 100);
    }

    // Handle conversation status changes (take over / resume)
    const statusInfo = this.conversationStatusInfo();
    if (changes['conversationStatusInfo'] && statusInfo) {
      this.handleConversationStatusChange(statusInfo);
    }
  }

  private handleConversationStatusChange(statusInfo: { action: 'takeOver' | 'resume', isChatWithAI: boolean }): void {
    // Note: isChatWithAI is now an input signal, so we don't update it directly
    // The parent component will handle updating this value

    // Show notification based on action
    if (statusInfo.action === 'takeOver') {
      this.showSnackBar('Conversation has been taken over successfully', "info");
    } else if (statusInfo.action === 'resume') {
      this.showSnackBar('Conversation has been resumed by AI successfully', "info");
    }

    // You can add additional logic here if needed:
    // - Disable/enable message input based on isChatWithAI
    // - Update UI indicators
    // - Show different message templates

    this.cdr.detectChanges();
  }

  // Handle keyboard events locally
  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.handleSendMessage();
    }
  }

  // Handle send message directly without emitting
  onSendMessage(): void {
    this.handleSendMessage();
  }

  // Internal method to handle message sending
  private handleSendMessage(): void {
    const message = this.message();
    const conversationId = this.conversationId();
    const isSendingMessage = this.isSendingMessage();

    if (!message || message.trim() === '' || isSendingMessage || !conversationId) return;

    this.sendingStatusChange.emit(true);

    const body = {
      author: this.author,
      content: message.trim(),
      internal: false,
      debug: false,
    };

    console.log(body);

    // Call the API
    this.messagesService
      .replyMessageByHuman(conversationId, body)
      .subscribe({
        next: (res) => {
          if (res.message) {
            // Clear message input
            this.messageChange.emit('');

            // Emit event to reload chat messages from server
            this.reloadChatMessages.emit();
          }
          this.sendingStatusChange.emit(false);
        },
        error: (err) => {
          this.sendingStatusChange.emit(false);
          this.showSnackBar('Failed to send message', "error");
          console.error('Error sending message:', err);
        },
      });
  }
  author: any = {
    type: 'human_operator',
  };




  onMessageChange(value: string): void {
    this.messageChange.emit(value);
  }

  onAddTag(): void {
    this.addTag.emit();
  }

  onOpenSaveFAQDialog(message: IMessage): void {
    this.openSaveFAQDialog.emit(message);
  }

  onSendMessageAndScroll(): void {
    this.handleSendMessage();
  }

  updateTextAreaHeight() {
    // Calculate the number of rows in the textarea based on the content
    const textarea = document.getElementById('chat') as HTMLTextAreaElement;
    if (textarea) {
      const maxHeight = 200; // Maximum height in pixels
      const minHeight = 40; // Minimum height in pixels

      textarea.style.height = 'auto'; // Reset the height to auto
      const scrollHeight = textarea.scrollHeight;

      if (scrollHeight <= maxHeight) {
        // If content fits within max height, expand textarea
        textarea.style.height = Math.max(scrollHeight, minHeight) + 'px';
      } else {
        // If content exceeds max height, set to max height and let it scroll
        textarea.style.height = maxHeight + 'px';
      }
    }

    if (textarea && this.message().trim() === '') {
      textarea.style.height = '40px'; // Reset to minimum height when empty
    }
  }

  scrollToBottomOfNgScrollBar(): void {
    // Simple native scroll approach
    setTimeout(() => {
      try {
        if (this.scrollContainer && this.scrollContainer.nativeElement) {
          const element = this.scrollContainer.nativeElement;
          element.scrollTop = element.scrollHeight;
        }
      } catch (error) {
        console.warn('Error scrolling to bottom:', error);
      }
    }, 100);
  }

  getColor(config: string): string {
    try {
      const parsedConfig = JSON.parse(config);
      return parsedConfig.color || '#000';
    } catch (error) {
      console.error('Invalid config:', config);
      return '#000'; // Default color nếu lỗi
    }
  }

  getTextColor(bgColor: string): string {
    const hexToRgb = (hex: string) => ({
      r: parseInt(hex.substring(0, 2), 16),
      g: parseInt(hex.substring(2, 4), 16),
      b: parseInt(hex.substring(4, 6), 16),
    });

    const calculateTextColor = ({ r, g, b }: { r: number, g: number, b: number }): string => {
      const adjustComponent = (comp: number) => Math.floor(comp * 0.4);

      const textRgb = {
        r: adjustComponent(r),
        g: adjustComponent(g),
        b: adjustComponent(b),
      };

      return `#${textRgb.r.toString(16).padStart(2, '0')}${textRgb.g.toString(16).padStart(2, '0')}${textRgb.b.toString(16).padStart(2, '0')}`;
    };

    const rgb = hexToRgb(bgColor.replace('#', ''));
    return calculateTextColor(rgb);
  }

  removeTagsHTML(input: string): string {
    return input.replace(/<[^>]*>/g, '');
  }

  findNearestUserObjectBefore(currentObject: IMessage): any {
    const chatMessages = this.chatMessages();
    const currentIndex = chatMessages.indexOf(currentObject);
    for (let i = currentIndex - 1; i >= 0; i--) {
      if (chatMessages[i].role === 'user') {
        return chatMessages[i];
      }
    }
    return null;
  }
}
