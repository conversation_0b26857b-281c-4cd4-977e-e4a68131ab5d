.typing-animation {
  height: 20px;
  width: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dot {
  height: 8px;
  width: 8px;
  background-color: #333;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}

.overflow-wrap {
  overflow-wrap: anywhere;
}

textarea::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

textarea {
  resize: none;
}
