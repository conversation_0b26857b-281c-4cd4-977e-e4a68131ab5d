import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>Case, NgS<PERSON>Def<PERSON>,} from '@angular/common';
import {Component, inject, OnInit, signal} from '@angular/core';
import {FormsModule} from '@angular/forms';
import {DIALOG_DATA, DxButton, DxDialog, DxDialogRef, DxFormField, DxInput, DxLabel,} from '@dx-ui/ui';
import {NgIcon, provideIcons} from '@ng-icons/core';
import {heroPencilSquare, heroXCircle, heroXMark} from '@ng-icons/heroicons/outline';
import {DataTableComponent, IColumn,} from '@shared/components';
import {IUserInfo} from '@shared/models';
import {SettingsService} from '@shared/services';
import {EditAiUserComponent} from '@views/admin/pages/list-user/edit-ai-user/edit-ai-user.component';

@Component({
  selector: 'app-list-user-ais',
  imports: [
    <PERSON>TableComponent,
    DatePipe,
    DxLabel,
    NgIcon,
    NgSwitchCase,
    FormsModule,
    NgStyle,
    NgSwitch,
    NgSwitchDefault,
    DxFormField,
    DxInput,
    DxButton,
  ],
  providers: [
    provideIcons({
      heroXCircle,
      heroXMark,
      heroPencilSquare,
    }),
  ],
  templateUrl: './list-user-ais.component.html',
  styleUrl: './list-user-ais.component.css',
  host: {
    class: 'h-full',
  },
})
export class ListUserAisComponent implements OnInit {
  isLoading = signal<boolean>(false);

  filterAI: { user_id: number; name: string } = { user_id: 0, name: '' };
  count: number = 0;
  listAI = signal<any[]>([]);
  columnsAI: IColumn[] = [
    {
      columnDef: 'index',
      headerName: 'No.',
      flex: 0.1,
      minWidth: '40px',
    },
    {
      columnDef: 'name',
      headerName: 'Name',
      flex: 0.4,
      minWidth: '40px',
    },
    {
      columnDef: 'created_at',
      headerName: 'Created at',
      flex: 0.3,
      minWidth: '40px',
    },
    {
      columnDef: 'message_numb',
      headerName: 'Message used',
      flex: 0.1,
      minWidth: '40px',
    },
    {
      columnDef: 'action_edit',
      headerName: 'Action',
      flex: 0.2,
      minWidth: '120px',
      alignHeader: 'center',
      align: 'center',
    },
  ];
  aiChangeLLMId: any;

  dialogRef = inject(DxDialogRef<ListUserAisComponent>);
  data: { listAI: any; user: IUserInfo } = inject(DIALOG_DATA);
  private dialog = inject(DxDialog);
  private settingsService: SettingsService = inject(SettingsService);

  ngOnInit(): void {
    if (this.data.user.id != null) {
      this.filterAI.user_id = this.data.user.id;
      // this.listAI.set(this.data.listAI);
    }
    this.filterAIList();
  }

  editAI(ai: any) {
    this.aiChangeLLMId = ai.id;
    this.dialog.open(EditAiUserComponent, {
      data: {
        aiId: ai.id,
      },
      width: '60vw',
    });
  }

  filterAIList() {
    this.settingsService.getListAIOwn(this.filterAI).subscribe({
      next: (res) => {
        this.listAI.set(res);
        this.count = res.total;
      },
      error: () => {},
    });
  }
}
