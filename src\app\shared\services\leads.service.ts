import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';

export interface ICustomer {
  ai_id?: string;
  user_name: string;
  user_id?: string;
  phone_number?: string;
  email?: string;
  status: string;
  data_info?: string;
  updated_at?: string;
}

export interface ICustomerResponse {
  total: number;
  items: ICustomer[];
}

export interface ICustomerParams {
  page?: number;
  page_size?: number;
  search?: string;
  status?: string;
}

@Injectable({
  providedIn: 'root',
})
export class LeadsService {
  private http = inject(HttpClient);

  private apiUrl = '/api/customer';

  getCustomers(params: ICustomerParams): Observable<ICustomerResponse> {
    let httpParams = new HttpParams();

    if (params.page) {
      httpParams = httpParams.set('page', params.page.toString());
    }
    if (params.page_size) {
      httpParams = httpParams.set('page_size', params.page_size.toString());
    }
    if (params.search) {
      httpParams = httpParams.set('keyword', params.search);
    }
    if (params.status) {
      httpParams = httpParams.set('status', params.status);
    }

    return this.http.get<ICustomerResponse>(this.apiUrl + '/', {
      params: httpParams,
    });
  }

  createCustomer(customer: Partial<ICustomer>): Observable<ICustomer> {
    return this.http.post<ICustomer>(`${this.apiUrl}/create`, customer);
  }

  updateCustomer(
    userId: string,
    customer: Partial<ICustomer>
  ): Observable<ICustomer> {
    return this.http.put<ICustomer>(`${this.apiUrl}/update`, customer);
  }

  deleteCustomer(userId: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${userId}`);
  }

  getCustomerById(userId: string): Observable<ICustomer> {
    return this.http.get<ICustomer>(`${this.apiUrl}/${userId}`);
  }
}
