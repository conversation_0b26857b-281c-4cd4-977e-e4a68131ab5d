<div class="settings-tab-content w-full p-6 flex flex-col space-y-6">
  <div class="flex items-center space-x-2">
    <div
      class="text-xl font-bold text-base-content dark:text-dark-base-content"
    >
      Widget settings
    </div>
  </div>
  <hr class="text-primary-border dark:text-dark-primary-border" />
  <div class="flex flex-col lg:w-3/4">
    <dx-form-field>
      <dx-label class="flex items-center space-x-1">
        <div>Logo's url</div>
        <ng-icon
          name="heroInformationCircle"
          class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
          dxTooltip="This will be shown in the AI's avatar of the chat window and preview."
          dxTooltipPosition="right"
        ></ng-icon>
      </dx-label>
      <app-svg-icon
        dxPrefix
        type="icLinkPrefix"
        class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
      ></app-svg-icon>
      <input dxInput placeholder="Paste url here" [formControl]="logo_url" />
    </dx-form-field>
    <dx-form-field>
      <dx-label class="flex items-center space-x-1">
        <div>Header</div>
        <ng-icon
          name="heroInformationCircle"
          class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
          dxTooltip="This will be shown at the top of the chat window and preview."
          dxTooltipPosition="right"
        ></ng-icon>
      </dx-label>
      <textarea
        dxInput
        id="settings-header"
        placeholder="Enter the header text to display at the top of the chat widget"
        [formControl]="header"
        (valueChange)="updateTextAreaHeight('settings-header')"
      ></textarea>
    </dx-form-field>
    <dx-form-field>
      <dx-label class="flex items-center space-x-1">
        <div>Welcome message</div>
        <ng-icon
          name="heroInformationCircle"
          class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
          dxTooltip="This will be the first message written by your chat bot."
          dxTooltipPosition="right"
        ></ng-icon>
      </dx-label>
      <textarea
        dxInput
        id="settings-welcome-message"
        placeholder="Write a welcome message that will greet users when they open the chat"
        [formControl]="welcome_message"
        (valueChange)="updateTextAreaHeight('settings-welcome-message')"
      ></textarea>
    </dx-form-field>
    <dx-form-field>
      <dx-label class="flex items-center space-x-1">
        <div>Chat input placeholder</div>
        <ng-icon
          name="heroInformationCircle"
          class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
          dxTooltip="This will be shown in the empty message box."
          dxTooltipPosition="right"
        ></ng-icon>
      </dx-label>
      <textarea
        dxInput
        id="settings-input-placeholder"
        placeholder="Type a placeholder message to guide users when entering their question"
        [formControl]="input_placeholder"
        (valueChange)="updateTextAreaHeight('settings-input-placeholder')"
      ></textarea>
    </dx-form-field>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div class="flex items-center mb-4">
    <div
      class="text-lg font-bold text-base-content dark:text-dark-base-content mr-2"
    >
      Whitelist
    </div>
    <ng-icon
      name="heroInformationCircle"
      class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
      dxTooltip="List of domains are allowed to embed your AI Asisstant."
      dxTooltipPosition="right"
    ></ng-icon>
  </div>

  <div class="flex flex-col lg:w-3/4">
    <dx-form-field [subscriptHidden]="true">
      <dx-label>Add domain to whitelist</dx-label>
      <app-svg-icon
        dxPrefix
        type="icLinkPrefix"
        class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
      ></app-svg-icon>
      <input
        dxInput
        placeholder="Paste url here"
        [formControl]="whitelistInput"
        (keyup.enter)="addWhitelistUrl()"
      />
    </dx-form-field>
    @if (whitelist.value && whitelist.value.length > 0) {
    <div class="flex flex-wrap gap-2">
      @for (url of whitelist.value; track url; let index = $index) {
      <div
        class="flex items-center space-x-2 px-4 py-1 bg-base-100 dark:bg-dark-base-100 rounded-[12px]"
      >
        <div class="text-sm text-base-content dark:text-dark-base-content">
          {{ url }}
        </div>
        <ng-icon
          name="heroXCircleSolid"
          class="!text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          (click)="removeWhitelistUrl(index)"
        ></ng-icon>
      </div>
      }
    </div>
    }
  </div>
</div>
