// @ts-nocheck
import { flowApi, flowDevApi } from "@flow-editor-v1/api";
import SelectField from "@flow-editor-v1/components/form/SelectField";
import NodeHeader from "@flow-editor-v1/components/nodes/NodeHeader";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor-v1/components/styled";
import NodeTooltip from "@flow-editor-v1/components/tooltip/NodeTooltip";
import { STUDIO_STATUS } from "@flow-editor-v1/constant";
import { useFlowInstance } from "@flow-editor-v1/hook";
import { ChatFlow, ChatFlowDev, FlowDebugState, LayoutState, SwitchFlowNodeData } from "@flow-editor-v1/model";
import { useBuildFlowState, useFlowDebugState, useFlowInstanceState, useLayoutState, useStudioState } from "@flow-editor-v1/store";
import { isValidConnection } from "@flow-editor-v1/utils/flow";
import { hexToRgb } from "@flow-editor-v1/utils/util";
import { yupResolver } from "@hookform/resolvers/yup";
import { Modal, Typography } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Handle, Position } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";

const StyledHandleSourceAnchor = styled.div<{ $bgColor; $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => (props.$data ? "white" : `rgba(255, 255, 255, 0.5)`)};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => (props.$data ? "normal" : "italic")};
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const switchFlowNodeDataFormSchema = yup.object().shape({
  flow_id: yup.number(),
  flow_dev_id: yup.number(),
});

const SwitchFlowNode = ({data}: { data: SwitchFlowNodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [flows, setFlows] = useState<Array<ChatFlow | ChatFlowDev>>([]);
  const {flowInstance} = useFlowInstanceState((state) => state);
  const {flow, setDirty} = useBuildFlowState((state) => state);
  const {status} = useStudioState((state) => state);
  const {debug} = useFlowDebugState<FlowDebugState>(state => state);
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const {theme} = useLayoutState<LayoutState>(state => state);

  const {control, setValue, handleSubmit, reset, getValues} = useForm({
    resolver: yupResolver(switchFlowNodeDataFormSchema),
  });

  const resetForm = () => {
    reset();
  };

  const onSubmit = (formValue) => {
    if (status && status === STUDIO_STATUS.LIVE)
      data.flow_id = formValue?.flow_id;
    if (status && status === STUDIO_STATUS.DEV)
      data.flow_dev_id = formValue?.flow_dev_id;
    resetForm();
    setModalOpen(false);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  };

  useEffect(() => {
    resetForm();
    data.flow_id && setValue("flow_id", data.flow_id);
    data.flow_dev_id && setValue("flow_dev_id", data.flow_dev_id);
  }, [JSON.stringify(data), JSON.stringify(flows)]);

  useEffect(() => {
    const fetchFlows = async () => {
      let res: ChatFlow[] | ChatFlowDev[];
      if (status && status === STUDIO_STATUS.LIVE)
        res = await flowApi.getListFlow({trigger_type: flow.trigger_type});
      if (status && status === STUDIO_STATUS.DEV)
        res = await flowDevApi.getListFlowDev({trigger_type: flow.trigger_type});
      setFlows(res);
    };

    void fetchFlows();
  }, [JSON.stringify(flow), status]);

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    if (!data.debug) setModalOpen(true);
  }, [data.debug]);

  const handleGoToFlow = (formValue) => {
    const newFlowId = formValue.flow_dev_id ?? formValue.flow_id;
    window.postMessage({type: 'GO_TO_FLOW', payload: newFlowId}, '*');
  };
  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $debug={data.debug} $theme={theme}>
      {
        debug && (
          <div className="absolute left-0 text-[10px]" style={{top: -20, color: '#D4D9E2'}}>{data.id}</div>
        )
      }
      {status && status === STUDIO_STATUS.DEV && (
        <NodeTooltip data={data} selected={data.selected}/>
      )}
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 6,
            width: 6,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
        <StyledHandleSourceAnchor
          $bgColor={data.node_color}
          $data={!!data.flow_id}
          onDoubleClick={handleOpenModal}
        >
          {status && status === STUDIO_STATUS.LIVE
            ? data.flow_id
              ? flows.find((flow) => flow.id === data.flow_id)?.name ||
              "Configure"
              : "Configure"
            : status && status === STUDIO_STATUS.DEV
              ? data.flow_dev_id
                ? flows.find((flow) => flow.id === data.flow_dev_id)?.name ||
                "Configure"
                : "Configure"
              : null}
        </StyledHandleSourceAnchor>
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-col space-y-4 mt-6"
        >
          {status && status === STUDIO_STATUS.LIVE && (
            <div className="flex flex-col space-y-2">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Select flow <span style={{color: 'red'}}>*</span>
              </Typography.Text>
              <SelectField
                name={"flow_id"}
                control={control}
                options={
                  flows.length > 0
                    ? flows.map((v) => ({
                      ...v,
                      key: v.id,
                      value: v.id,
                      label: v.name,
                      disabled: v.id == flow.id,
                    }))
                    : []
                }
              />
            </div>
          )}
          {status && status === STUDIO_STATUS.DEV && (
            <div className="flex flex-col space-y-2">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Select flow <span style={{color: 'red'}}>*</span>
              </Typography.Text>
              <SelectField
                name={"flow_dev_id"}
                control={control}
                options={
                  flows.length > 0
                    ? flows.filter(v => v.id !== flow.id).map((v) => ({
                      ...v,
                      key: v.id,
                      value: v.id,
                      label: v.name,
                      disabled: v.id == flow.id,
                    }))
                    : []
                }
              />
            </div>
          )}

          <div
            className="w-full flex justify-between items-center space-x-4"
            style={{marginTop: 24}}
          >
            <div>
              <StyledButtonModal
                type="button"
                key="go to flow"
                $bgColor={theme === "dark" ? "white" : "#ececec"}
                $color={"black"}
                onClick={() => handleGoToFlow(getValues())}
              >
                Go to flow
              </StyledButtonModal>
            </div>
            <div className={"flex justify-end items-center space-x-4"}>
              <StyledButtonModal
                type="button"
                key="cancel"
                $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
                $color={theme === 'light' ? '#282826' : '#F8FAFF'}
                $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
                onClick={() => setModalOpen(false)}
              >
                Cancel
              </StyledButtonModal>
              <StyledButtonModal
                type="submit"
                key="save"
                $bgColor={"#7F75CF"}
                $color={"white"}
                disabled={status && status === STUDIO_STATUS.LIVE}
              >
                Save
              </StyledButtonModal>
            </div>

          </div>
        </form>
      </Modal>
    </StyledNodeFlow>
  );
};

export default SwitchFlowNode;
