import { NgClass } from '@angular/common';
import { Component, effect, inject, OnInit, signal } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ROLE_ACCOUNT } from '@core/constants';
import { UserAiStore } from '@core/stores';
import {
  DIALOG_DATA,
  DxButton,
  DxCheckbox,
  DxDialogRef,
  DxError,
  DxFormField,
  DxInput,
  DxLabel, DxLoadingButton,
  DxOption,
  DxProgressSpinner,
  DxSelect,
  DxSnackBar,
  DxSuffix,
} from '@dx-ui/ui';
import { NgIcon, provideIcons } from '@ng-icons/core';
import {
  heroArrowTopRightOnSquare,
  heroCheckCircle,
  heroXCircle,
  heroXMark,
} from '@ng-icons/heroicons/outline';
import { SelectOption } from '@shared/components';
import { NonNegativeDirective, TrimStringDirective } from '@shared/directives';
import { PlanService, UserService } from '@shared/services';

@Component({
  selector: 'app-add-or-edit-plan',
  imports: [
    ReactiveFormsModule,
    NonNegativeDirective,
    NgIcon,
    DxFormField,
    DxLabel,
    DxInput,
    DxError,
    DxSelect,
    DxOption,
    TrimStringDirective,
    DxProgressSpinner,
    NgClass,
    DxSuffix,
    DxCheckbox,
    DxButton,
    DxLoadingButton,
  ],
  providers: [
    provideIcons({
      heroXCircle,
      heroXMark,
      heroCheckCircle,
      heroArrowTopRightOnSquare,
    }),
  ],
  templateUrl: './add-or-edit-plan.component.html',
  styleUrl: './add-or-edit-plan.component.css',
  host: {
    class: 'h-full',
  },
})
export class AddOrEditPlanComponent implements OnInit {
  isLoading = signal(false);
  listFeature = signal<any[]>([]);
  loadingCheckEmail = false;

  durationSelectOptions: SelectOption[] = [
    {
      label: 'Monthly',
      value: 'monthly',
    },
    {
      label: 'Yearly',
      value: 'yearly',
    },
  ];
  durationOptions: SelectOption[] = [
    {
      label: 'All',
      value: '',
    },
    {
      label: 'Monthly',
      value: 'monthly',
    },
    {
      label: 'Yearly',
      value: 'yearly',
    },
  ];
  currencyOptions: SelectOption[] = [
    {
      label: 'USD',
      value: 'USD',
    },
    {
      label: 'MYR',
      value: 'MYR',
    },
    {
      label: 'VND',
      value: 'VND',
    },
  ];
  scopeOptions: SelectOption[] = [
    {
      label: 'Public',
      value: 'public',
    },
    {
      label: 'Custom',
      value: 'custom',
    },
  ];

  isEmailScopeValid = signal(true); //false;
  regexStripeLink: string = '/^price_[S]+S/';
  currentUser: any = { email: '', role: ROLE_ACCOUNT.ADMIN };

  formGroup: FormGroup = inject(FormBuilder).group({
    id: [null],
    name: [null, [Validators.required, Validators.maxLength(50)]],
    price: [null, [Validators.required, Validators.max(***********)]],
    duration: ['monthly'],
    currency: ['USD'],
    stripe_payment_link: [null, [Validators.pattern(this.regexStripeLink)]],
    scope: ['public', [Validators.required]],
    valueScope: ['public', [Validators.required]],
  });

  dialogRef = inject(DxDialogRef<AddOrEditPlanComponent>);
  fb = inject(FormBuilder);
  data: { isCreate: boolean; isClone: boolean; plan: any } =
    inject(DIALOG_DATA);
  planService = inject(PlanService);
  private userStore = inject(UserAiStore);
  private userService = inject(UserService);
  private snackBar = inject(DxSnackBar);

  constructor() {
    /*effect(() => {
      this.initFormGroup();
    });*/
  }

  ngOnInit(): void {
    this.getCurrentUser();
    this.getAllFeatures();
    // this.initFormGroup();
  }

  initFormGroup() {
    const formGroupControls: Record<string, any> = {};
    this.listFeature().forEach((feature) => {
      formGroupControls[feature.id] = [null];
    });
    this.formGroup.addControl('features', this.fb.group(formGroupControls));
    if (this.data.isClone) {
      this.formGroup
        .get('name')
        ?.setValue(this.formGroup.get('name')?.value + '-(Copy)');
      if (this.currentUser.role !== ROLE_ACCOUNT.ADMIN) {
        this.formGroup.get('scope')?.setValue('custom');
        this.formGroup.get('scope')?.disable();
      }
    }
    if (this.currentUser.role == ROLE_ACCOUNT.ADMIN) {
      this.formGroup.patchValue({
        scope: 'public',
        valueScope: 'public',
      });
    }
    this.formGroup.get('scope')?.valueChanges.subscribe((value) => {
      if (value === 'public') {
        this.formGroup.patchValue({
          valueScope: 'public',
        });
        this.formGroup.get('valueScope')?.removeValidators([Validators.email]);
        this.formGroup.get('valueScope')?.updateValueAndValidity();
        this.isEmailScopeValid.set(true);
      } else {
        // Initialize with empty string instead of null to avoid 'undefined' display
        this.formGroup.patchValue({
          valueScope: '',
        });
        this.formGroup.get('valueScope')?.setValidators([Validators.email]);
        this.formGroup.get('valueScope')?.updateValueAndValidity();
        this.isEmailScopeValid.set(false);
      }
    });
    if (!this.data.isCreate) {
      this.patchValueForm(this.data.plan);
    }
  }

  getAllFeatures() {
    this.planService.getAllFeatures().subscribe({
      next: (res) => {
        this.listFeature.set(res);
        this.initFormGroup();
      },
    });
  }

  closeDialogCreateOrUpdatePlan() {
    this.formGroup.reset();
    this.dialogRef.close();
  }

  private getCurrentUser() {
    if (this.userStore.currentUser()) {
      const roleValue = this.userStore.currentUser()?.role;
      const roleKey = Object.keys(ROLE_ACCOUNT).find(
        (key) => ROLE_ACCOUNT[key as keyof typeof ROLE_ACCOUNT] === roleValue
      ) as keyof typeof ROLE_ACCOUNT;
      this.currentUser = {
        email: this.userStore.currentUser()?.email || '',
        role: ROLE_ACCOUNT[roleKey] || ROLE_ACCOUNT.USER,
      };
    }
  }

  checkEmailExist(event: Event) {
    event.preventDefault();
    event.stopPropagation();
    const valueScope = this.formGroup.get('valueScope')?.value;
    if (valueScope && valueScope.trim() !== '') {
      this.loadingCheckEmail = true;
      const params = {
        email: valueScope,
      };
      this.userService.checkEmailExist(params).subscribe({
        next: (res: any) => {
          if (res) {
            this.isEmailScopeValid.set(true);
          }
        },
        error: (err: any) => {
          this.isEmailScopeValid.set(false);
          this.showSnackBar(err.error.detail, 'error');
        },
        complete: () => {
          this.loadingCheckEmail = false;
        },
      });
    }
  }

  clearEmailExist(event: Event) {
    event.preventDefault();
    event.stopPropagation();
    this.isEmailScopeValid.set(false);
    this.formGroup.patchValue({
      valueScope: '',
    });
  }

  get formGroupFeature(): FormGroup {
    return this.formGroup.get('features') as FormGroup;
  }

  createOrUpdatePlan(data: any, isClone = false) {
    const body = {
      ...data,
      features: this.convertObjectFeatureToArrayFeature(data.features),
      scope: data.valueScope,
    };

    if (isClone) {
      data.id = null;
    }
    const isCreation = !data.id;
    this.isLoading.set(true);
    if (isCreation) {
      this.planService.createPlan(body).subscribe({
        next: (res) => {
          if (res) {
            this.isLoading.set(false);
            this.showSnackBar(res.message, 'success');
            this.closeDialogCreateOrUpdatePlan();
          }
        },
        error: (err) => {
          this.isLoading.set(false);
          // this.showSnackBar('Create plan failed!', 'error');
        },
      });
    } else {
      this.planService.updatePlan(body).subscribe({
        next: (res) => {
          if (res) {
            this.isLoading.set(false);
            this.showSnackBar(res.message, 'success');
            this.closeDialogCreateOrUpdatePlan();
          }
        },
        error: (err) => {
          this.isLoading.set(false);
          // this.showSnackBar('Update plan failed!', 'error');
        },
      });
    }
  }

  convertObjectFeatureToArrayFeature(obj: any): any[] {
    const result: any[] = [];
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (obj[key]) {
          result.push({
            id: +key,
            value: typeof obj[key] === 'boolean' ? '1' : String(obj[key]),
          });
        }
      }
    }
    return result;
  }

  private showSnackBar(message: string, type: 'success' | 'error') {
    if (type === 'success') {
      this.snackBar.open(message, '', {
        panelClass: 'dx-snack-bar-success',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }

    if (type === 'error') {
      this.snackBar.open(message, '', {
        panelClass: 'dx-snack-bar-error',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }
  }

  private patchValueForm(plan: any) {
    if (plan) {
      const features: Record<string, any> = {};
      plan.features.forEach((item: any) => {
        if (!features[item.feature.id]) {
          features[item.feature.id] =
            item.feature.type === 'Quantity'
              ? Number(item.value)
              : item.value === '1';
        }
      });
      this.formGroupFeature.patchValue(features);
      this.formGroup.patchValue({
        id: plan.id,
        name: plan.name,
        price: plan.price,
        duration: plan.duration,
        currency: plan.currency,
        stripe_payment_link: plan.stripe_payment_link,
        scope: plan.scope === 'public' ? 'public' : 'custom',
        valueScope: plan.scope === 'public' ? 'public' : plan.scope || '',
      });
    }
  }
}
