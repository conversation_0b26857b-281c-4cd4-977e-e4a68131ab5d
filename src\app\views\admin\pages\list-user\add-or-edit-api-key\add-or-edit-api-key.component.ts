import {Component, inject, signal} from '@angular/core';
import {DIALOG_DATA, DxButton, DxDialogRef, DxFormField, DxInput, DxLabel, DxLoadingButton} from '@dx-ui/ui';
import {FormsModule} from '@angular/forms';
import {AiLlmService} from '@shared/services';
import {NgIcon, provideIcons} from '@ng-icons/core';
import {heroXMark} from '@ng-icons/heroicons/outline';

@Component({
  selector: 'app-add-or-edit-api-key',
  imports: [
    DxButton,
    DxFormField,
    DxInput,
    DxLabel,
    DxLoadingButton,
    FormsModule,
    NgIcon
  ],
  providers: [
    provideIcons({
      heroXMark,
    })
  ],
  templateUrl: './add-or-edit-api-key.component.html',
  styleUrl: './add-or-edit-api-key.component.css'
})
export class AddOrEditApiKeyComponent {

  isLoading = signal<boolean>(false);

  dialogRef = inject(DxDialogRef<AddOrEditApiKeyComponent>);
  data: {aiId: number|string, llmName: string, apiKey: string, id: number} = inject(DIALOG_DATA);
  private aiLLMService: AiLlmService = inject(AiLlmService);

  saveNewLLM(data: any) {
    this.isLoading.set(true);
    const newLLM = {
      id: data.id,
      ai_id: data.AIId,
      llm_name: data.llmName,
      api_key: data.apiKey,
    };

    if (data.id) {
      this.aiLLMService.updateLLM(newLLM).subscribe({
        next: () => {
          this.closeAddOrEditApikey()
        },
        error: () => {
        }
      });
    } else {
      this.aiLLMService.createLLM(newLLM).subscribe({
        next: () => {
          this.closeAddOrEditApikey()
        },
        error: () => {
        }
      });
    }
  }
  closeAddOrEditApikey() {
    this.isLoading.set(false);
    this.dialogRef.close();
  }
}
