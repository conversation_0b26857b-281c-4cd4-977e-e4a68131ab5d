// @ts-nocheck
import GoTo<PERSON>lockHandle from "@flow-editor-v1/components/flow/GoToBlockHandle";
import SelectField from "@flow-editor-v1/components/form/SelectField";
import NodeHeader from "@flow-editor-v1/components/nodes/NodeHeader";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor-v1/components/styled";
import NodeTooltip from "@flow-editor-v1/components/tooltip/NodeTooltip";
import { ERROR_MESSAGES, STUDIO_STATUS } from "@flow-editor-v1/constant";
import { useFlowInstance } from "@flow-editor-v1/hook";
import { ApiNodeData, ApiState, FlowDebugState, LayoutState, VariableDefState, VariableLocatedState } from "@flow-editor-v1/model";
import {
  useApiState,
  useBuildFlowState,
  useFlowDebugState,
  useFlowInstanceState,
  useLayoutState,
  useStudioState,
  useVariableDefState,
  useVariableLocatedState
} from "@flow-editor-v1/store";
import { isValidConnection } from "@flow-editor-v1/utils/flow";
import { hexToRgb } from "@flow-editor-v1/utils/util";
import { yupResolver } from "@hookform/resolvers/yup";
import { Divider, Modal, Select, Typography } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useFieldArray, useForm, useWatch } from "react-hook-form";
import { Handle, Position, useReactFlow } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";
import { VARIABLE_POSITION_TYPE } from "../../../constant/variable";

const StyledHandleSourceAnchor = styled.div<{ $bgColor, $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => props.$data ? 'white' : `rgba(255, 255, 255, 0.5)`};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => props.$data ? 'normal' : 'italic'};
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const StyledHandleSourceAnchorStatus = styled.div<{ $type? }>`
  margin-top: 8px;
  padding: 0 6px;
  text-align: start;
  color: ${(props) => props.$type === 'success' ? '#66b266' : props.$type === 'fallback' ? '#ff6666' : props.$type === 'error' ? '#fa6505' :'white'};
  font-size: 10px;
  font-weight: 600;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: end;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const errorsFormSchema = yup.object().shape({
  key: yup.string(),
  value: yup.string()
})

const apiNodeDataFormSchema = yup.object().shape({
  api_id: yup.number().required('Api required'),
  var_store_id: yup.number().nullable(),
  success: yup.string(),
  fallback: yup.string(),
  errors: yup.array().of(errorsFormSchema),
});

const ApiNode = ({data}: { data: ApiNodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const {flowInstance} = useFlowInstanceState(state => state);
  const {variablesDev} = useVariableDefState<VariableDefState>(state => state);
  const {apis} = useApiState<ApiState>(state => state);
  const {flow, setDirty} = useBuildFlowState(state => state);
  const {status} = useStudioState(state => state);
  const {debug} = useFlowDebugState<FlowDebugState>(state => state);
  const {setVariableLocation} = useVariableLocatedState<VariableLocatedState>(state => state);
  const [selectedItems, setSelectedItems] = useState([]);
  const {getEdges, setEdges} = useReactFlow();
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const {theme} = useLayoutState<LayoutState>(state => state);

  const {
    control,
    setValue,
    handleSubmit,
    reset
  } = useForm({
    resolver: yupResolver(apiNodeDataFormSchema)
  })

  const {
    fields: errorFields,
    append: errorAppend,
    remove: errorRemove,
    replace: errorReplace,
  } = useFieldArray({
    control,
    name: "errors",
  });

  const errorsFormArray = useWatch({
    control,
    name: "errors",
  });

  const resetForm = () => {
    reset()
  }

  const onSubmit = (formValue) => {
    data.api_id = formValue?.api_id;
    data.var_store_id = formValue?.var_store_id;
    data.success = formValue?.success;
    data.fallback = formValue?.fallback;
    data.errors = formValue?.errors;

    if (data.var_store_id) {
      setVariableLocation({
        position_type: VARIABLE_POSITION_TYPE.FLOW,
        flow_dev_id: flow.id,
        node_id: data.id,
        var_def_dev_id: data.var_store_id
      })
    }

    resetForm();
    setModalOpen(false);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  }

  useEffect(() => {
    resetForm();
    data.api_id && setValue('api_id', data.api_id);
    data.var_store_id && setValue('var_store_id', data.var_store_id)
    data.errors && errorReplace(data.errors)
    data.errors && setSelectedItems(data.errors.map(v => `api_err_${v.key}`))
    setValue('success', `${data.id}_source#1`)
    setValue('fallback', `${data.id}_source#2`)
    data.success = `${data.id}_source#1`;
    data.fallback = `${data.id}_source#2`;
  }, [JSON.stringify(data), JSON.stringify(apis)]);

  const handleChangeErrorApi = useCallback((value) => {
    const errors = value.map((v, index) => ({key: v.split("api_err_")[1], value: `${data.id}_source#${index + 3}`}));

    const otherEdges = [...getEdges().filter(otherEdge => otherEdge.source !== data.id)]
    const defaultThisNodeEdges = [...getEdges().filter(edge => edge.source === data.id && (edge.sourceHandle === `${data.id}_source#1` || edge.sourceHandle === `${data.id}_source#2`))]
    const newErrorThisNodeEdges = errors.length === 0 ? defaultThisNodeEdges : [
      ...getEdges().filter(edge => errors.some(err => err.value === edge.sourceHandle))
    ];
    setEdges([...otherEdges, ...defaultThisNodeEdges, ...newErrorThisNodeEdges]);

    errorReplace(errors)
    setSelectedItems(value)
  }, [])

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    if (!data.debug) setModalOpen(true);
  }, [data.debug]);

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $debug={data.debug} $theme={theme}>
      {
        debug && (
          <div className="absolute left-0 text-[10px]" style={{top: -20, color: '#D4D9E2'}}>{data.id}</div>
        )
      }
      {
        status && status === STUDIO_STATUS.DEV && (
          <NodeTooltip data={data} selected={data.selected}/>
        )
      }
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black'
          }}
        />
        <StyledHandleSourceAnchor $bgColor={data.node_color} $data={!!(data.api_id)} onDoubleClick={handleOpenModal}>
          {
            data.api_id ? apis.find(api => api.id === data.api_id) ? apis.find(api => api.id === data.api_id).name : 'Configure' : 'Configure'
          }
        </StyledHandleSourceAnchor>
        <StyledHandleSourceAnchorStatus $type={'success'} onDoubleClick={handleOpenModal}>
          Success
        </StyledHandleSourceAnchorStatus>
        <StyledHandleSourceAnchorStatus $type={'fallback'} onDoubleClick={handleOpenModal}>
          Fallback
        </StyledHandleSourceAnchorStatus>
        {
          errorsFormArray && errorsFormArray.map((err) => (
            <StyledHandleSourceAnchorStatus key={err.key} $type={'error'} onDoubleClick={handleOpenModal}>
              {err.key}
            </StyledHandleSourceAnchorStatus>
          ))
        }
        {
          !data.goToBlockSource ? (
            ['success', 'fallback', ...(errorsFormArray || [])].map((type, index) => {
              const key = `${data.id}_source#${index + 1}`
              return <Handle
                key={key}
                type="source"
                position={Position.Right}
                id={key}
                style={{
                  height: 10,
                  width: 10,
                  top: `${88 + 23 * index}px`,
                  backgroundColor: type === 'success' ? '#66b266' : type === 'fallback' ? '#ff6666' : '#fa6505',
                }}
              />
            })
          ) : (
            ['success', 'fallback', ...(errorsFormArray || [])].map((type, index) => {
              const key = `${data.id}_source#${index + 1}`
              const goToBlock = data.goToBlockSource.find(v => v.sourceHandle === key)
              if (goToBlock) {
                return <GoToBlockHandle
                  key={key}
                  data={data}
                  sourceHandle={key}
                  style={{
                    height: 14,
                    width: 84,
                    top: `${88 + 23 * index}px`,
                    right: -80,
                    backgroundColor: type === 'success' ? '#66b266' : type === 'fallback' ? '#ff6666' : 'white',
                    fontSize: 10,
                    color: theme === 'dark' ? 'white' : 'black',
                    border: 'none',
                    borderRadius: 10,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                />
              }

              return <Handle
                key={key}
                type="source"
                position={Position.Right}
                id={key}
                style={{
                  height: 10,
                  width: 10,
                  top: `${88 + 23 * index}px`,
                  backgroundColor: type === 'success' ? '#66b266' : type === 'fallback' ? '#ff6666' : 'white',
                }}
              />
            })
          )
        }
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col space-y-4 mt-6">
          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
              Select API <span style={{color: 'red'}}>*</span>
            </Typography.Text>
            <SelectField name={'api_id'} disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                         options={apis.map(v => ({
                           ...v,
                           key: v.id,
                           value: v.id,
                           label: v.name
                         }))}/>
          </div>

          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Error status code</Typography.Text>
            <Select
              mode={'multiple'}
              allowClear
              tokenSeparators={[',']}
              className="antd-select-tags-cus"
              onChange={handleChangeErrorApi}
              value={selectedItems}
              options={ERROR_MESSAGES.map(v => ({...v, key: v.value, value: v.value, label: v.label}))}
              disabled={status && status === STUDIO_STATUS.LIVE}
              notFoundContent={"No data"}
            />

          </div>

          <Divider style={{borderColor: "white"}}/>

          <div className="w-full flex justify-end">
            <div className="flex justify-end items-center space-x-2">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Store response in:</Typography.Text>
              <SelectField style={{width: '250px'}} name={'var_store_id'}
                           disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                           options={variablesDev.map(v => ({
                             ...v,
                             key: v.id,
                             value: v.id,
                             label: v.var_name
                           }))}/>
            </div>
          </div>

          <div className="w-full flex justify-end items-center space-x-4" style={{marginTop: 24}}>
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() => setModalOpen(false)}
            >
              Cancel
            </StyledButtonModal>
            <StyledButtonModal
              type="submit"
              key="save"
              $bgColor={"#7F75CF"}
              $color={"white"}
              disabled={status && status === STUDIO_STATUS.LIVE}
            >
              Save
            </StyledButtonModal>
          </div>
        </form>
      </Modal>
    </StyledNodeFlow>
  );
};

export default ApiNode;
