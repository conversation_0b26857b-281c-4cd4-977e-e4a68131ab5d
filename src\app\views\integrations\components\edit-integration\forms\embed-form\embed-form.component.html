<div class="h-full" [ngClass]="[isHandset() ? 'flex flex-col' : 'flex flex-row']">
  <div class="overflow-y-auto lg:w-1/2" [ngClass]="{'h-[576px] pr-4': !isHandset(), 'h-full': isHandset()}">
    <div class="mb-8">
      <div class="text-xl font-semibold mb-2 text-base-content dark:text-dark-base-content">Web widget:</div>
      <h3 class="text-sm font-normal text-neutral-content dark:text-dark-neutral-content mb-3">To add the web widget to your website include this JavaScript snippet at the end of your HEAD tag:</h3>
      <div class="relative rounded-xl border border-primary-border dark:border-dark-primary-border">
        <pre><code class="!bg-base-300 dark:!bg-dark-base-300 rounded-xl text-primary-hover hljs" [innerHTML]="highlightedScript()"></code></pre>
        <div class="absolute top-0 right-0 mt-4 mr-4 p-1 w-fit h-fit flex items-center justify-center rounded-lg bg-base-400 dark:bg-dark-base-400" (click)="copyText(textScript())" >
          <ng-icon name="heroDocumentDuplicate" class="text-2xl !text-primary dark:!text-dark-primary"></ng-icon>
        </div>
      </div>
    </div>

    <div class="flex flex-col mb-4">
      <div class="text-xl font-semibold mb-2 text-base-content dark:text-dark-base-content">Bubble custom:</div>
      <div class="text-sm font-normal text-neutral-content dark:text-dark-neutral-content mb-3">Change the bubble of your web widget button to complement your brand</div>
      <dx-form-field>
        <dx-label>Background color:</dx-label>
        <input dxInput [(ngModel)]="bubble().bgColor" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text"/>
      </dx-form-field>
      <dx-form-field>
        <dx-label>Text color:</dx-label>
        <input dxInput [(ngModel)]="bubble().color" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text"/>
      </dx-form-field>
      <dx-form-field>
        <dx-label>Content (url image or string):</dx-label>
        <input dxInput [(ngModel)]="bubble().content" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text"/>
      </dx-form-field>
      <dx-form-field>
        <dx-label>Padding:</dx-label>
        <input dxInput [(ngModel)]="bubble().padding" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text" placeholder="0px"/>
      </dx-form-field>
      <dx-form-field>
        <dx-label>Border radius:</dx-label>
        <input dxInput [(ngModel)]="bubble().borderRadius" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text" placeholder="50%"/>
      </dx-form-field>
    </div>

    <div class="flex flex-col mb-4">
      <div class="text-xl font-semibold mb-2 text-base-content dark:text-dark-base-content">Widget custom:</div>
      <div class="text-sm font-normal text-neutral-content dark:text-dark-neutral-content mb-3">Change the interface of your web widget to complement your brand</div>
      <dx-form-field>
        <dx-label>Background color:</dx-label>
        <input dxInput [(ngModel)]="widget().bgColor" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text"/>
      </dx-form-field>
      <dx-form-field>
        <dx-label>Header/Footer color:</dx-label>
        <input dxInput [(ngModel)]="widget().color" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text"/>
      </dx-form-field>
      <dx-form-field>
        <dx-label>Content image URL:</dx-label>
        <input dxInput [(ngModel)]="widget().contentImg" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text" placeholder=""/>
      </dx-form-field>
      <dx-form-field>
        <dx-label>Content text:</dx-label>
        <input dxInput [(ngModel)]="widget().contentText" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text" placeholder="Nhân viên tư vấn"/>
      </dx-form-field>
    </div>

    <div class="flex flex-col mb-4">
      <div class="text-xl font-semibold mb-2 text-base-content dark:text-dark-base-content">Chat assistant custom:</div>
      <div class="text-sm font-normal text-neutral-content dark:text-dark-neutral-content mb-3">Change the chat assistant of your web widget to complement your brand</div>
      <dx-form-field>
        <dx-label>Background color:</dx-label>
        <input dxInput [(ngModel)]="chatAssistant().bgColor" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text"/>
      </dx-form-field>
      <dx-form-field>
        <dx-label>Header/Footer color:</dx-label>
        <input dxInput [(ngModel)]="chatAssistant().color" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text"/>
      </dx-form-field>
    </div>

    <div class="flex flex-col mb-4">
      <div class="text-xl font-semibold mb-2 text-base-content dark:text-dark-base-content">Chat user custom:</div>
      <div class="text-sm font-normal text-neutral-content dark:text-dark-neutral-content mb-3">Change the chat user of your web widget to complement your brand</div>
      <dx-form-field>
        <dx-label>Background color:</dx-label>
        <input dxInput [(ngModel)]="chatUser().bgColor" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text"/>
      </dx-form-field>
      <dx-form-field>
        <dx-label>Header/Footer color:</dx-label>
        <input dxInput [(ngModel)]="chatUser().color" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text"/>
      </dx-form-field>
    </div>

    <div class="flex flex-col mb-4">
      <div class="text-xl font-semibold mb-2 text-base-content dark:text-dark-base-content">Powered by custom:</div>
      <div class="text-sm font-normal text-neutral-content dark:text-dark-neutral-content mb-3">Change the powered by section of your web widget</div>
      <dx-form-field>
        <dx-label>Powered by text:</dx-label>
        <input dxInput [(ngModel)]="poweredBy().text" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text" placeholder="DxGPT"/>
      </dx-form-field>
      <dx-form-field>
        <dx-label>Powered by link:</dx-label>
        <input dxInput [(ngModel)]="poweredBy().link" (ngModelChange)="changeScript()" appTrimString trim="blur" type="text" placeholder="https://"/>
      </dx-form-field>
    </div>
  </div>
  @if (!isHandset()){
    <div class="relative lg:w-1/2 h-full pl-4 border-l-base-200">
      <div class="justify-center items-center flex">
        <div class="text-xl font-semibold mb-2 text-base-content dark:text-dark-base-content">Widget</div>
      </div>
      <div #previewContainer class="w-full h-[520px]"></div>
    </div>
  }
</div>
