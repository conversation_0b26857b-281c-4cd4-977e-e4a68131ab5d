<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100">
    <div class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content">
      Tags
    </div>
    <div class="flex items-center justify-end space-x-4">
      <app-svg-icon type="icClose" class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                    (click)="onCloseDialog()"></app-svg-icon>
    </div>
  </div>

  <!-- Content -->
  <div class="flex-1 overflow-y-auto mt-18 mb-20 px-6 pb-6">
    <div class="mt-4 w-full max-h-[60vh] overflow-auto space-y-2">
      @if (availableTags().length === 0) {
        <div class="text-center text-gray-500 dark:text-gray-400 italic">
          No tags available at the moment. Please contact the administrator to add some!
        </div>
      } @if (availableTags().length > 0) {
      <div class="space-y-2">
        @for (tag of availableTags(); track tag.id) {
          <div class="w-full">
            <mat-checkbox class="w-full" [checked]="isTagSelected(tag.id)"
                          (change)="onTagSelectionChange($event, tag.id)">
              <div class="w-full px-3 py-2 rounded-md text-sm font-medium" [ngStyle]="{
                  background: getColor(tag.config),
                  color: getTextColor(getColor(tag.config))
                }">
                {{ tag.name }}
              </div>
            </mat-checkbox>
          </div>
        }
      </div>
    }
    </div>

    <!-- Statistics -->
    <div class="mt-4 flex justify-between text-sm text-gray-600 dark:text-gray-400">
      <span>Selected: {{ selectedTagsCount() }}</span>
      <span>Available: {{ availableTagsCount() }}</span>
    </div>

    <!-- Quick Actions -->
    <div class="mt-2 flex gap-2">
      <button
        class="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded hover:bg-blue-200 dark:hover:bg-blue-800 transition"
        (click)="onSelectAll()"
        [disabled]="selectedTagsCount() === availableTagsCount()"
      >
        Select All
      </button>
      <button
        class="text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded hover:bg-red-200 dark:hover:bg-red-800 transition"
        (click)="onClearAll()"
        [disabled]="selectedTagsCount() === 0"
      >
        Clear All
      </button>
      <button
        class="text-xs bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 px-2 py-1 rounded hover:bg-gray-200 dark:hover:bg-gray-800 transition"
        (click)="onResetSelections()"
        [disabled]="!hasChanges()"
      >
        Reset
      </button>
    </div>
  </div>

  <!-- Footer -->
  <div class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200">
    <button dxButton="elevated" (click)="onCloseDialog()">
      Cancel
    </button>
    <button dxLoadingButton="filled"
            [loading]="isLoadingSave()"
            [disabled]="!canSave()"
            (click)="onSaveAddTag()">
      <span>Save ({{ selectedTagsCount() }})</span>
    </button>
  </div>
</div>
