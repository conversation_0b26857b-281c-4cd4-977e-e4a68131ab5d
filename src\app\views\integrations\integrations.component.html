<div class="flex flex-col justify-between">
  <h1 class="text-[28px] font-bold text-base-content dark:text-dark-base-content">Integrations</h1>
  <p class="mt-2 text-[15px] text-neutral-content dark:text-dark-neutral-content">
    As soon as your AI Assistant is ready, anyone can start talking to your AI
    Assistant using your public link or web widget.
  </p>

  <div class="mt-6 w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    @for (integration of listIntegrations(); track $index) {
      <div class="col-span-1 w-full">
        <dx-card
          appearance="outlined"
          class="card-integration"
          [ngClass]="{
        'cursor-pointer': !integration.isComingSoon
      }"
        >
          <dx-card-header>
            <div class="w-full flex items-start justify-between">
              <img
                [ngSrc]="integration.icon!"
                alt="Icon description"
                width="56"
                height="56"
                class="w-14 h-14 object-cover"
                (click)="$event.stopPropagation()"
              />
              @if (!integration.isComingSoon) {
                <dx-slide-toggle
                  [checked]="!!integration.isEnabled"
                  (checkedChange)="enableIntegration($event,integration)"
                ></dx-slide-toggle>
              } @else {
                <div
                  class="px-2 py-1 text-[13px] text-primary-content bg-neutral-content rounded-full"
                >
                  Coming Soon
                </div>
              }
            </div>
          </dx-card-header>
          <dx-card-content  (click)="showDiaLogEdit(integration)">
            <h1
              class="text-xl font-bold text-base-content dark:text-dark-base-content"
            >
              {{ integration.title }}
            </h1>
            <div
              class="mt-2 text-[15px] text-neutral-content dark:text-dark-neutral-content"
            >
              {{ integration.subTitle }}
            </div>
          </dx-card-content>
        </dx-card>
      </div>
    }
  </div>
</div>
