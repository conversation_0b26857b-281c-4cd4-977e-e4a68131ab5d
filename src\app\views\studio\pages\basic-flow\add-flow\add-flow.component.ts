import { Component, computed, inject, OnInit, signal } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { UIStore } from '@core/stores';
import {
  DIALOG_DATA,
  DxButton,
  DxDialogRef,
  DxError,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxOption,
  DxSelect,
  DxSnackBar,
} from '@dx-ui/ui';
import { provideIcons } from '@ng-icons/core';
import { heroXMark } from '@ng-icons/heroicons/outline';
import { FLOW_TRIGGER_TYPE } from '@shared/app.constant';
import { SvgIconComponent } from '@shared/components';
import { AutosizeDirective } from '@shared/directives';
import { IToolDev } from '@shared/models';
import { FlowDevService } from '@shared/services';

@Component({
  selector: 'app-add-flow',
  imports: [
    DxButton,
    DxLoadingButton,
    ReactiveFormsModule,
    DxLabel,
    DxError,
    DxFormField,
    DxInput,
    SvgIconComponent,
    AutosizeDirective,
    DxOption,
    DxSelect,
  ],
  providers: [provideIcons({ heroXMark })],
  templateUrl: './add-flow.component.html',
  styleUrl: './add-flow.component.css',
  host: {
    class: 'h-full',
  },
})
export class AddFlowComponent implements OnInit {
  isSubmitting = signal<boolean>(false);

  readonly editorOptions = computed(() => ({
    theme: this.uiStore.theme() === 'dark' ? 'vs-dark' : 'vs-light',
    language: 'json',
    domReadOnly: true,
    automaticLayout: true,
    readOnly: false,
  }));
  listTriggerType = [
    {
      value: FLOW_TRIGGER_TYPE.INTENT,
      label: 'Intent',
    },
    {
      value: FLOW_TRIGGER_TYPE.EVENT,
      label: 'Event',
    },
  ];

  formGroup: FormGroup = inject(FormBuilder).group({
    id: [null],
    name: [null, Validators.required],
    description: [null, Validators.required],
    trigger_type: [null, Validators.required],
    flow_data: [null],
  });
  data: {
    id: number;
    name: string;
    description: string;
    trigger_type: string;
    flow_data: string;
    isEdit: boolean;
    isClone: boolean;
  } = inject(DIALOG_DATA);
  protected dialogRef = inject(DxDialogRef<AddFlowComponent>);
  private snackBar = inject(DxSnackBar);
  private uiStore = inject(UIStore);
  private flowDevService = inject(FlowDevService);

  ngOnInit(): void {
    if (this.data.isEdit) {
      this.formGroup.patchValue({
        id: this.data.id,
        name: this.data.name,
        description: this.data.description,
        trigger_type: this.data.trigger_type,
        flow_data: this.data.flow_data,
      });
    }
  }

  onSave(isEdit: boolean): void {
    if (this.formGroup.invalid) {
      this.formGroup.markAllAsTouched();
      return;
    }

    const body: IToolDev = this.formGroup.value;

    this.isSubmitting.set(true);
    if (isEdit) {
      this.flowDevService.saveFlowDev(body).subscribe({
        next: () => {
          this.snackBar.open('Flow updated successfully!', '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
          this.dialogRef.close();
          this.isSubmitting.set(false);
        },
        error: () => {
          this.isSubmitting.set(false);
        },
      });
    } else {
      this.flowDevService.saveFlowDev(body).subscribe({
        next: () => {
          this.snackBar.open('Flow created successfully!', '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
          this.dialogRef.close();
          this.isSubmitting.set(false);
        },
        error: () => {
          this.isSubmitting.set(false);
        },
      });
    }
  }
}
