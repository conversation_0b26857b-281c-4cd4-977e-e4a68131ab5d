:host {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
  /* Important for flexbox to work properly */
}

.messages-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.messages {
  flex: 1;
  min-height: 0;
  position: relative;
  overflow-y: auto;
  max-height: 100%;
  scroll-behavior: smooth;
}

/* Custom scrollbar styling */
.messages::-webkit-scrollbar {
  width: 8px;
}

.messages::-webkit-scrollbar-track {
  background: transparent;
}

.messages::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.messages::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* For Firefox */
.messages {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* Chat content container */
.chat-content-container {
  height: 400px;
  display: block;
  /* Remove flex to allow natural content flow */
}

/* Chat frame styles */
.chat-frame {
  max-width: 100%;
}

.chat-input {
  border-radius: 200px;
  border: 1px;
  gap: 16px;
  background: #E1E4E8;
}

.chat-input textarea {
  background: #E1E4E8;
}

/* Message bubble styles */
.msg-bubble {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
  position: relative;
  max-width: 38vw;
  word-break: break-word;
  white-space: pre-wrap;
}

.msg-bubble-user {
  border-radius: 16px;
  border-top-right-radius: 4px;
}

.msg-bubble-assistant {
  border-radius: 16px;
  border-top-left-radius: 4px;
}

/* Tag styles */
.tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.2s ease-in-out;
}



.tag-full {
  font-size: 13px;
  font-weight: 600;
  padding: 2px 10px;
  line-height: 1.3;
  border-radius: 99px;
  min-height: 22px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tag-hover:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.tag:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Custom text area */
.cus-text-area {
  height: 200px;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px;
  gap: 4px;
  background: linear-gradient(0deg, #090B14, #090B14),
    linear-gradient(0deg, #33383F, #33383F);
  border: 1px solid #33383F
}

/* Typing animation */
message-input .typing-animation {
  height: 20px;
  width: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dot {
  height: 8px;
  width: 8px;
  background-color: #333;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typing {

  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1.0);
  }
}

/* Grid animation */
.grid-item {
  animation-delay: 1s;
}

.message-input textarea {
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  resize: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  overflow-y: auto;
  /* Luôn hiển thị scroll khi cần */
}

.message-input textarea:focus {
  outline: none;
  transform: translateY(-1px);
}


/* Custom scrollbar for textarea */
.message-input textarea::-webkit-scrollbar {
  width: 6px;
}

.message-input textarea::-webkit-scrollbar-track {
  background: transparent;
}

.message-input textarea::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.message-input textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* For Firefox */
.message-input textarea {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}
textarea{
  resize: none;
}
