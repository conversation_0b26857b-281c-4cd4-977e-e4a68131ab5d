<div class="h-full flex flex-col overflow-hidden">
  <div
    class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
  >
    Plan Management
  </div>
  <div
    class="mt-6 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
  >
    <div class="flex flex-wrap items-center justify-between">
      <div class="flex items-center space-x-4 flex-wrap">
        <dx-form-field
          class="w-full md:w-96"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <app-svg-icon
            dxPrefix
            type="icSearch"
            class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
          ></app-svg-icon>
          <input
            dxInput
            [(ngModel)]="searchModel.key_word"
            (ngModelChange)="changeFilter()"
            [type]="'text'"
            placeholder="Search by Name"
          />
        </dx-form-field>
        <!-- Duration Filter -->
        <dx-form-field
          class="w-full md:w-48"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <dx-select
            [(ngModel)]="searchModel.duration"
            (selectionChange)="changeFilter()"
          >
            @for (duration of durationOptions; track $index) {
            <dx-option [value]="duration.value">{{ duration.label }}</dx-option>
            }
          </dx-select>
        </dx-form-field>
      </div>
      <div class="flex items-center justify-end">
        <button dxButton (click)="showCreatePlan()">Add Plan</button>
      </div>
    </div>

    <div class="flex-1 flex overflow-hidden">
      <app-data-table
        class="w-full"
        [rows]="listAllPlan"
        [columns]="columns"
        [pageIndex]="searchModel.page"
        [limit]="searchModel.pageSize"
        [count]="count"
        [rowTemplate]="rowTemplate"
        [actionTemplate]="actionTemplate"
        [hiddenPaginator]="true"
        (pageChange)="changePage($event)"
        (action)="onAction($event)"
      ></app-data-table>
    </div>
  </div>
</div>

<!-- Custom row template -->
<ng-template #rowTemplate let-row="row" let-column="column">
  @switch (column.columnDef) { @case ('status') {
  <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
    <div
      class="px-4 rounded-full text-center"
      [ngClass]="{
        'bg-success text-success-content':
          row[column.columnDef] === STATUS.ACTIVE,
        'bg-error text-error-content': row[column.columnDef] !== STATUS.ACTIVE
      }"
    >
      {{ row[column.columnDef] | titlecase }}
    </div>
  </div>
  } @case ('price') {
  <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
    {{
      (row[column.columnDef] | currency : "" : "" : "1.0-0") +
        " " +
        (row.currency ?? "USD")
    }}
  </div>
  } @case ('duration') {
  <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
    {{ row[column.columnDef] | capital }}
  </div>
  } @case ('scope') {
  <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
    {{ row[column.columnDef] | capital }}
  </div>
  } @default {
  <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
    {{ row[column.columnDef] }}
  </div>
  } }
</ng-template>

<!-- Custom action template -->
<ng-template #actionTemplate let-row>
  <div class="flex justify-center items-center">
    <button
      class="flex"
      (click)="row.isActions = !row.isActions"
      cdkOverlayOrigin
      #trigger="cdkOverlayOrigin"
    >
      <ng-icon
        name="heroEllipsisHorizontal"
        size="24"
        class="flex items-center justify-center"
      ></ng-icon>
    </button>
    <ng-template
      cdkConnectedOverlay
      [cdkConnectedOverlayOrigin]="trigger"
      [cdkConnectedOverlayOpen]="row.isActions"
      [cdkConnectedOverlayPush]="true"
      [cdkConnectedOverlayPositions]="[
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'top',
          offsetY: 10
        },
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'bottom',
          offsetY: 10,
        },
      ]"
    >
      <ul
        class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
        (clickOutside)="row.isActions = false; row.isContextMenu = false"
      >
        @if (currentUser.role === ROLE_ACCOUNT.ADMIN) {
        <li>
          <div
            (click)="
              $event.stopPropagation();
              showClonePlan(row);
              row.isActions = false
            "
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg"
          >
            <ng-icon
              dxTooltip="Clone plan"
              name="heroDocumentDuplicate"
              class="text-2xl flex items-center justify-center"
            ></ng-icon>
            <div
              class="flex items-center justify-between text-[16px] font-medium"
            >
              Duplicate
            </div>
          </div>
        </li>
        } @if (!(currentUser.role === ROLE_ACCOUNT.PARTNER && row.scope ===
        'public')) {
        <li>
          <div
            (click)="
              $event.stopPropagation(); showEditPlan(row); row.isActions = false
            "
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg"
          >
            <ng-icon
              dxTooltip="Edit plan"
              name="heroPencilSquare"
              class="text-2xl flex items-center justify-center"
            ></ng-icon>
            <div
              class="flex items-center justify-between text-[16px] font-medium"
            >
              Edit
            </div>
          </div>
        </li>
        } @if (!(currentUser.role === ROLE_ACCOUNT.PARTNER && row.scope ===
        'public')) { @if (row.status !== STATUS.INACTIVE) {
        <li>
          <div
            (click)="
              $event.stopPropagation();
              showDeletePlan(row);
              row.isActions = false
            "
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg"
          >
            <ng-icon
              dxTooltip="Delete plan"
              name="heroTrash"
              class="text-2xl flex items-center justify-center"
            ></ng-icon>
            <div
              class="flex items-center justify-between text-[16px] font-medium"
            >
              Delete
            </div>
          </div>
        </li>
        } }
      </ul>
    </ng-template>
  </div>
</ng-template>
