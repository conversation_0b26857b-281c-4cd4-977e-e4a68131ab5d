import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import {
  Component,
  inject,
  OnInit,
  signal,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import {
  DxButton,
  DxDialog,
  DxFormField,
  DxInput,
  DxOption,
  DxPrefix,
  DxSelect,
  DxTooltip,
} from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroEllipsisHorizontal,
  heroEye,
  heroMagnifyingGlass,
  heroPencilSquare,
  heroPlus,
  heroTrash,
} from '@ng-icons/heroicons/outline';
import { SvgIconComponent } from '@shared/components';
import { BaseComponent } from '@shared/components/base.component';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import {
  DataTableComponent,
  IColumn,
} from '@shared/components/data-table/data-table.component';
import { SelectOption } from '@shared/components/select/select.component';
import { ClickOutsideDirective } from '@shared/directives';
import { ICustomer, LeadsService } from '@shared/services';
import { AddOrEditLeadComponent } from '@views/leads/add-or-edit-lead/add-or-edit-lead.component';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

export interface ILead extends ICustomer {
  id?: number;
  index?: number;
}

@Component({
  selector: 'app-leads',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatInputModule,
    NgIconsModule,
    OverlayModule,
    DataTableComponent,
    ClickOutsideDirective,
    DxFormField,
    DxInput,
    DxSelect,
    DxOption,
    DxButton,
    DxTooltip,
    DxPrefix,
    SvgIconComponent,
  ],
  templateUrl: './leads.component.html',
  styleUrls: ['./leads.component.css'],
  providers: [
    provideIcons({
      heroMagnifyingGlass,
      heroPlus,
      heroEllipsisHorizontal,
      heroPencilSquare,
      heroTrash,
      heroEye,
    }),
  ],
})
export class LeadsComponent extends BaseComponent implements OnInit {
  @ViewChild('viewLeadDialog') viewLeadDialog: any;
  @ViewChild('paginator') paginator: any;
  @ViewChild('actionTemplate') actionTemplate!: TemplateRef<any>;

  // Helper method to truncate text for display
  truncateText(text: string, maxLength: number = 100): string {
    if (!text) return '';
    return text.length > maxLength
      ? text.substring(0, maxLength) + '...'
      : text;
  }

  // Helper method to format date time
  formatDateTime(dateTime: string): string {
    if (!dateTime) return '';
    try {
      const date = new Date(dateTime);
      // Add 7 hours to convert from UTC to Vietnam timezone (UTC+7)
      date.setHours(date.getHours() + 7);

      // Format: DD/MM/YYYY, HH:mm:ss
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');

      return `${day}/${month}/${year}, ${hours}:${minutes}:${seconds}`;
    } catch (error) {
      return dateTime;
    }
  }

  leadData: ILead = {
    user_name: '',
    phone_number: '',
    email: '',
    user_id: '',
    status: 'new',
    data_info: '{}',
  };

  columns: IColumn[] = [
    {
      columnDef: 'index',
      flex: 0.1,
      headerName: 'No.',
    },
    {
      columnDef: 'user_name',
      minWidth: '200px',
      maxWidth: '300px',
      flex: 0.25,
      headerName: 'Name',
    },
    {
      columnDef: 'phone_number',
      minWidth: '150px',
      maxWidth: '200px',
      flex: 0.2,
      headerName: 'Phone',
    },
    {
      columnDef: 'email',
      minWidth: '200px',
      maxWidth: '300px',
      flex: 0.25,
      headerName: 'Email',
    },
    {
      columnDef: 'status',
      minWidth: '120px',
      maxWidth: '150px',
      flex: 0.15,
      headerName: 'Status',
      align: 'center',
    },
    {
      columnDef: 'user_id',
      minWidth: '150px',
      maxWidth: '200px',
      flex: 0.2,
      headerName: 'User ID',
    },
    {
      columnDef: 'update_time',
      minWidth: '180px',
      maxWidth: '220px',
      flex: 0.2,
      headerName: 'Update Time',
    },
    {
      columnDef: 'action',
      flex: 0.1,
      headerName: 'Action',
    },
  ];

  listLeads = signal<ILead[]>([]);
  count = signal<number>(0);
  isLoading = signal<boolean>(false);
  isLoadingSaveLead = signal<boolean>(false);
  selectedStatus = 'null';
  pageIndex = signal<number>(0);

  statusOptions: SelectOption[] = [
    {
      value: 'null',
      label: 'All Statuses',
    },
    {
      value: 'new',
      label: 'New',
    },
    {
      value: 'contacted',
      label: 'Contacted',
    },
    {
      value: 'qualified',
      label: 'Qualified',
    },
    {
      value: 'proposal',
      label: 'Proposal',
    },
    {
      value: 'negotiation',
      label: 'Negotiation',
    },
    {
      value: 'closed_won',
      label: 'Closed Won',
    },
    {
      value: 'closed_lost',
      label: 'Closed Lost',
    },
  ];

  override searchModel: any = {
    key_word: '',
    status: null,
    page: 0,
    pageSize: 10,
  };
  private searchSubject = new Subject<string>();

  private dialogService = inject(DxDialog);

  get displayedColumns(): any {
    return this.columns.map((c) => c.columnDef);
  }

  constructor(private leadsService: LeadsService) {
    super();
  }

  ngOnInit() {
    this.searchModel.status = null;
    this.loadLeads();

    this.searchSubject.pipe(debounceTime(300)).subscribe(() => {
      this.loadLeads();
    });
  }

  // Load leads from API
  loadLeads() {
    this.isLoading.set(true);

    const params = {
      page: this.searchModel.page + 1, // API expects 1-based page
      page_size: this.searchModel.pageSize,
      search: this.searchModel.key_word || undefined,
      status: this.searchModel.status || undefined,
    };

    this.leadsService.getCustomers(params).subscribe({
      next: (response) => {
        this.listLeads.set(
          response.items.map((item, index) => ({
            ...item,
            id: index + 1, // Add temporary ID for display
            update_time: (item as any).update_at || (item as any).updated_at, // Map update_at/updated_at to update_time
          }))
        );
        this.count.set(response.total);
        this.isLoading.set(false);
      },
      error: (error) => {
        this.showSnackBar(error.error.detail, 'error');
        this.isLoading.set(false);
      },
    });
  }

  resetLeadData() {
    this.leadData = {
      user_name: '',
      phone_number: '',
      email: '',
      user_id: '',
      status: 'new',
      data_info: '{}',
    };
  }

  // Validate and format JSON

  closeLeadDialog() {
    this.isLoadingSaveLead.set(false);
    this.closeDialog();
    this.resetLeadData();
  }

  handleAction(caseName: string, element: any) {
    switch (caseName) {
      case 'view':
        this.viewLeadDetails(element);
        break;
      case 'edit':
        this.editLead(element);
        break;
      case 'delete':
        this.deleteLead(element);
        break;
    }
  }

  viewLeadDetails(lead: ILead) {
    this.showDialog(this.viewLeadDialog, {
      data: { lead },
      panelClass: 'dialog-responsive',
      width: '800px',
      maxWidth: '90vw',
      maxHeight: '90vh',
    });
  }

  editLead(lead: ILead) {
    this.leadData = { ...lead };

    // Format data_info for editing (pretty print JSON)
    if (this.leadData.data_info) {
      try {
        const parsed = JSON.parse(this.leadData.data_info);
        this.leadData.data_info = JSON.stringify(parsed, null, 2);
      } catch (error) {
        // Keep original if not valid JSON
      }
    }

    this.dialogService
      .open(AddOrEditLeadComponent, {
        data: { isEdit: true, lead: this.leadData },
        panelClass: 'dialog-responsive',
        width: '600px',
        maxWidth: '90vw',
        maxHeight: '90vh',
      })
      .afterClosed()
      .subscribe(() => {
        this.loadLeads();
      });
  }

  deleteLead(lead: ILead) {
    if (!lead.user_id) {
      this.showSnackBar('Cannot delete lead: missing user ID', 'error');
      return;
    }

    this.dialogService
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Delete Lead',
          content: `Are you sure you want to delete lead "${lead.user_name}"?`,
          confirmText: 'Delete',
          cancelText: 'Cancel',
          isDelete: true,
        },
      })
      .afterClosed()
      .subscribe((result: any) => {
        if (!!result && lead.user_id) {
          this.leadsService.deleteCustomer(lead.user_id).subscribe({
            next: () => {
              this.showSnackBar('Lead deleted successfully', 'success');
              this.loadLeads();
            },
            error: (error) => {
              this.showSnackBar(error.error.detail, 'error');
            },
          });
        }
      });
  }

  changePage(event: any) {
    this.pageIndex.set(event.pageIndex);
    this.searchModel.page = event.pageIndex;
    this.searchModel.pageSize = event.pageSize;
    this.loadLeads();
  }

  getRowIndex(row: any): any {
    const index = this.listLeads().findIndex(
      (item) => item.user_id === row.user_id
    );
    return this.pageIndex() * this.searchModel.pageSize + index;
  }

  createNewLead() {
    this.resetLeadData();
    this.dialogService
      .open(AddOrEditLeadComponent, {
        data: { isEdit: false },
        panelClass: 'dialog-responsive',
        width: '600px',
        maxWidth: '90vw',
        maxHeight: '90vh',
      })
      .afterClosed()
      .subscribe(() => {
        this.loadLeads();
      });
  }

  onSearchChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchModel.key_word = target.value;
    this.applyFilters();
  }

  onStatusChange(): void {
    const value = this.selectedStatus;
    this.searchModel.status = value === 'null' ? null : value;
    this.applyFilters();
  }

  applyFilters() {
    // Reset to first page when filtering
    this.searchModel.page = 0;
    this.pageIndex.set(0);
    this.searchSubject.next(this.searchModel.key_word);
    // this.loadLeads();
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'new':
        return 'bg-info text-info-content';
      case 'contacted':
        return 'bg-warning text-warning-content';
      case 'qualified':
        return 'bg-primary text-primary-content';
      case 'proposal':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'negotiation':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300';
      case 'closed_won':
        return 'bg-success text-success-content';
      case 'closed_lost':
        return 'bg-error text-error-content';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'new':
        return 'New';
      case 'contacted':
        return 'Contacted';
      case 'qualified':
        return 'Qualified';
      case 'proposal':
        return 'Proposal';
      case 'negotiation':
        return 'Negotiation';
      case 'closed_won':
        return 'Closed Won';
      case 'closed_lost':
        return 'Closed Lost';
      default:
        return status;
    }
  }

  formatDataInfo(dataInfo: string): string {
    try {
      const parsed = JSON.parse(dataInfo);
      return JSON.stringify(parsed, null, 2);
    } catch (error) {
      return dataInfo;
    }
  }

  onAction(event: any): void {
    // Handle any additional actions if needed
  }
}
