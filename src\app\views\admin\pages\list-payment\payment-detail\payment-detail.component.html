<div class="flex flex-col rounded-3xl">
  <div
    class="w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      Payment Details
    </div>
    <div class="flex items-center justify-end space-x-4">
      <ng-icon
        name="heroXMark"
        class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></ng-icon>
    </div>
  </div>

  <div
    class="flex flex-col space-y-4 px-6 py-5 text-base-content dark:text-dark-base-content"
  >
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <h3 class="font-semibold mb-2">Customer Information</h3>
        <div class="space-y-2">
          <div>
            <span class="font-medium">Full Name:</span
            >{{ data?.payment?.fullName || "N/A" }}
          </div>
          <div>
            <span class="font-medium">Email:</span
            >{{ data?.payment?.email || "N/A" }}
          </div>
          @if (data?.payment?.phone_number) {
          <div>
            <span class="font-medium">Phone:</span
            >{{ data?.payment?.phone_number }}
          </div>
          }
        </div>
      </div>

      <div>
        <h3 class="font-semibold mb-2">Payment Information</h3>
        <div class="space-y-2">
          <div>
            <span class="font-medium">Plan:</span>
            <span
              class="px-2 border rounded text-center ml-2"
              [ngClass]="
                data?.payment?.plan ? handleClassesPlan(data.payment.plan) : ''
              "
            >
              {{ data?.payment?.plan || "None" }}
            </span>
          </div>
          <div>
            <span class="font-medium">Amount:</span>
            {{
              data?.payment?.amount
                ? (data.payment.amount | currency : "" : "" : "1.0-0") +
                  " " +
                  (data?.payment?.currency | uppercase)
                : "N/A"
            }}
          </div>
          <div>
            <span class="font-medium">Payment Method:</span>
            {{ data?.payment?.payment_method || "N/A" }}
          </div>
          <div>
            <span class="font-medium">Status:</span>
            <span
              class="px-2 rounded text-light-white text-center ml-2"
              [ngClass]="{
                'bg-light-orange': data?.payment?.status === 'pending',
                'bg-light-green': data?.payment?.status === 'done',
                'bg-light-red':
                  data?.payment?.status &&
                  data.payment.status !== 'pending' &&
                  data.payment.status !== 'done'
              }"
            >
              {{ data?.payment?.status || "N/A" }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-4">
      <h3 class="font-semibold mb-2">Dates</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <span class="font-medium">Start Date:</span>
          {{
            data?.payment?.start_date
              ? (data.payment.start_date | date : "dd/MM/yyyy")
              : "None"
          }}
        </div>
        <div>
          <span class="font-medium">End Date:</span>
          {{
            data?.payment?.end_date
              ? (data.payment.end_date | date : "dd/MM/yyyy")
              : "None"
          }}
        </div>
      </div>
    </div>
  </div>

  <div
    class="flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Close</button>
    <button
      dxButton="filled"
      [class]="
        data?.payment?.status === 'pending'
          ? '!bg-light-green'
          : '!bg-light-orange'
      "
      (click)="
        data?.payment &&
          updatePaymentStatus(
            data.payment,
            data?.payment?.status === 'pending' ? 'done' : 'cancel'
          );
        dialogRef.close()
      "
    >
      Mark as Done
    </button>
    <!--@if (data?.payment?.status === 'pending') {
      <button dxButton="filled" class="!bg-light-green"
              (click)="data?.payment && updatePaymentStatus(data.payment, 'done'); dialogRef.close()">
        Mark as Done
      </button>
    }
    @if (data?.payment?.status !== 'pending') {
      <button dxButton="filled" class="!bg-light-orange"
              (click)="data?.payment && updatePaymentStatus(data.payment, 'cancel'); dialogRef.close()">
        Cancel Payment
      </button>
    }-->
  </div>
</div>
