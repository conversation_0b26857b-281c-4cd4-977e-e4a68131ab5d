import {
  ApplicationConfig,
  importProvidersFrom,
  provideAppInitializer,
  provideZoneChangeDetection,
} from '@angular/core';
import {
  provideRouter,
  withComponentInputBinding,
  withViewTransitions,
} from '@angular/router';

import {
  HttpClient,
  provideHttpClient,
  withFetch,
  withInterceptors,
} from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { httpTranslateLoaderFactory } from '@core/factory';
import { initializeApp } from '@core/factory/initialize';
import { authInterceptor } from '@core/interceptors';
import { provideIcons } from '@ng-icons/core';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { ADMIN_ROUTES } from '@views/admin/admin.routes';
import { AUTH_ROUTES } from '@views/auth/auth.routes';
import { KNOWLEDGE_BASE_ROUTES } from '@views/knowledge-base/knowledge-base.routes';
import { STUDIO_ROUTES } from '@views/studio/studio.routes';
import { provideMonacoEditor } from 'ngx-monaco-editor-v2';
import { routes } from './app.routes';

export const appConfig: ApplicationConfig = {
  providers: [
    provideAppInitializer(initializeApp),
    provideAnimations(),
    provideAnimationsAsync(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes, withComponentInputBinding(), withViewTransitions()),
    provideRouter(
      AUTH_ROUTES,
      withComponentInputBinding(),
      withViewTransitions()
    ),
    provideRouter(
      STUDIO_ROUTES,
      withComponentInputBinding(),
      withViewTransitions()
    ),
    provideRouter(
      ADMIN_ROUTES,
      withComponentInputBinding(),
      withViewTransitions()
    ),
    provideRouter(
      KNOWLEDGE_BASE_ROUTES,
      withComponentInputBinding(),
      withViewTransitions()
    ),
    provideHttpClient(withInterceptors([authInterceptor]), withFetch()),
    importProvidersFrom([
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: httpTranslateLoaderFactory,
          deps: [HttpClient],
        },
      }),
    ]),
    provideMonacoEditor(),
    provideIcons({}),
  ],
};
