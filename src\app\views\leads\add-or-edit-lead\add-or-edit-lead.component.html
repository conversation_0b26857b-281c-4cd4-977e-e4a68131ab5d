<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      {{ data.isEdit ? "Edit Lead" : "Create Lead" }}
    </div>
    <div class="flex items-center justify-end space-x-4">
      <ng-icon
        name="heroXMark"
        class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="closeLeadDialog()"
      ></ng-icon>
    </div>
  </div>

  <!-- Dialog Content -->
  <div class="flex-1 overflow-auto mt-18 mb-20">
    <div [formGroup]="formGroup" class="px-6 pt-6 pb-[3px] flex flex-col space-x-4">
      <dx-form-field class="w-full">
        <dx-label class="text-sm">Name</dx-label>
        <input
          dxInput
          formControlName="user_name"
          [type]="'text'"
          placeholder="Enter customer name"
          appTrimString
          trim="blur"
        />
      </dx-form-field>
      <dx-form-field class="w-full">
        <dx-label class="text-sm">Phone Number</dx-label>
        <input
          dxInput
          formControlName="phone_number"
          [type]="'tel'"
          placeholder="Enter phone number"
          appTrimString
          trim="blur"
        />
      </dx-form-field>

      <dx-form-field class="w-full">
        <dx-label class="text-sm">Email</dx-label>
        <input
          dxInput
          formControlName="email"
          [type]="'email'"
          placeholder="Enter email address"
          appTrimString
          trim="blur"
        />
      </dx-form-field>

      <dx-form-field class="w-full">
        <dx-label class="text-sm">User ID</dx-label>
        <input
          dxInput
          formControlName="user_id"
          [type]="'text'"
          placeholder="Enter user ID"
          appTrimString
          trim="blur"
        />
      </dx-form-field>

      <dx-form-field class="w-full">
        <dx-label class="text-sm">Status</dx-label>
        <dx-select formControlName="status" placeholder="Select status">
          @for (status of leadStatusOptions; track $index) {
          <dx-option [value]="status.value">{{ status.label }}</dx-option>
          }
        </dx-select>
      </dx-form-field>

      <dx-form-field class="w-full">
        <dx-label class="text-sm">Data info</dx-label>
        <textarea
          dxInput
          formControlName="data_info"
          [type]="'text'"
          placeholder="Enter additional information in JSON format"
          [rows]="3"
          appTrimString
          trim="blur"
        ></textarea>
        <div class="text-xs text-gray-500 mt-1">
          Format: JSON (e.g., {{ "{" }}"source": "Website", "budget": "$5000",
          "company": "ABC Corp"{{ "}" }})
        </div>
      </dx-form-field>
    </div>
  </div>

  <!-- Dialog Footer -->
  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="closeLeadDialog()">Cancel</button>
    <button
      dxLoadingButton="filled"
      [loading]="isLoadingSaveLead()"
      [disabled]="!canSave()"
      (click)="saveLead(data)"
    >
      Save
    </button>
  </div>
</div>
