import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  effect,
  inject,
  signal,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { DxError, DxFormField, DxInput, DxLabel, DxSnackBar } from '@dx-ui/ui';
import { NgIconsModule } from '@ng-icons/core';

@Component({
  selector: 'app-freshchat-form',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    DxInput,
    DxFormField,
    DxLabel,
    DxError,
    NgIconsModule,
  ],
  templateUrl: './freshchat-form.component.html',
})
export class FreshchatFormComponent implements OnInit {
  @Input() initialData!: any;
  @Input() webhookUrl!: string;
  @Output() formChange = new EventEmitter<FormGroup>();

  fb = inject(FormBuilder);
  private snackBar = inject(DxSnackBar);

  formGroup = this.fb.group({
    api_url: [
      '',
      [Validators.required, Validators.pattern('^https?:\\/\\/.+$')],
    ],
    dxgpt_agent_email: ['', [Validators.required, Validators.email]],
    assigned_group_name: [''],
  });

  linkWebHook = signal('');

  constructor() {
    effect(() => {
      this.formChange.emit(this.formGroup);
    });
  }

  ngOnInit() {
    if (this.initialData) {
      this.formGroup.patchValue(this.initialData);
    }
    this.linkWebHook.set(this.webhookUrl);
  }

  async copyText(content: string) {
    try {
      await navigator.clipboard.writeText(content);
      this.snackBar.open('Copied successfully', '', {
        panelClass: 'dx-snack-bar-success',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    } catch {
      this.snackBar.open('Copy failed', '', {
        panelClass: 'dx-snack-bar-error',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }
  }
}
