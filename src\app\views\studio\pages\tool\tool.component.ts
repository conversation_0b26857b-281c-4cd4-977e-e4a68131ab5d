import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  effect,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroEllipsisHorizontalMini,
  heroPencilSquareMini,
  heroPlayMini,
  heroTrashMini,
} from '@ng-icons/heroicons/mini';

import { Router } from '@angular/router';
import {
  AGENT_FLOW_PATH,
  APP_ROUTES,
  STUDIO_PATH,
  STUDIO_STATUS,
} from '@core/constants';
import { StudioStore, UIStore } from '@core/stores';
import { DxButton, DxDialog, DxFormField, DxInput, DxPrefix } from '@dx-ui/ui';
import { heroPlus } from '@ng-icons/heroicons/outline';
import {
  ConfirmDialogComponent,
  DataTableComponent,
  IColumn,
} from '@shared/components';
import { ITool, IToolDev, IToolDevFilter, IToolFilter } from '@shared/models';
import { ToolDevService, ToolService } from '@shared/services';
import { AddToolComponent } from '@views/studio/pages/tool/add-tool/add-tool.component';
import { MonacoEditorModule } from 'ngx-monaco-editor-v2';

@Component({
  selector: 'app-tool',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatProgressSpinnerModule,
    OverlayModule,
    NgIconsModule,
    DataTableComponent,
    MonacoEditorModule,
    DxFormField,
    DxInput,
    DxButton,
    DxPrefix,
  ],
  providers: [
    provideIcons({
      heroPlus,
      heroPencilSquareMini,
      heroPlayMini,
      heroTrashMini,
      heroEllipsisHorizontalMini,
    }),
  ],
  templateUrl: './tool.component.html',
  styleUrls: ['./tool.component.css'],
})
export class ToolComponent implements OnInit {
  searchModel: IToolDevFilter | IToolFilter = {
    keyword: '',
    page: 1,
    page_size: 10,
    sort_by: 'id',
    direction: 'desc',
  };

  tools = signal<Array<IToolDev | ITool>>([]);
  totalTools = signal<number>(0);
  formGroup: FormGroup = new FormGroup({});
  fb = inject(FormBuilder);
  isSubmitting = signal<boolean>(false);
  columns = signal<IColumn[]>([]);

  readonly editorOptions = computed(() => ({
    theme: this.uiStore.theme() === 'dark' ? 'vs-dark' : 'vs-light',
    language: 'json',
    domReadOnly: true,
    automaticLayout: true,
    readOnly: false,
  }));

  readonly STUDIO_STATUS = STUDIO_STATUS;

  studioStore = inject(StudioStore);
  private dialog = inject(DxDialog);
  private uiStore = inject(UIStore);
  private toolDevService = inject(ToolDevService);
  private toolService = inject(ToolService);
  private router = inject(Router);

  constructor() {
    effect(() => {
      const status = this.studioStore.status();
      const baseColumns: IColumn[] = [
        {
          columnDef: 'name',
          headerName: 'Tool Name',
          flex: 2,
        },
        {
          columnDef: 'description',
          headerName: 'Description',
          flex: 3,
        },
        {
          columnDef: 'version',
          headerName: 'Version',
          flex: 1,
        },
      ];

      if (status === this.STUDIO_STATUS.DEV) {
        this.columns.set([
          ...baseColumns,
          {
            columnDef: 'action',
            headerName: 'Actions',
            flex: 1,
            actions: [
              {
                case: 'edit',
                name: 'Edit',
                title: 'Edit',
              },
              {
                case: 'publish',
                name: 'Publish',
                title: 'Publish',
              },
              {
                case: 'delete',
                name: 'Delete',
                title: 'Delete',
              },
            ],
          },
        ]);
      }

      if (status === this.STUDIO_STATUS.LIVE) {
        this.columns.set([
          ...baseColumns,
          {
            columnDef: 'action',
            headerName: 'Actions',
            flex: 1,
            actions: [
              {
                case: 'delete',
                name: 'Delete',
                title: 'Delete',
              },
            ],
          },
        ]);
      }

      this.getTools();
    });
  }

  ngOnInit() {
    this.initForm();
  }

  initForm(): void {
    this.formGroup = this.fb.group({
      id: [null],
      name: [null, Validators.required],
      description: [null, Validators.required],
      parameters: [null, Validators.required],
      flow_data: [null],
    });
  }

  getTools() {
    if (this.studioStore.status() === STUDIO_STATUS.DEV) {
      this.toolDevService.findBySearch(this.searchModel).subscribe({
        next: (response) => {
          this.tools.set(response.items);
          this.totalTools.set(response.total);
        },
      });
    }
    if (this.studioStore.status() === STUDIO_STATUS.LIVE) {
      this.toolService.findBySearch(this.searchModel).subscribe({
        next: (response) => {
          this.tools.set(response.items);
          this.totalTools.set(response.total);
        },
      });
    }
  }

  onPageChange(event: any): void {
    this.searchModel = {
      ...this.searchModel,
      page: event.pageIndex + 1,
      page_size: event.pageSize,
    };
    this.getTools();
  }

  doSearch(): void {
    this.searchModel = {
      ...this.searchModel,
      keyword: this.searchModel.keyword,
    };
    this.getTools();
  }

  onAction(event: any): void {
    const { type, data } = event;
    switch (type) {
      case 'edit':
        this.onEdit(data);
        break;
      case 'publish':
        this.onPublish(data);
        break;
      case 'delete':
        this.onDelete(data);
        break;
      default:
        break;
    }
  }

  openCreateDialog(): void {
    this.formGroup.reset();
    this.dialog.open(AddToolComponent, {
      width: '50vw',
      data: { isEdit: false },
    });
  }

  onEdit(tool: IToolDev) {
    this.dialog
      .open(AddToolComponent, {
        width: '50vw',
        data: {
          id: tool.id,
          name: tool.name,
          description: tool.description,
          parameters: tool.parameters,
          flow_data: tool.flow_data,
          isEdit: true,
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.getTools();
      });
  }

  onPublish(tool: IToolDev) {
    console.log('Publish tool:', tool);
  }

  onDelete(tool: IToolDev) {
    if (tool.id) {
      const id = tool.id;
      this.dialog
        .open(ConfirmDialogComponent, {
          data: {
            title: 'Delete Tool',
            message: `Are you sure you want to delete tool "${tool.name}"?`,
            confirmText: 'Delete',
            cancelText: 'Cancel',
            isDelete: true,
          },
        })
        .afterClosed()
        .subscribe((result: any) => {
          if (!!result && this.studioStore.status() === STUDIO_STATUS.DEV) {
            this.toolDevService.delete(id).subscribe({
              next: () => {
                this.getTools();
              },
            });
          }
          if (result && this.studioStore.status() === STUDIO_STATUS.LIVE) {
            this.toolService.delete(id).subscribe({
              next: () => {
                this.getTools();
              },
            });
          }
        });
    }
  }

  onEditFlow(tool: IToolDev | ITool) {
    void this.router.navigate([
      `${APP_ROUTES.STUDIO}/${STUDIO_PATH.AGENT_FLOW}/${AGENT_FLOW_PATH.TOOL}/${tool.id}`,
    ]);
  }
}
