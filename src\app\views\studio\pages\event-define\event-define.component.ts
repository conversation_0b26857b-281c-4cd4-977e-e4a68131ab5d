import { NgClass } from '@angular/common';
import {
  Component,
  computed,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { StudioStore } from '@core/stores';
import {
  DxButton,
  DxDialog,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxPrefix,
  DxSnackBar,
} from '@dx-ui/ui';
import { STUDIO_STATUS } from '@shared/app.constant';
import {
  ConfirmDialogComponent,
  FlowEnvSelectComponent,
  SvgIconComponent,
} from '@shared/components';
import { IEvent, IEventFilter } from '@shared/models';
import { EventService } from '@shared/services/event.service';
import { ThemeService } from '@shared/services/theme.service';
import { AddOrEditEventComponent } from '@views/studio/pages/event-define/add-or-edit-event/add-or-edit-event.component';
import { ClipboardService } from 'ngx-clipboard';
import { EditorComponent } from 'ngx-monaco-editor-v2';
import {debounceTime, Subject, Subscription} from 'rxjs';

@Component({
  selector: 'app-event-define',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    DxButton,
    DxInput,
    DxFormField,
    DxLabel,
    SvgIconComponent,
    NgClass,
    DxPrefix,
    DxLoadingButton,
    EditorComponent,
    FlowEnvSelectComponent,
  ],
  templateUrl: './event-define.component.html',
  styleUrl: './event-define.component.css',
})
export class EventDefineComponent implements OnInit, OnDestroy {
  formGroup: FormGroup = new FormGroup({});
  eventSelectedId = signal<string | undefined>(undefined);
  eventSelected = signal<IEvent | undefined>(undefined);
  listEvent = signal<IEvent[]>([]);
  event = signal<IEvent | null>(null);
  searchModel: IEventFilter = {
    keyword: '',
  };
  configData: string = JSON.stringify({ channel: '' }, null, 4);

  isCreateOrUpdateEvent = signal(false);
  studioStoreStatus = computed(() => this.studioStore.status());

  fb = inject(FormBuilder);
  studioStore = inject(StudioStore);
  themeService = inject(ThemeService);

  readonly STUDIO_STATUS = STUDIO_STATUS;
  readonly editorOptions = computed(() => ({
    theme: this.themeService.theme() === 'dark' ? 'vs-dark' : 'vs-light',
    language: 'json',
    domReadOnly: true,
    automaticLayout: true,
    readOnly: false,
  }));

  private subcription!: Subscription;
  searchSubject = new Subject<string>();

  private eventService: EventService = inject(EventService);
  private clipboardService: ClipboardService = inject(ClipboardService);
  private dialog = inject(DxDialog);
  private snackBar = inject(DxSnackBar);

  ngOnInit(): void {
    this.getListEvent();
    this.searchSubject.pipe(debounceTime(300),).subscribe((value) => this.doSearch());
  }

  ngOnDestroy(): void {
    if (this.subcription) this.subcription.unsubscribe();
  }

  doSearch(event?: any) {
    this.getListEvent();
  }

  onChangeBody(value: any) {
    const initBody = JSON.stringify({ channel: '' }, null, 4);
    const oldBody = this.eventSelected()?.config ?? initBody;
    if (value && this.configData !== oldBody) {
      this.setDirtyState(true, this.eventSelectedId());
    } else {
      this.clearDirtyState();
    }

  }

  createEvent() {
    const createEventAction = () => {
      this.formGroup.patchValue({
        id: null,
        ai_id: '',
        name: null,
        slug: null,
        headers: null,
        parameters: null,
        body: null,
      });
      this.dialog
        .open(AddOrEditEventComponent, {
          data: {
            isCreate: true,
          },
          width: '40dvw',
        })
        .afterClosed()
        .subscribe((res: any) => {
          if (!!res) {
            this.getListEvent();
          }
        });
    };

    if (this.isOneEventDirty) {
      this.handleConfirmDirtyEvent((value: any) => {
        if (!!value) {
          this.clearDirtyState();
          createEventAction();
        }
      },false);
      return;
    }
    createEventAction();
  }

  editEvent(event: IEvent, evt?: any) {
    evt && evt.stopPropagation();

    const editEventAction = () => {
      this.formGroup.patchValue({
        id: event.id,
        ai_id: '',
        name: event.name,
        config: event.config,
      });
      this.dialog.open(AddOrEditEventComponent, {
        data: {
          id: event.id,
          ai_id: '',
          name: event.name,
          config: event.config,
          isCreate: false,
        },
        width: '40dvw',
      }).afterClosed()
        .subscribe((res: any) => {
          if (!!res) {
            this.getListEvent(true);
          }
        });
    };

    if (this.isOneEventDirty) {
      this.handleConfirmDirtyEvent((value: any) => {
        if (!!value) {
          this.clearDirtyState();
          editEventAction();
        }
      },false);
      return;
    }
    editEventAction();
  }

  saveEvent() {
    let body: IEvent;
    if (!this.eventSelected()) {
      this.showSnackBar('Please select event', 'error');
      return;
    }
    body = {
      ...this.eventSelected()!,
      id: this.eventSelectedId(),
      config: this.configData,
    };

    if (body.id) {
      this.isCreateOrUpdateEvent.set(true);
      this.eventService.updateEvent(body).subscribe({
        next: () => {
          this.showSnackBar('Update successfully', 'success');
          this.getListEvent(true);
          this.isCreateOrUpdateEvent.set(false);
        },
        error: () => this.isCreateOrUpdateEvent.set(false),
      });
    } else {
      this.isCreateOrUpdateEvent.set(true);
      this.eventService.createEvent(body).subscribe({
        next: () => {
          this.showSnackBar('Create successfully', 'success');
          this.getListEvent();
          this.isCreateOrUpdateEvent.set(false);
        },
        error: () => this.isCreateOrUpdateEvent.set(false),
      });
    }
  }

  deleteEvent(event: IEvent, evt?: any) {
    evt && evt.stopPropagation();

    const deleteEventAction = () => {
      this.dialog
        .open(ConfirmDialogComponent, {
          data: {
            title: 'Delete this event',
            content: 'Are you sure delete this event?',
            isDelete: true,
          },
          width: '300px',
        })
        .afterClosed()
        .subscribe((value: any) => {
          if (!!value) {
            if (event && event.id) {
              this.eventService.deleteEvent(event.id).subscribe({
                next: () => {
                  this.showSnackBar('Delete successfully', 'success');
                  this.getListEvent();
                },
                error: (err) => {
                  this.showSnackBar('Failed to delete event', 'error');
                },
              });
            }
          }
        });
    };

    if (this.isOneEventDirty) {
      this.handleConfirmDirtyEvent((value?: any) => {
        if (!!value) {
          this.clearDirtyState();
          deleteEventAction();
        }
      });
      return;
    }
    deleteEventAction();
  }

  selectEvent(event_id?: string) {
    if (this.eventSelectedId() === event_id) return;

    if (this.isOneEventDirty) {
      this.handleConfirmDirtyEvent((value: any) => {
        if (!!value) {
          this.clearDirtyState();
          this.selectEventAction(event_id);
        }
      }, false);
      return;
    }
    this.selectEventAction(event_id);
  }

  copyEventId() {
    if (!this.eventSelectedId()) return;
    this.clipboardService.copy(this.eventSelectedId()!);
    this.showSnackBar('Copied!', 'success');
  }

  changeConfigData() {
    if (this.eventSelected && this.eventSelected()?.config) {
      const config = JSON.parse(this.eventSelected()?.config ?? '{}');
      this.configData = JSON.stringify(config, null, 4);
    }
  }

  private getListEvent(isEdit: boolean = false) {
    this.eventService.getListEvent(this.searchModel).subscribe({
      next: (res) => {
        if (res && res.length !== 0) {
          this.listEvent.set(res);
          if (!isEdit) this.selectEvent(res[0].id);
          else {
            this.selectEventAction(this.eventSelectedId());
            // console.log('edit');
            this.eventSelected.set(
              this.listEvent().find((v) => v.id === this.eventSelectedId())
            );
          }
        } else this.listEvent.set([]);
      },
      error: () => {},
    });
  }

  private setDirtyState(state: boolean, event_id?: string) {
    const event = this.listEvent().find((v) => v.id === event_id);
    if (event) event.isDirty = state;
    if (this.eventSelected() && this.eventSelected()?.id === event_id) {
      this.eventSelected.set({ ...this.eventSelected()!, isDirty: state });
    }
  }

  private get isOneEventDirty() {
    const indexDirtyList = this.listEvent().findIndex((v) => v.isDirty);
    return Boolean(indexDirtyList > -1);
  }

  private handleConfirmDirtyEvent(callback?: any, isDelete: boolean = true) {
    return this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Confirm',
          content:
            'You haven’t saved the changes. Are you sure you want to continue?',
          isDelete: isDelete,
          confirmText: isDelete ? 'Delete' : 'Continue',
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (!!value) callback && callback(value);
      });
  }

  private clearDirtyState() {
    const indexDirtyList = this.listEvent().findIndex((v) => v.isDirty);
    if (indexDirtyList !== -1) {
      this.listEvent()[indexDirtyList].isDirty = false;
    }
    if (this.eventSelected() && this.eventSelected()?.isDirty) {
      this.eventSelected.set({ ...this.eventSelected()!, isDirty: false });
    }
  }

  private selectEventAction(event_id?: string) {
    this.eventSelectedId.set(event_id);
    this.eventSelected.set(this.listEvent().find((v) => v.id === event_id));
    if (!this.eventSelected) return;
    const config = this.eventSelected()?.config
      ? JSON.parse(this.eventSelected()?.config ?? '{}')
      : { channel: '' };
    this.configData = JSON.stringify(config, null, 4);
  }

  showSnackBar(message: string, type: 'success' | 'error') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }
}
