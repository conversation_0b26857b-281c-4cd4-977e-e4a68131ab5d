<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100">
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      {{ data.isClone ? "Clone" : data.isCreate ? "Create" : "Edit" }} plan
    </div>
    <div class="flex items-center justify-end space-x-4">
      <ng-icon
        name="heroXMark"
        class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="closeDialogCreateOrUpdatePlan()"
      ></ng-icon>
    </div>
  </div>

  <div class="flex-1 mt-18 mb-20 overflow-y-auto flex flex-col space-y-4 px-6 py-5">
    <!--    Plan-->
    <div class="" [formGroup]="formGroup">
      <div class="text-xl font-bold mb-4 text-base-content dark:text-dark-base-content">Plan</div>
      <div class="grid grid-cols-8 gap-x-3">
        <dx-form-field class="col-span-8">
          <dx-label class="text-sm">Name</dx-label>
          <input
            dxInput
            formControlName="name"
            [type]="'text'"
            placeholder="Name"
          />
          @if (formGroup.get('name')?.errors?.['required'] &&
          (formGroup.get('name')?.touched || formGroup.get('name')?.dirty)) {
            <dx-error>Name is required.</dx-error>
          }
        </dx-form-field>
        <dx-form-field class="col-span-4 xl:col-span-3">
          <dx-label class="text-sm">Price:</dx-label>
          <input
            dxInput
            formControlName="price"
            [type]="'number'"
            placeholder="Price"
          />
            @if (formGroup.get('price')?.errors?.['required'] && (formGroup.get('price')?.touched || formGroup.get('price')?.dirty)) {
              <dx-error>Price is required.</dx-error>
            }
            @if (formGroup.get('price')?.errors?.['max'] && (formGroup.get('price')?.touched || formGroup.get('price')?.dirty)) {
              <dx-error>Price must be less than 100000000000.</dx-error>
            }
          @if (formGroup.get('currency')?.touched ||
          formGroup.get('currency')?.dirty) {
            @if (!formGroup.get('currency')?.value) {
              <dx-error>Currency is required.</dx-error>
            }
          }
        </dx-form-field>
        <dx-form-field class="col-span-4 xl:col-span-1">
          <dx-label class="text-sm">Currency:</dx-label>
          <dx-select formControlName="currency">
            @for (currency of currencyOptions; track $index) {
              <dx-option [value]="currency.value">{{ currency.label }}</dx-option>
            }
          </dx-select>
        </dx-form-field>
        <dx-form-field class="col-span-8 xl:col-span-4">
          <dx-label class="text-sm"
          >Stripe payment link
            <a
              href="https://dashboard.stripe.com/test/products?active=true"
              target="_blank"
            >
              <ng-icon
                name="heroArrowTopRightOnSquare"
                class="text-xs hover:text-light-primary"
              ></ng-icon>
            </a>
          </dx-label>
          <input
            dxInput
            formControlName="stripe_payment_link"
            [type]="'text'"
            placeholder="Stripe payment link"
            appTrimString
            trim="blur"
          />
          @if (formGroup.get('stripe_payment_link')?.errors &&
          formGroup.get('stripe_payment_link')?.touched) {
            <dx-error>Stripe payment link is invalid.</dx-error>
          }
        </dx-form-field>
        <dx-form-field class="col-span-8 sm:col-span-4">
          <dx-label class="text-sm">Duration:</dx-label>
          <dx-select formControlName="duration">
            @for (duration of durationOptions; track $index) {
              <dx-option [value]="duration.value">{{ duration.label }}</dx-option>
            }
          </dx-select>
          @if (!formGroup.get('duration')?.errors &&
          formGroup.get('duration')?.touched) {
            <dx-error>Duration is invalid.</dx-error>
          }
        </dx-form-field>
        <dx-form-field class="col-span-8 sm:col-span-4">
          <dx-label class="text-sm">Scope</dx-label>
          <dx-select formControlName="scope">
            @for (scope of scopeOptions; track $index) {
              <dx-option [value]="scope.value">{{ scope.label }}</dx-option>
            }
          </dx-select>
        </dx-form-field>
        @if (formGroup.get('scope')?.value === 'custom') {
          <div class="col-span-8 grid grid-cols-subgrid gap-3">
            <dx-form-field class="col-span-8 sm:col-span-4 sm:col-start-5">
              <dx-label class="text-sm">Email</dx-label>
              <input
                dxInput
                formControlName="valueScope"
                [type]="'text'"
                placeholder="Enter user email"
                [readonly]="isEmailScopeValid()"
              />
              <div dxSuffix class="w-fit m-2">
                @if (!loadingCheckEmail) {
                  @if (!isEmailScopeValid) {
                    <button type="button" (click)="checkEmailExist($event)">
                      <ng-icon name="heroArrowPath"></ng-icon>
                    </button>
                  } @else {
                    <button type="button" (click)="clearEmailExist($event)">
                      <ng-icon name="heroTrash"></ng-icon>
                    </button>
                  }
                } @else {
                  <dx-progress-spinner [diameter]="16"></dx-progress-spinner>
                }
                <button type="button">
                  <ng-icon
                    [name]="isEmailScopeValid() ? 'heroCheckCircle' : 'heroXCircle'"
                    class="text-xl"
                    [ngClass]="{
                    '!text-light-green': isEmailScopeValid,
                    '!text-light-red': !isEmailScopeValid
                  }"
                  ></ng-icon>
                </button>
              </div>
              @if (formGroup.get('valueScope')?.errors?.['email']) {
                <dx-error>Email is invalid.</dx-error>
              }
              @if (formGroup.get('valueScope')?.errors?.['required']) {
                <dx-error>Email is required.</dx-error>
              }
            </dx-form-field>
          </div>
        }
      </div>
    </div>
    <!--    Features-->
    @if (listFeature().length > 0) {
      <div class="" [formGroup]="formGroupFeature">
        <div class="text-xl font-bold mb-4 text-base-content dark:text-dark-base-content">Features</div>
        <div class="grid grid-cols-2 gap-x-3">
          @for (item of listFeature(); track item) {
            @if (item.type ===
            'Quantity') {
              <dx-form-field class="col-span-2 sm:col-span-1">
                <dx-label class="text-sm capitalize"
                >Number of {{ item.name }}
                </dx-label
                >
                <input
                  dxInput
                  [type]="'number'"
                  [formControlName]="item.id"
                  appNonNegative
                />
              </dx-form-field>
            }
          }
        </div>
        <div class="grid grid-cols-3 gap-x-3">
          @for (item of listFeature(); track item) {
            @if (item.type === 'Boolean') {
              <div
                class="col-span-3 xl:col-span-1 text-neutral-content dark:text-dark-neutral-content"
              >
                <dx-checkbox [formControlName]="item.id" labelPosition="after">
                  <div class="text-neutral-content dark:text-dark-neutral-content">
                    {{ item.name }}
                  </div>
                </dx-checkbox>
              </div>
            }
          }
        </div>
      </div>
    }
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="closeDialogCreateOrUpdatePlan()">
      Close
    </button>
    <button
      dxLoadingButton="filled"
      [loading]="isLoading()"
      [disabled]="formGroup.invalid || !isEmailScopeValid()"
      (click)="createOrUpdatePlan(formGroup.value, data.isClone)"
    >
      {{ data.isClone ? "Clone" : data.isCreate ? "Create" : "Update" }}
    </button>
  </div>
</div>
