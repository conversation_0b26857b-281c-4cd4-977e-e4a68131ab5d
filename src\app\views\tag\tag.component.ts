import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule, formatDate } from '@angular/common';
import { Component, inject, LOCALE_ID, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroEllipsisHorizontal,
  heroMagnifyingGlass,
  heroPencilSquare,
  heroPlus,
  heroTrash,
} from '@ng-icons/heroicons/outline';

import {
  DxButton,
  DxDialog,
  DxFormField,
  DxInput,
  DxOption, DxPrefix,
  DxSelect, DxSnackBar,
  DxTooltip,
} from '@dx-ui/ui';
import { BaseComponent } from '@shared/components/base.component';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import {
  DataTableComponent,
  IColumn,
} from '@shared/components/data-table/data-table.component';
import { SelectOption } from '@shared/components/select/select.component';
import { ClickOutsideDirective } from '@shared/directives';
import { TagService } from '@shared/services';
import { AddOrEditTagComponent } from '@views/tag/add-or-edit-tag/add-or-edit-tag.component';
import {SvgIconComponent} from '@shared/components';
import {debounceTime, Subject} from 'rxjs';

interface ITag {
  id?: number;
  name: string;
  application: string;
  config: string;
  is_active: boolean;
  created_by?: string;
  updated_at?: string;
  index?: number;
}

@Component({
  selector: 'app-tag',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatInputModule,
    NgIconsModule,
    OverlayModule,
    DataTableComponent,
    ClickOutsideDirective,
    DxFormField,
    DxInput,
    DxOption,
    DxSelect,
    DxButton,
    DxTooltip,
    DxPrefix,
    SvgIconComponent,
  ],
  templateUrl: './tag.component.html',
  styleUrls: ['./tag.component.css'],
  providers: [
    provideIcons({
      heroEllipsisHorizontal,
      heroPencilSquare,
      heroTrash,
      heroMagnifyingGlass,
      heroPlus,
    }),
  ],
})
export class TagComponent implements OnInit {
  // Helper method to truncate text for display
  /*truncateText(text: string, maxLength: number = 100): string {
    if (!text) return '';
    return text.length > maxLength
      ? text.substring(0, maxLength) + '...'
      : text;
  }*/

  tagData = {
    name: '',
    application: '',
    config: '',
    is_active: true,
  };

  columns: IColumn[] = [
    {
      columnDef: 'index',
      flex: 0.1,
      headerName: 'No.',
    },
    {
      columnDef: 'name',
      minWidth: '200px',
      maxWidth: '300px',
      flex: 0.3,
      headerName: 'Tag name',
    },
    {
      columnDef: 'application',
      minWidth: '150px',
      maxWidth: '200px',
      flex: 0.2,
      headerName: 'Type',
      align: 'center',
    },
    {
      columnDef: 'created_by',
      minWidth: '150px',
      maxWidth: '200px',
      flex: 0.2,
      headerName: 'Created by',
    },
    {
      columnDef: 'updated_at',
      minWidth: '180px',
      maxWidth: '250px',
      headerName: 'Last update',
      flex: 0.3,
    },
    {
      columnDef: 'action',
      flex: 0.1,
      headerName: 'Action',
    },
  ];
  listTag: ITag[] = [];
  count: number = 0;

  applicationOptions: SelectOption[] = [
    {
      value: 'null',
      label: 'All Types',
    },
    {
      value: 'CONVERSATION',
      label: 'Conversation',
    },
    {
      value: 'FILE',
      label: 'Knowledge base',
    },
  ];

  statusOptions: SelectOption[] = [
    {
      value: 'null',
      label: 'All Statuses',
    },
    {
      value: 'TRUE',
      label: 'Active',
    },
    {
      value: 'FALSE',
      label: 'Inactive',
    },
  ];

  colors = [
    '#baf3db',
    '#f8e6a0',
    '#fedec8',
    '#ffd5d2',
    '#dfd8fd',
    '#4bce97',
    '#f5cd47',
    '#fea362',
    '#f87168',
    '#9f8fef',
    '#1f845a',
    '#946f00',
    '#c25100',
    '#c9372c',
    '#6e5dc6',
    '#cce0ff',
    '#c6edfb',
    '#d3f1a7',
    '#fdd0ec',
    '#dcdfe4',
    '#579dff',
    '#6cc3e0',
    '#94c748',
    '#e774bb',
    '#8590a2',
    '#0c66e4',
    '#227d9b',
    '#5b7f24',
    '#ae4787',
    '#626f86',
  ];
  selectedColor = '#baf3db';

  searchModel: any = {
    key_word: '',
    application: null,
    is_active: null,
    page: 0,
    pageSize: 10,
  };

  selectedApplication: string = 'null';
  selectedStatus: string = 'null';
  pageIndex: number = 0;
  private searchSubject = new Subject<string>();

  dialogService = inject(DxDialog);
  private locale: string = inject(LOCALE_ID);
  private tagService: TagService = inject(TagService);

  /*get displayedColumns(): any {
    return this.columns.map((c) => c.columnDef);
  }*/
  private snackBar = inject(DxSnackBar);

  constructor() {}

  ngOnInit() {
    // Explicitly set status to null (All) on initialization
    this.searchModel.application = null;
    this.searchModel.is_active = null;
    this.selectedApplication = 'null';
    this.selectedStatus = 'null';
    this.callListTag();
    this.searchSubject.pipe(debounceTime(300)).subscribe(() => {
      this.callListTag();
    });
  }

  setColor(color: string) {
    this.selectedColor = color;
    this.tagData.config = JSON.stringify({ color: color });
  }

  getColor(config: string): string {
    try {
      const parsedConfig = JSON.parse(config);
      return parsedConfig.color || '#000';
    } catch (error) {
      console.error('Invalid config:', config);
      return '#000'; // Default color if error
    }
  }

  handleAction(caseName: string, element: any) {
    // Close dropdown
    element.isActions = false;

    switch (caseName) {
      case 'edit':
        this.dialogService.open(AddOrEditTagComponent, {
          data: {
            id: element.id,
            name: element.name,
            application: element.application,
            config: element.config,
            is_active: element.is_active,
          },
          width: '20vw',
          minWidth: '400px',
        });
        break;
      case 'delete':
        this.dialogService
          .open(ConfirmDialogComponent, {
            data: {
              title: 'Delete this tag',
              content: 'Are you sure delete this tag ?',
              isDelete: true,
            },
            width: '300px',
          })
          .afterClosed()
          .subscribe((value: any) => {
            if (!!value) {
              this.tagService.deleteTag(element.id as number).subscribe({
                next: () => {
                  this.showSnackBar('Delete tag successfully', 'success');
                  this.callListTag();
                },
                error: (error) => {
                  // Handle error
                  // console.error('Error deleting tag:', error);
                  this.showSnackBar('Delete tag failed', 'error'); // Show error message
                },
              });
            }
          });
        break;
    }
  }

  changePage(event: any) {
    this.pageIndex = event.pageIndex; // Update the pageIndex for the data-table component
    this.searchModel.page = event.pageIndex;
    this.searchModel.pageSize = event.pageSize;
    this.callListTag();
  }

  /**
   * Get the row index for display in the table
   * This matches the behavior in the data-table component
   */
  getRowIndex(row: any): any {
    return (
      this.pageIndex * this.searchModel.pageSize + this.listTag.indexOf(row)
    );
  }

  createNewTag() {
    this.dialogService
      .open(AddOrEditTagComponent, {
        data: {
          name: '',
          application: '',
          config: '',
          is_active: true,
        },
        width: '20vw',
        minWidth: '400px',
      })
      .afterClosed()
      .subscribe(() => {
        this.callListTag();
      });
  }

  callListTag() {
    // Ensure pageIndex is synchronized with searchModel.page
    this.pageIndex = this.searchModel.page;

    // Prepare pagination params for query params
    const apiParams: any = {
      page: this.searchModel.page + 1, // API expects 1-based page index
      page_size: this.searchModel.pageSize,
    };

    // Prepare filter params for request body
    const body: any = {
      key_word: this.searchModel.key_word,
    };

    // Only add application if it's not null
    if (this.searchModel.application !== null) {
      body.application = this.searchModel.application;
    }

    // Only add is_active if it's not null
    if (this.searchModel.is_active !== null) {
      body.is_active = this.searchModel.is_active;
    }

    this.tagService.getListTag(apiParams, body).subscribe((res) => {
      this.listTag = res.items;
      this.count = res.total;
    });
  }

  changeFilter() {
    this.searchModel.page = 0;
    this.pageIndex = 0;
    this.searchSubject.next(this.searchModel.key_word);
    // this.callListTag();
  }

  // Handle search input change

  // Handle application filter change
  onApplicationChange(): void {
    // 'null' string means 'All Types'
    const value = this.selectedApplication;
    this.searchModel.application = value === 'null' ? null : value;
    this.changeFilter();
  }

  // Handle status filter change
  onStatusChange(): void {
    // 'null' string means 'All Statuses'
    const value = this.selectedStatus;
    if (value === 'null') {
      this.searchModel.is_active = null;
    } else if (value === 'TRUE') {
      this.searchModel.is_active = true;
    } else if (value === 'FALSE') {
      this.searchModel.is_active = false;
    }
    this.selectedStatus = value; // Keep selectedStatus in sync
    this.changeFilter();
  }

  getTextColor(bgColor: string): string {
    // Convert from hex to RGB
    const r = parseInt(bgColor.slice(1, 3), 16);
    const g = parseInt(bgColor.slice(3, 5), 16);
    const b = parseInt(bgColor.slice(5, 7), 16);

    // Calculate luminance
    const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

    // Decide text color
    return luminance > 128 ? 'black' : 'white';
  }

  onAction(event: any): void {
    const { type, data } = event;
    this.handleAction(type, data);
  }

  protected readonly formatDate = formatDate;

  getFormatDate(date: string | null) {
    const format = 'dd/MM/yyyy HH:mm:ss';
    const locale = this.locale;
    return date ? formatDate(date, format, locale) : '';
    /* row[column.columnDef]? (row[column.columnDef] + 'Z' | date : 'dd/MM/yyyy HH:mm:ss') : ''*/
  }
  showSnackBar(message: string, type: 'success' | 'error' | 'info') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }
}
