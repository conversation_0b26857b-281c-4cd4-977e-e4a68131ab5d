import { R<PERSON><PERSON>, R<PERSON><PERSON>_ACCOUNT, STATUS, TYPE_PLAN } from '@shared/app.constant';

/**
 * Interface for AI User data
 */
export interface IUserAi {
  id: number;
  user_id?: number;
  username: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  role?: ROLE;
  status: STATUS;
  invitation_id?: any;
}

/**
 * Interface for User Information
 */
export interface IUserInfo {
  id?: number;
  username?: string;
  email?: string;
  email_verified?: boolean;
  first_name?: string;
  last_name?: string;
  fullName?: string;
  role?: ROLE_ACCOUNT;
  plan?: TYPE_PLAN;
  plan_id?: number;
  start_date?: string;
  end_date?: string;
  last_login?: string;
  created_at?: string;
  updated_at?: string;
  isDeleting?: boolean;
}

/**
 * Interface for Payment Information
 */
export interface IPaymentInfo {
  id?: number;
  email?: string;
  first_name?: string;
  last_name?: string;
  fullName?: string;
  plan?: TYPE_PLAN;
  plan_id?: number;
  payment_method?: string;
  amount?: string;
  currency?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
  isDeleting?: boolean;
}

/**
 * Interface for User Response from API
 */
export interface IUserResponse {
  items: IUserInfo[];
  total: number;
}

/**
 * Interface for Payment Response from API
 */
export interface IPaymentResponse {
  items: IPaymentInfo[];
  total: number;
}
