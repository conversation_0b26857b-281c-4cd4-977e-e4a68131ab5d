// @ts-nocheck
import CsEdge from "@flow-editor/components/edge/CsEdge"
import MiniMapToolBar from "@flow-editor/components/flow/MiniMapToolbar"
import NodeContextMenu from "@flow-editor/components/flow/NodeContextMenu"
import ApiNode from "@flow-editor/components/nodes/api/ApiNode"
import ErrorNode from "@flow-editor/components/nodes/error/ErrorNode"
import EventTriggerNode from "@flow-editor/components/nodes/eventTrigger/EventTriggerNode"
import FunctionNode from "@flow-editor/components/nodes/function/FunctionNode"
import IfElseNode from "@flow-editor/components/nodes/ifElse/IfElseNode"
import InputNode from "@flow-editor/components/nodes/input/InputNode"
import OutputNode from "@flow-editor/components/nodes/output/OutputNode"
import ToolAINode from "@flow-editor/components/nodes/toolAI/ToolAINode"
import { MAX_ZOOM, MIN_ZOOM, SAVE_DEBOUNCE, STUDIO_STATUS } from "@flow-editor/constant"
import { useDeviceSupport, useFlowInstance } from "@flow-editor/hook"
import { inputNode } from "@flow-editor/init"
import {
  BuildFlowState,
  FlowInstanceState,
  IFlowCanvasProps,
  LayoutState,
  NodeFlow,
  NodeFlowData,
  StudioState
} from "@flow-editor/model"
import {
  useBuildFlowState,
  useFlowInstanceState,
  useLayoutState,
  useMenuState,
  useNodeHeaderState,
  useStudioState
} from "@flow-editor/store"
import { getUniqueNodeId, LocalStorageKey } from "@flow-editor/utils/flow"
import { toolFlowDevApi } from "@views/flow-editor/api"
import { ToolFlowDev } from "@views/flow-editor/model/bo"
import _, { debounce } from "lodash"
import * as React from "react"
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react"
import type { Edge, Node } from "reactflow"
import ReactFlow, { Background, Connection, SelectionMode, useEdgesState, useNodesState } from "reactflow"
import useUndoable from "use-undoable"

const nodeTypes = {
  _input: InputNode,
  _output: OutputNode,
  _api: ApiNode,
  _ifElse: IfElseNode,
  _function: FunctionNode,
  _error: ErrorNode,
  _toolAI: ToolAINode,
  _eventTrigger: EventTriggerNode
}
const edgeTypes = {cs: CsEdge}

const FlowCanvas = forwardRef((props: IFlowCanvasProps, ref) => {
  const {
    flowId,
    listNodeCollapsed,
    socket,
    conversationId,
    messageApi,
    onOpenModalConfirm,
    onOk,
    onCancel
  } = props

  const [nodes, setNodes, onNodesChange] = useNodesState<NodeFlowData>([])
  const [edges, setEdges, onEdgesChange] = useEdgesState<any>([])

  useImperativeHandle(ref, () => ({
    async triggerSave() {
      await handleSave(null, true, false)
    }
  }))

  const {deleteNodes} = useFlowInstance()
  const {isCtrlKeyPressed} = useDeviceSupport()

  /**
   * State
   */
  const [
    panningMouseButton,
    setPanningMouseButton
  ] = useState<number[]>([1]);
  const [
    draggedNode,
    setDraggedNode
  ] = useState(null)
  const [
    firstElements,
    setFirstElements
  ] = useState({
    nodes: [],
    edges: [],
  })
  const [
    elements,
    setElements,
    {
      past,
      future,
      undo,
      canUndo,
      redo,
      canRedo,
      reset
    }
  ] = useUndoable({
    nodes: [],
    edges: [],
  }, {behavior: 'mergePast'})
  const [
    paramSourceNode,
    setParamSourceNode
  ] = useState<{ source: string, sourceHandle: string }>(null)
  const [
    selecting,
    setSelecting
  ] = useState<boolean>(false)

  /**
   * Store
   */
  const {
    flowInstance,
    setFlowInstance
  } = useFlowInstanceState<FlowInstanceState>((state) => state)
  const {
    isDirty,
    flow,
    dialogShowing,
    triggerSave,
    setFlow,
    setDirty,
    setNotDirty
  } = useBuildFlowState<BuildFlowState>((state: BuildFlowState) => state)
  const {
    status
  } = useStudioState<StudioState>((state) => state)
  const {
    menu,
    setMenu
  } = useMenuState(state => state)
  const {
    theme
  } = useLayoutState<LayoutState>(state => state)
  const {isEditing} = useNodeHeaderState(state => state)

  /**
   * Ref
   * */
  const isConnectingEdgeRef = useRef(false);
  const menuOpenRef = useRef(false)
  const isSocketConnectingRef = useRef(false)
  const intervalAutoSaveRef = useRef(null)
  const timeoutAutoSaveRef = useRef(null)


  /**
   * Func
   */
  const handleUndo = useCallback(
    () => {
      if (!canUndo || _.isEqual(past[past.length - 1], firstElements)) {
        reset()
        setNodes([...firstElements.nodes])
        setEdges([...firstElements.edges])
        setElements({...firstElements})
      } else {
        undo()
        const lastPast = past?.[past.length - 1]
        if (!lastPast || lastPast.nodes.length === 0 || lastPast.edges.length === 0) {
          reset()
          setElements({...firstElements})
        } else {
          setNodes([...lastPast.nodes])
          setEdges([...lastPast.edges])
        }
      }
    },
    [canUndo, past, undo, reset, setNodes, setEdges, setElements]
  )

  const handleRedo = useCallback(
    () => {
      if (!canRedo || _.isEqual(elements, firstElements)) {
        setNodes([...elements.nodes])
        setEdges([...elements.edges])
      } else {
        redo()
        const firstFuture = future?.[0]
        if (!firstFuture || firstFuture.nodes.length === 0 || firstFuture.edges.length === 0) {
          reset()
          setElements({...firstElements})
        } else {
          setNodes([...firstFuture.nodes])
          setEdges([...firstFuture.edges])
        }
      }
    },
    [canRedo, future, elements, firstElements, redo, reset, setNodes, setEdges, setElements, setFirstElements]
  )

  const handleSave = useCallback(
    async (flowDataStorage?: string | null, showToastr: boolean = false, useKeys: boolean = false) => {
      if (flow) {
        const body: ToolFlowDev = {
          ...flow,
          flow_data: flowDataStorage && useKeys
            ? flowDataStorage
            : JSON.stringify({
              nodes: [...flowInstance.getNodes()].map(node => ({
                ...node,
                selected: false,
                data: {...node.data, selected: false},
                style: {...node.style, opacity: 1},
              })),
              edges: [...flowInstance.getEdges()].map(edge => {
                if (edge.data?.isGoToBlock) {
                  return {
                    ...edge,
                    animated: false,
                    style: {
                      ...edge.style,
                      opacity: 0,
                    },
                  }
                } else {
                  return {
                    ...edge,
                    animated: false,
                    style: {
                      ...edge.style,
                      stroke: theme === 'dark' ? 'white' : '#94a2b8',
                      strokeWidth: 1,
                      strokeOpacity: 1,
                      opacity: 1,
                    },
                  }
                }
              }),
            }),
        }

        await Promise.all([
          toolFlowDevApi.saveFlowDataDev(body),
        ])

        if (!flowDataStorage && showToastr) messageApi.success("Flow saved")

        if (useKeys) {
          localStorage.setItem(LocalStorageKey.flowDevData(body.id), body.flow_data)
          localStorage.setItem(LocalStorageKey.flowDevDataUpdatedAt(flow.id), new Date().toISOString())
        }

        setNotDirty()
      }
    },
    [flow, flowInstance, toolFlowDevApi, messageApi, setNotDirty, theme]
  )

  const handleCopy = useCallback(
    (e: KeyboardEvent) => {
      if (!flowInstance) return;

      const selectedContent = window.getSelection()?.toString();
      const selectedNodes = flowInstance.getNodes().filter((node) => node.selected);
      if (selectedContent && !selectedNodes.length) {
        void navigator.clipboard.writeText(selectedContent)
        return;
      }

      e.preventDefault()

      const selectedNodeIds = selectedNodes.map((node) => node.id);
      const selectedEdges = flowInstance
        .getEdges()
        .filter((edge) => selectedNodeIds.includes(edge.source) && selectedNodeIds.includes(edge.target));
      if (!selectedEdges.length && !selectedNodes.length) return;

      const dataToCopy = JSON.stringify({
        nodes: selectedNodes,
        edges: selectedEdges,
      });

      navigator.clipboard.writeText(dataToCopy)
        .then(() => messageApi.success('Data copied to clipboard'))
        .catch((err) => messageApi.error('Failed to copy:', err));
    },
    [flowInstance]
  );

  const handlePaste = useCallback(
    (e: KeyboardEvent) => {
      navigator.clipboard.readText()
        .then((data) => {
          if (!data) return

          // if (intervalAutoSaveRef.current) {
          //   clearInterval(intervalAutoSaveRef.current)
          // }

          let dataCopied = null;
          try {
            dataCopied = JSON.parse(data);
          } catch (err) {
          }

          if (dataCopied && typeof dataCopied === 'object' && dataCopied.nodes?.length) {
            if (!flowInstance) return;

            resetStyle()

            e.preventDefault()

            const {nodes: nodesCopied, edges: edgesCopied} = dataCopied;
            const oldNodeIds = [...nodesCopied].map(node => node.id)

            let nodePositionPastedKey = '';
            for (const key in localStorage) {
              if (key.includes('nodePositionPasted') && key.includes(String(flowId))) {
                nodePositionPastedKey = key;
                break;
              }
            }

            if (nodePositionPastedKey) {
              const positionStored = JSON.parse(localStorage.getItem(nodePositionPastedKey))
              if (positionStored) {
                nodesCopied.forEach((node) => {
                  const storedPosition = positionStored[node.id];
                  if (storedPosition) {
                    node.position = storedPosition.position;
                    node.positionAbsolute = storedPosition.positionAbsolute;
                  }
                })
              }
            }

            const mousePosition = JSON.parse(localStorage.getItem(LocalStorageKey.mousePositionFlow(flowId)))
            if (mousePosition) {
              const {x: mouseX, y: mouseY} = flowInstance.screenToFlowPosition(mousePosition)
              let minY = Infinity, minX = Infinity;
              for (let node of nodesCopied) {
                if (node.position.y < minY) {
                  minY = node.position.y;
                }
                if (node.position.x < minX) {
                  minX = node.position.x;
                }
              }
              const selectionRectPosition = {x: minX, y: minY}

              const deltaX = mouseX - selectionRectPosition.x;
              const deltaY = mouseY - selectionRectPosition.y;

              nodesCopied.forEach((node) => {
                const {position: oldPosition} = node
                node.position = {x: oldPosition.x + deltaX, y: oldPosition.y + deltaY};
              })
              localStorage.removeItem(LocalStorageKey.mousePositionFlow(flowId))
            }

            const newNodes = []
            nodesCopied.forEach((node) => {
              const oldNodeId = node.id;
              const newNodeId = getUniqueNodeId(node.data, [...new Set([...flowInstance.getNodes(), ...newNodes])], flowId);

              if (!mousePosition) {
                const position = {
                  x: node.position.x + 100, y: node.position.y + 50,
                };
                const positionAbsolute = {
                  x: node.positionAbsolute.x + 100, y: node.positionAbsolute.y + 50,
                };
                node.position = position
                node.positionAbsolute = positionAbsolute
              }
              node.id = newNodeId
              node.data = {...node.data, id: newNodeId, selected: true, oldId: oldNodeId}

              newNodes.push(node)

              edgesCopied.forEach((edge) => {
                if (edge.id.includes(oldNodeId)) {
                  edge.id = edge.id.replace(oldNodeId, newNodeId);
                }
                if (edge.source === oldNodeId) {
                  edge.source = edge.source.replace(oldNodeId, newNodeId);
                  edge.sourceHandle = edge.sourceHandle.replace(oldNodeId, newNodeId);
                }
                if (edge.target === oldNodeId) {
                  edge.target = edge.target.replace(oldNodeId, newNodeId);
                  edge.targetHandle = edge.targetHandle.replace(oldNodeId, newNodeId);
                }
                edge.animated = true
                if (!edge.data?.isGoToBlock) {
                  edge.style = {
                    ...edge.style,
                    stroke: node.data.node_color,
                    strokeWidth: 2,
                    strokeOpacity: 1,
                    opacity: 1
                  }
                } else {
                  edge.style = {
                    ...edge.style,
                    opacity: 0
                  }
                }
              });
            });

            const positionNodes = nodesCopied.reduce((acc, item) => {
              acc[item.data.oldId] = {position: item.position, positionAbsolute: item.positionAbsolute};
              return acc;
            }, {});
            localStorage.setItem(LocalStorageKey.nodePositionPasted(oldNodeIds.join('-')), JSON.stringify(positionNodes));

            try {
              if (nodesCopied.length) {
                setNodes((nodes) =>
                  [...nodes.concat([...nodesCopied])]
                )
              }
              if (edgesCopied.length) {
                setEdges((edges) =>
                  [...edges.concat([...edgesCopied])]
                )
              }
            } catch (err) {
              console.error(err);
            }
          }

          setDirty()
        })
        .catch((err) => console.error('Failed to read clipboard:', err));
    },
    [setNodes, setEdges, flowInstance]
  );

  const handleDelete = useCallback(
    () => {
      if (flowInstance) {
        const nodesDeleting = flowInstance.getNodes().filter((node) => node.selected && node.id !== 'input_0') || [];
        const nodeIds = nodesDeleting?.map(v => v.id)
        deleteNodes(nodeIds)
      }
    },
    [flowInstance, deleteNodes]
  )

  /**
   * Key/Mouse bindings
   */
  const handleKeyDown = useCallback(
    async (event: React.KeyboardEvent) => {
      const modifiers = [
        isCtrlKeyPressed(event) ? "Control" : "",
        event.altKey ? "Alt" : "",
        event.shiftKey ? "Shift" : "",
      ]
        .filter(Boolean)
        .join("-")

      const keyCombination = `${modifiers}-${event.key.toLowerCase()}`.replace(/-$/, "")

      switch (keyCombination) {
        case "Control-z":
          handleUndo()
          break
        case "Control-Shift-z":
          handleRedo()
          break
        case "Control-s":
          await handleSave(null, true, true)
          break
        case "-Delete":
        case "-Backspace":
          handleDelete()
          break;
        default:
          break
      }
    },
    [handleUndo, handleRedo, handleSave, handleCopy, handlePaste, handleDelete]
  )

  /**
   * Node
   * */
  const handleNodeClick = useCallback(
    () => {
      setMenu(null)
    },
    [setMenu]
  )

  /**
   * Drag & Drop
   */
  const handleNodeDragStart = useCallback(
    (_, node) => {
      setDraggedNode(node)
    },
    []
  )

  const handleNodeDragStop = useCallback(
    (event, node, nodes) => {
      if (draggedNode && _.isEqual(draggedNode, node)) {
        setDraggedNode(null)
        return
      }
      setDirty()
    },
    [draggedNode]
  )

  const handleSelectionDragStop = useCallback(
    (event: React.MouseEvent, _) => {
      setDirty()
    },
    []
  )

  /**
   * Connections / Edges
   */
  const handleConnectStart = useCallback(
    (event: React.MouseEvent, param) => {
      isSocketConnectingRef.current = true
      menuOpenRef.current = false
      const source = {source: param.nodeId, sourceHandle: param.handleId}
      setParamSourceNode(source)
    },
    []
  )

  const handleConnect = useCallback(
    (connection: Connection) => {
      isConnectingEdgeRef.current = true
      const newEdge = {
        ...connection,
        type: "cs",
        id: `${connection.source}-${connection.sourceHandle}-${connection.target}-${connection.targetHandle}`,
        data: {
          isGoToBlock: false
        }
      }

      const nodeAlreadyConnect = edges.find(v => v.source === paramSourceNode.source && v.sourceHandle === paramSourceNode.sourceHandle)
      setEdges((edges) => {
        if (nodeAlreadyConnect) {
          return edges.concat(newEdge).filter((edge) => edge.id !== nodeAlreadyConnect.id).map((edge) => edge)
        }
        return edges.concat(newEdge).map((edge) => edge)
      })

      setDirty()
    },
    []
  )

  const handleConnectEnd = useCallback(
    (event) => {
      event.preventDefault()
      if (!isConnectingEdgeRef.current) {
        setMenu({
          top: event.clientY > 134 ? event.clientY - 134 : 0,
          left: event.clientX > 320 ? (listNodeCollapsed ? event.clientX - 320 : event.clientX - 580) : 0
        });
      } else {
        isConnectingEdgeRef.current = false;
      }
      if (event.type == "contextmenu") {
        setParamSourceNode({source: "", sourceHandle: ""})
      }
    },
    [listNodeCollapsed]
  )

  /**
   * Edge and Nodes styled
   */
  const highlightNodeAndEdge = useCallback(
    (element: { nodes: Node[], edges: Edge[] }) => {
      resetStyle()
      if (element.nodes.length === 1) {
        const selectionNode = element.nodes[0]
        setNodes((nds) =>
          nds.map((node) => {
            if (node.id !== selectionNode.id) {
              node.style = {
                ...node.style,
                opacity: 0.3
              }
            }
            return node;
          })
        );
        setEdges((eds) =>
          eds.map((edge) => {
            if (!(edge.target === selectionNode.id || edge.source === selectionNode.id)) {
              if (!edge.data?.isGoToBlock) {
                edge.style = {
                  ...edge.style,
                  opacity: 0.3
                }
              } else {
                edge.style = {
                  ...edge.style,
                  opacity: 0
                }
              }
            } else {
              if (!edge.data?.isGoToBlock) {
                edge.animated = true
                edge.style = {
                  ...edge.style,
                  stroke: selectionNode.data.node_color,
                  strokeWidth: 2,
                  strokeOpacity: 1,
                  opacity: 1
                }
              } else {
                edge.animated = false
                edge.style = {
                  ...edge.style,
                  opacity: 0
                }
              }
            }
            return edge
          })
        )
      }

      if (element.nodes.length > 1) {
        const selectedNodeIds = element.nodes.map((node) => node.id);
        setNodes((nds) =>
          nds.map((node) => {
            node.style = {
              ...node.style,
              opacity: selectedNodeIds.includes(node.id) ? 1 : 0.3
            }
            return node;
          })
        );
        const selectedEdgesIds = element.edges.filter(edge => selectedNodeIds.includes(edge.source) && selectedNodeIds.includes(edge.target)).map(edge => edge.id);
        setEdges((eds) =>
          eds.map((edge) => {
            if (!selectedEdgesIds.includes(edge.id)) {
              if (!edge.data?.isGoToBlock) {
                edge.style = {
                  ...edge.style,
                  opacity: 0.3
                }
              } else {
                edge.style = {
                  ...edge.style,
                  opacity: 0
                }
              }
            } else {
              if (!edge.data?.isGoToBlock) {
                edge.animated = true
                edge.style = {
                  ...edge.style,
                  stroke: theme === 'dark' ? '#0059DC' : '#0059DC',
                  strokeWidth: 2,
                  strokeOpacity: 1,
                  opacity: 1
                }
              } else {
                edge.animated = false
                edge.style = {
                  ...edge.style,
                  opacity: 0
                }
              }
            }
            return edge
          })
        )
      }
    },
  )

  const resetStyle = useCallback(
    () => {
      setNodes((nds) =>
        nds.map((node) => {
          node.style = {
            ...node.style,
            opacity: 1,
          }
          return node;
        })
      );
      setEdges((eds) =>
        eds.map((edge) => {
          if (!edge.data?.isGoToBlock) {
            edge.animated = false
            edge.style = {
              ...edge.style,
              stroke: theme === 'dark' ? 'white' : '#94a2b8',
              strokeWidth: 1,
              strokeOpacity: 1,
              opacity: 1
            }
          } else {
            edge.animated = false
            edge.style = {
              ...edge.style,
              stroke: theme === 'dark' ? 'white' : '#94a2b8',
              opacity: 0
            }
          }
          return edge
        })
      )
    },
    [flowInstance, theme]
  )

  /**
   * Drag and drop
   */
  const handleDragOver = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault()
      event.dataTransfer.dropEffect = "move"
    },
    []
  )

  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault()
      if (
        !flow ||
        !status ||
        (flow && status && status === STUDIO_STATUS.LIVE)
      ) {
        void messageApi.warning("You cannot edit in production mode.")
        return
      }

      const nodeDataTransfer = event.dataTransfer.getData(
        "application/reactflow"
      )
      if (typeof nodeDataTransfer === "undefined" || !nodeDataTransfer) {
        return
      }
      const nodeData = JSON.parse(nodeDataTransfer)
      const position = flowInstance.screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      })
      const newNodeId = getUniqueNodeId(
        nodeData,
        flowInstance.getNodes(),
        flowId
      )

      const newNode: NodeFlow = {
        id: newNodeId,
        position,
        type: nodeData.node_type,
        data: {
          ...nodeData,
          id: newNodeId,
        },
      }

      setNodes((nodes) =>
        nodes.concat(newNode).map((node) => {
          node.data = {
            ...node.data,
            selected: false,
          }
          return node
        })
      )

      setDirty()
    },
    [flowInstance, flow]
  )

  /**
   * Panel
   */
  const handlePanelClick = useCallback(
    (event: React.MouseEvent) => {
      menuOpenRef.current = !menuOpenRef.current;
      setMenu(null)

      resetStyle()

      localStorage.setItem(LocalStorageKey.mousePositionFlow(flowId), JSON.stringify({
        x: event.clientX,
        y: event.clientY
      }))
    },
  )

  /**
   * Drag-select
   */
  const handleSelectionChangeDebounce = (element: { nodes: Node[], edges: Edge[] }) => {
    const resetNodesSelection = (selectedNodeIds: string[]) => {
      setNodes((nds) =>
        nds.map((node) => {
          node.data = {
            ...node.data,
            selected: selectedNodeIds.includes(node.id),
          };
          return node;
        })
      );
    };

    const nodeCount = element.nodes.length;
    if (nodeCount === 0) {
      setSelecting(false);
      resetNodesSelection([]);
      return;
    }

    const selectedNodeIds = element.nodes.map((node) => node.id);
    setSelecting(true);
    resetNodesSelection(selectedNodeIds);
    highlightNodeAndEdge(element);
  }

  const handleSelectionChange = useCallback(debounce(handleSelectionChangeDebounce, 250), []);

  /**
   * Lifecycle
   */
  useEffect(
    () => {
      if (status && flow) {
        /*
        * TODO: Open a confirmation modal to choose between using flow data saved in LocalStorage or from the database when the LocalStorage data is more recent than the database.
        * */
        if (localStorage.getItem(LocalStorageKey.flowDevDataUpdatedAt(flowId)) && localStorage.getItem(LocalStorageKey.flowDevData(flowId)) && new Date(flow.updated_at) > new Date(localStorage.getItem(LocalStorageKey.flowDevDataUpdatedAt(flowId)))) {
          if (intervalAutoSaveRef.current) clearInterval(intervalAutoSaveRef.current)
          onOpenModalConfirm()
          return
        }
        setNodes(
          JSON.parse(flow.flow_data)
            ? JSON.parse(flow.flow_data).nodes
            : [inputNode]
        )
        setEdges(
          JSON.parse(flow.flow_data) ? JSON.parse(flow.flow_data).edges : []
        )
        setFirstElements(JSON.parse(flow.flow_data) ? JSON.parse(flow.flow_data) : {nodes: [], edges: []})
        setElements(JSON.parse(flow.flow_data) ? JSON.parse(flow.flow_data) : {nodes: [], edges: []})
      }
    },
    [flowId, flow]
  )

  useEffect(() => {
    if (onOk) {
      const flow_data = JSON.parse(localStorage.getItem(LocalStorageKey.flowDevData(flowId)))
      setNodes(
        flow_data
          ? flow_data.nodes
          : [inputNode]
      )
      setEdges(
        flow_data ? flow_data.edges : []
      )
      setFirstElements(flow_data ? flow_data : {nodes: [], edges: []})
      setElements(flow_data ? flow_data : {nodes: [], edges: []})
    }
    if (onCancel) {
      setNodes(
        JSON.parse(flow.flow_data)
          ? JSON.parse(flow.flow_data).nodes
          : [inputNode]
      )
      setEdges(
        JSON.parse(flow.flow_data) ? JSON.parse(flow.flow_data).edges : []
      )
      setFirstElements(JSON.parse(flow.flow_data) ? JSON.parse(flow.flow_data) : {nodes: [], edges: []})
      setElements(JSON.parse(flow.flow_data) ? JSON.parse(flow.flow_data) : {nodes: [], edges: []})
    }
  }, [onOk, onCancel, flowId, flow]);

  useEffect(
    () => {
      const handleKeyDown = async (e: KeyboardEvent) => {
        const isCtrl = isCtrlKeyPressed(e)
        const isShift = e.shiftKey
        const key = e.key
        const isUndo = isCtrl && key.toLowerCase() === "z" && !isShift
        const isRedo = isCtrl && key.toLowerCase() === "z" && isShift
        const isSave = isCtrl && key.toLowerCase() === "s"
        const isCopy = isCtrl && key.toLowerCase() === "c"
        const isPaste = isCtrl && key.toLowerCase() === "v"
        const isDelete = key === 'Delete'
        const isBackspace = key === 'Backspace'
        const isHoldingCtrl = isCtrl

        if (isUndo) {
          e.preventDefault()
          if (!canUndo || _.isEqual(past[past.length - 1], firstElements)) {
            reset()
            setNodes([...firstElements.nodes])
            setEdges([...firstElements.edges])
            setElements({...firstElements})
          } else {
            undo()
            const lastPast = past?.[past.length - 1]
            if (!lastPast || lastPast.nodes.length === 0 || lastPast.edges.length === 0) {
              reset()
              setElements({...firstElements})
            } else {
              setNodes([...lastPast.nodes])
              setEdges([...lastPast.edges])
            }
          }
        }

        if (isRedo) {
          e.preventDefault()
          if (!canRedo || _.isEqual(elements, firstElements)) {
            setNodes([...elements.nodes])
            setEdges([...elements.edges])
          } else {
            redo()
            const firstFuture = future?.[0]
            if (!firstFuture || firstFuture.nodes.length === 0 || firstFuture.edges.length === 0) {
              reset()
              setElements({...firstElements})
            } else {
              setNodes([...firstFuture.nodes])
              setEdges([...firstFuture.edges])
            }
          }
        }

        if (isSave) {
          e.preventDefault()
          await handleSave(null, true, true)
          if (timeoutAutoSaveRef.current) {
            clearTimeout(timeoutAutoSaveRef.current)
          }
        }

        if (isHoldingCtrl) {
          setPanningMouseButton([0, 1]);
        }

        if (isCopy) {
          handleCopy(e)
        }

        if (isPaste) {
          handlePaste(e)
        }

        if ((isDelete || isBackspace) && !isEditing) {
          // e.preventDefault()
          handleDelete()
        }
      }

      const handleKeyUp = async (e: KeyboardEvent) => {
        const isCtrl = isCtrlKeyPressed(e)

        if (!isCtrl) {
          setPanningMouseButton([1]);
        }
      }

      window.addEventListener("keydown", handleKeyDown)
      window.addEventListener("keyup", handleKeyUp)
      return () => {
        window.removeEventListener("keydown", handleKeyDown)
        window.removeEventListener("keyup", handleKeyUp)
      }
    },
    [isDirty, elements, isEditing]
  )

  useEffect(
    () => {
      if (flowInstance && elements) {
        if (!_.isEqual({nodes: flowInstance.getNodes(), edges: flowInstance.getEdges()}, elements)) {
          setElements({
            nodes: flowInstance.getNodes(),
            edges: flowInstance.getEdges()
          })
        }
      }
    },
    [isDirty]
  )

  // useEffect(
  //   () => {
  //     const intervalSaveFlow = async () => {
  //       if (flowInstance && flow && status && status === STUDIO_STATUS.DEV) {
  //         const storageFlowData = localStorage.getItem(LocalStorageKey.flowDevData(flow.id))
  //         if (storageFlowData) {
  //           await handleSave(storageFlowData)
  //         } else {
  //           const flow_data = JSON.stringify({
  //             nodes: [...flowInstance.getNodes()].map(node => ({
  //               ...node,
  //               selected: false,
  //               data: {...node.data, selected: false},
  //               style: {...node.style, opacity: 1}
  //             })),
  //             edges: [...flowInstance.getEdges()].map(edge => {
  //               if (edge.data?.isGoToBlock) {
  //                 return {
  //                   ...edge,
  //                   animated: false,
  //                   style: {
  //                     ...edge.style,
  //                     opacity: 0
  //                   }
  //                 }
  //               } else {
  //                 return {
  //                   ...edge,
  //                   animated: false,
  //                   style: {
  //                     ...edge.style,
  //                     stroke: 'white',
  //                     strokeWidth: 1,
  //                     strokeOpacity: 1,
  //                     opacity: 1
  //                   }
  //                 }
  //               }
  //             }),
  //           })
  //           await handleSave(flow_data)
  //         }
  //       }
  //     }
  //
  //     if (flowInstance && flow && status && isDirty > 0) {
  //       if (intervalAutoSaveRef.current) {
  //         clearInterval(intervalAutoSaveRef.current)
  //       }
  //       intervalAutoSaveRef.current = setInterval(intervalSaveFlow, AUTO_SAVE_INTERVAL)
  //     }
  //
  //     return () => {
  //       if (intervalAutoSaveRef.current) {
  //         clearInterval(intervalAutoSaveRef.current)
  //       }
  //     }
  //   },
  //   [flowInstance, flow, status, isDirty]
  // )

  useEffect(
    () => {
      const autoSaveFlowToStorage = async () => {
        if (flowInstance && status === STUDIO_STATUS.DEV) {
          const flow_data = JSON.stringify({
            nodes: [...flowInstance.getNodes()].map(node => ({
              ...node,
              selected: false,
              data: {...node.data, selected: false},
              style: {...node.style, opacity: 1}
            })),
            edges: [...flowInstance.getEdges()].map(edge => {
              if (edge.data?.isGoToBlock) {
                return {
                  ...edge,
                  animated: false,
                  style: {
                    ...edge.style,
                    opacity: 0
                  }
                }
              } else {
                return {
                  ...edge,
                  animated: false,
                  style: {
                    ...edge.style,
                    stroke: theme === 'dark' ? 'white' : '#94a2b8',
                    strokeWidth: 1,
                    strokeOpacity: 1,
                    opacity: 1
                  }
                }
              }
            }),
          })
          localStorage.setItem(LocalStorageKey.flowDevData(flow?.id), flow_data)
          localStorage.setItem(LocalStorageKey.flowDevDataUpdatedAt(flow?.id), new Date().toISOString())
        }
      }

      if (status === STUDIO_STATUS.DEV) {
        timeoutAutoSaveRef.current = setTimeout(autoSaveFlowToStorage, SAVE_DEBOUNCE)
      }

      return () => {
        if (timeoutAutoSaveRef.current) {
          clearTimeout(timeoutAutoSaveRef.current)
        }
      }
    },
    [flowInstance, flow, status, theme]
  )

  useEffect(
    () => {
      setNodes((nds) =>
        nds.map((node) => {
          node.data = {
            ...node.data,
            selected: false
          }
          node.style = {
            ...node.style,
            opacity: 1
          }
          return node
        })
      )
      setEdges((eds) =>
        eds.map((edge) => {
          edge.animated = false
          edge.style = {
            ...edge.style,
            stroke: theme === 'dark' ? 'white' : '#94a2b8',
            strokeWidth: 1,
            strokeOpacity: 1,
            opacity: edge.data?.isGoToBlock ? 0 : 1
          }
          return edge
        })
      )
    },
    [setNodes, setEdges, theme]
  )

  useEffect(() => {
    const handleBeforeUnload = (event) => {
      localStorage.removeItem(LocalStorageKey.flowDevData(flowId))
      localStorage.removeItem(LocalStorageKey.flowDevDataUpdatedAt(flowId))
      localStorage.removeItem(LocalStorageKey.mousePositionFlow(flowId))
      for (const key in localStorage) {
        if (key.includes('nodePositionPasted') && key.includes(String(flowId))) {
          localStorage.removeItem(key)
        }
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  useEffect(
    () => {
      return () => {
        setFlowInstance(null)
        setFlow(null)
        setNodes([])
        setEdges([])
        setFirstElements({nodes: [], edges: []})
        setElements({nodes: [], edges: []})
        localStorage.removeItem(LocalStorageKey.flowDevData(flowId))
        localStorage.removeItem(LocalStorageKey.flowDevDataUpdatedAt(flowId))
        localStorage.removeItem(LocalStorageKey.mousePositionFlow(flowId))
        for (const key in localStorage) {
          if (key.includes('nodePositionPasted') && key.includes(String(flowId))) {
            localStorage.removeItem(key)
          }
        }
        setMenu(null)
        menuOpenRef.current = false
      };
    },
    [flowId]
  )

  useEffect(() => {
    handleSave(null, false, true);
  }, [triggerSave]);

  useEffect(() => {
    setTimeout(() => {
      resetStyle();
    }, 500);
  }, [theme])

  return (
    <ReactFlow
      fitView
      minZoom={MIN_ZOOM}
      maxZoom={MAX_ZOOM}
      nodes={nodes}
      edges={edges}
      nodeTypes={nodeTypes}
      edgeTypes={edgeTypes}
      onInit={setFlowInstance}
      onNodeClick={handleNodeClick}
      onNodeDragStart={handleNodeDragStart}
      onNodeDragStop={handleNodeDragStop}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onConnect={handleConnect}
      onConnectStart={handleConnectStart}
      onConnectEnd={handleConnectEnd}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onPaneClick={handlePanelClick}
      onPaneContextMenu={handleConnectEnd}
      multiSelectionKeyCode={dialogShowing ? null : ["Control", "Meta"]}
      onKeyDown={handleKeyDown}
      panOnDrag={panningMouseButton}
      selectionOnDrag={true}
      selectNodesOnDrag={true}
      selectionMode={SelectionMode.Partial}
      onSelectionChange={handleSelectionChange}
      onSelectionDragStop={handleSelectionDragStop}
      edgesUpdatable={status && status === STUDIO_STATUS.DEV && !menu}
      nodesDraggable={status && status === STUDIO_STATUS.DEV && !menu}
      nodesConnectable={status && status === STUDIO_STATUS.DEV && !menu}
      deleteKeyCode={null}
      zoomOnScroll={!menu}
    >
      <Background
        color="#94a2b8"
        gap={16}
        style={{opacity: selecting ? 0.4 : 1}}
      />
      <MiniMapToolBar/>
      {
        menu &&
        <NodeContextMenu
          onClick={handlePanelClick}
          {...{...menu, paramSourceNode, listNodeCollapsed}}
        />
      }
    </ReactFlow>
  )
})

export default FlowCanvas;
