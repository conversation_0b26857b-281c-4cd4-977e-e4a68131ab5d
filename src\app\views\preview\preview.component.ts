import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  effect,
  inject,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import {
  COMPLEX_MESSAGE_TYPE,
  STUDIO_STATUS,
  TRIGGER_KEYS,
  TYPE_MESSAGE_SOCKET,
} from '@core/constants';
import { TriggerService } from '@core/services';
import { SocketStore, StudioStore, UIStore } from '@core/stores';
import {
  DxFormField,
  DxInput,
  DxSnackBar,
  DxSuffix,
  DxTooltip,
} from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { heroPencilSquareMini } from '@ng-icons/heroicons/mini';
import {
  heroArrowPath,
  heroInformationCircle,
} from '@ng-icons/heroicons/outline';
import {
  heroExclamationTriangleSolid,
  heroPaperAirplaneSolid,
} from '@ng-icons/heroicons/solid';
import {
  ComplexMessageComponent,
  CustomIconComponent,
  ImageGalleryComponent,
  ImageGridComponent,
  SvgIconComponent,
} from '@shared/components';
import { AutosizeDirective } from '@shared/directives';
import { IMessage } from '@shared/models';
import { SafeHtmlPipe } from '@shared/pipes';
import {
  MessagesService,
  SettingsService,
  SocketService,
} from '@shared/services';
import { ChatUtils } from '@shared/utils';
import { NgScrollbar } from 'ngx-scrollbar';
import { debounceTime, filter, Subscription } from 'rxjs';
import { v4 as uuid } from 'uuid';

@Component({
  selector: 'app-preview',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgIconsModule,
    MatProgressSpinnerModule,
    ImageGalleryComponent,
    ImageGridComponent,
    ComplexMessageComponent,
    SafeHtmlPipe,
    NgScrollbar,
    CustomIconComponent,
    DxTooltip,
    DxInput,
    AutosizeDirective,
    DxFormField,
    SvgIconComponent,
    DxSuffix,
  ],
  templateUrl: './preview.component.html',
  styleUrl: './preview.component.css',
  providers: [
    provideIcons({
      heroInformationCircle,
      heroArrowPath,
      heroPencilSquareMini,
      heroExclamationTriangleSolid,
      heroPaperAirplaneSolid,
    }),
  ],
})
export class PreviewComponent implements OnInit, AfterViewInit {
  @ViewChild('scrollRef', { static: false }) scrollRef!: NgScrollbar;

  listChatMessage = signal<Array<IMessage>>([]);
  placeholder = signal<string>('Write a question');
  logoUrl = signal<string>('');
  header = signal<string>('');
  isLoadMessageAI = signal<boolean>(false);
  isSendingMessage = signal<boolean>(false);
  isReloadChat = signal<boolean>(true);
  isEditingMessage = signal<boolean>(false);
  editingMessageId = signal<string>('');
  isLoadingImage = signal<boolean>(false);
  isUserAtBottom = signal<boolean>(true);
  // Hold native scroll listener for cleanup
  private scrollListener: { el: HTMLElement; listener: () => void } | null = null;

  message: string = '';
  editedMessage: string = '';
  editFinalQuestion: string = '';

  private ai: string = '';
  private author: any = {
    type: 'user',
    data_info: this.getUserInfo(),
  };
  private isStudio: boolean = false;
  private partitionOrdinal: number = 0;
  private newChat: boolean = true;
  private isStreaming: Array<boolean> = [false];
  private streamingQueue: Array<any> = [];
  private isProcessingImage: boolean = false;
  private imageMarkdownBuffer: string = '';
  private complexMessageQueue: Array<any> = [];

  private UUID: string = uuid();
  private API_KEY = '';
  private hasJoinedCsRoom: boolean = false;
  private hasJoinedMessageRoom: boolean = false;

  private subscription: Subscription = new Subscription();

  uiStore = inject(UIStore);
  private socketService = inject(SocketService);
  private triggerService = inject(TriggerService);
  private settingsService = inject(SettingsService);
  private messageService = inject(MessagesService);
  private studioStore = inject(StudioStore);
  private socketStore = inject(SocketStore);
  private snackBar = inject(DxSnackBar);

  trackByChat(index: number, chat: IMessage): string | number {
    return chat.id && chat.id !== '' ? chat.id : index;
  }

  private effectFlowUnmounted = effect(() => {
    const flowUnmountedStatus = JSON.parse(
      this.triggerService.get(TRIGGER_KEYS.FLOW_UNMOUNTED)()
    );
    this.handleFlowUnmounted(flowUnmountedStatus);
  });

  private effectSocketCs = effect(() => {
    const connected = this.socketStore.socketCsConnected();
    if (connected === 'success' && !this.hasJoinedCsRoom) {
      this.socketService.joinRoomConversationCs({
        prevConversationId: this.UUID,
        conversationId: this.UUID,
      });
      this.hasJoinedCsRoom = true;
    }
  });

  private effectSocketMessage = effect(() => {
    const connected = this.socketStore.socketMessageConnected();
    if (connected === 'success' && !this.hasJoinedMessageRoom) {
      this.socketService.joinRoomConversationMessage({
        prevConversationId: this.UUID,
        conversationId: this.UUID,
      });
      this.hasJoinedMessageRoom = true;
    }
  });

  private effectReCallSettings = effect(() => {
    const reCallSettings = this.triggerService.get(
      TRIGGER_KEYS.RE_CALL_SETTINGS
    )();
    if (reCallSettings) {
      this.getInitSetting();
    }
  });

  private effectScrollToBottom = effect(() => {
    // Whenever chat messages array changes, scroll to bottom if user is already at bottom
    const _ = this.listChatMessage();
    if (this.isUserAtBottom()) {
      // Delay to allow DOM update
      setTimeout(() => {
        this.scrollToBottomOfNgScrollBar();
      }, 50);
    }
  });

  // Log scroll position for debugging purposes
  private effectScrollPositionLog = effect(() => {
    console.log('isUserAtBottom:', this.isUserAtBottom());
  });

  ngOnInit(): void {
    this.getInitSetting();
    this.getListAPIKey();
    this.setupSocketSubscriptions();
  }

  private setupSocketSubscriptions(): void {
    let partitionOrdinalSub = this.socketService
      .listenEventMessage('partition_ordinal')
      .pipe(
        filter(
          (message: any) => message && message.conversation_id === this.UUID
        )
      );

    let messageSub = this.socketService
      .listenEventMessage('message')
      .pipe(
        filter(
          (message: any) =>
            message &&
            message.content &&
            message.content.conversation_id === this.UUID
        )
      );

    this.subscription.add(
      partitionOrdinalSub.subscribe((message) => {
        this.handleMEventPartitionOrdinal(message);
      })
    );

    this.subscription.add(
      messageSub.subscribe((message) => {
        this.handleMEventMessage(message);
      })
    );

    this.subscription.add(
      this.socketService
        .listenEventCs('message')
        .pipe(
          filter(
            (message: any) =>
              message &&
              message.content &&
              message.content.conversation_id === this.UUID
          )
        )
        .subscribe((message) => {
          this.handleMEventCs(message);
        })
    );

    this.subscription.add(
      this.socketService
        .listenEventCs('message_mapping')
        .pipe(
          debounceTime(200),
          filter(
            (message: any) => message && message.conversation_id === this.UUID
          )
        )
        .subscribe((message) => {
          this.handleMEventMessageMapping(message);
        })
    );
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.scrollToBottomOfNgScrollBar();
      this.updateTextAreaHeight();
    }, 100);

    // Listen to scroll events to track user position
    // Fallback: if vmScrolled event not used, attach listener to viewport
    const nativeEl = (this.scrollRef as any)?.viewport?.nativeElement || (this.scrollRef as any)?.elementRef?.nativeElement;
    if (nativeEl) {
      const el = nativeEl as HTMLElement;
      const listener = () => this.updateUserScrollPosition();
      el.addEventListener('scroll', listener);
      this.scrollListener = { el, listener } as any;
    }
  }

  updateTextAreaHeight() {
    const textarea = document.getElementById('inboxChat') as HTMLTextAreaElement | null;
    if (textarea) {
      // Reset previous height to calculate the correct scrollHeight
      textarea.style.height = 'auto';
      // Determine the desired height, capped at 200px
      const desiredHeight = Math.min(textarea.scrollHeight + 2, 200);
      textarea.style.height = desiredHeight + 'px';
      // Toggle scrollbar when content exceeds the cap
      textarea.style.overflowY = textarea.scrollHeight > 200 ? 'auto' : 'hidden';
    }

    if (textarea && this.message.trim() === '') {
      textarea.style.height = 'auto';
    }
  }

  adjustHeight(event: Event) {
    const textarea = event.target as HTMLTextAreaElement;
    // Reset to auto to recalculate the scrollHeight on each input
    textarea.style.height = 'auto';
    const desiredHeight = Math.min(textarea.scrollHeight, 200);
    textarea.style.height = desiredHeight + 'px';
    textarea.style.overflowY = textarea.scrollHeight > 200 ? 'auto' : 'hidden';
  }

  onKeyDown(event: KeyboardEvent) {
    if (event.altKey && event.key === 'Enter') {
      event.preventDefault();
      let value = (event.target as HTMLTextAreaElement).value;
      let selectionStart = (event.target as HTMLTextAreaElement).selectionStart;
      let selectionEnd = (event.target as HTMLTextAreaElement).selectionEnd;

      (event.target as HTMLTextAreaElement).value =
        value.substring(0, selectionStart) +
        '\n' +
        value.substring(selectionEnd);

      (event.target as HTMLTextAreaElement).selectionStart = (
        event.target as HTMLTextAreaElement
      ).selectionEnd = selectionStart + 1;
      this.updateTextAreaHeight();
    } else if (event.key === 'Enter') {
      event.preventDefault();
      this.replyMessage();
      this.updateTextAreaHeight();
    }
  }

  replyMessage(action: any = null) {
    if (this.isLoadMessageAI()) {
      return;
    }

    if (
      ((this.message && this.message.trim() !== '') || action) &&
      !this.isSendingMessage()
    ) {
      
      this.isSendingMessage.set(true);
      this.message = this.message.trim().replace(/[\r\n]+/g, ' ');

      let message = this.message;
      let actionId = '';

      if (action) {
        if (action.link) {
          window.open(action.link, '_blank');
        }
        if (action.value) {
          message = action.value.trim().replace(/[\r\n]+/g, ' ');
          actionId = action.action_id;
        } else {
          return;
        }
      }

      const textAliases = this.findTextAliasesInteractiveButton(message);
      if (textAliases.length > 0) {
        if (textAliases[0].link) {
          window.open(textAliases[0].link, '_blank');
        }
        if (textAliases[0].value) {
          message = textAliases[0].value.trim().replace(/[\r\n]+/g, ' ');
          actionId = textAliases[0].actionId;
        } else {
          return;
        }
      }

      const body = {
        author: this.author,
        content: message,
        interactive: Boolean(actionId),
        action_id: actionId,
        internal:
          this.isStudio && this.studioStore.status() === STUDIO_STATUS.DEV,
        debug: this.studioStore.debugModeSnapshot(),
        preview: true,
        partition_ordinal: this.partitionOrdinal,
      };

      const messages = this.listChatMessage();
      if (messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        if (
          lastMessage.content &&
          lastMessage.content.send_type === 'complex'
        ) {
          this.listChatMessage.update((msgs) => {
            const newMsgs = [...msgs];
            newMsgs.pop();
            return newMsgs;
          });
          let content = '';
          if (
            lastMessage.content.complex_type ===
            COMPLEX_MESSAGE_TYPE.INTERACTIVE
          ) {
            content = lastMessage.content.text.trim();
          }
          if (
            lastMessage.content.complex_type === COMPLEX_MESSAGE_TYPE.RICH_CARD
          ) {
            content = `<div>${
              lastMessage.content.image_url
                ? `<img src="${lastMessage.content.image_url}" alt="${lastMessage.content.image_url}" class="zoom n-margin">`
                : ''
            }</div><br><b>${lastMessage.content.title.trim()}</b><br>${lastMessage.content.subtitle.trim()}
            `;
          }
          if (
            lastMessage.content.buttons &&
            lastMessage.content.buttons.length > 0
          ) {
            const buttonValues = lastMessage.content.buttons
              .map((button: { value: string }) => button.value)
              .filter((value: string) => value)
              .join('\n- ');
            if (buttonValues) {
              content += '\n- ' + buttonValues;
            }
          }
          this.listChatMessage.update((msgs) => [
            ...msgs,
            {
              role: 'assistant',
              content: content,
              typeMessage: 'text',
              created_at: new Date().toISOString(),
            },
          ]);
        }
      }
      this.listChatMessage.update((msgs) => [
        ...msgs,
        {
          role: 'user',
          content:
            textAliases.length > 0
              ? ChatUtils.extractUrls(this.message.trim(), 'user')
              : ChatUtils.extractUrls(message.trim(), 'user'),
          created_at: new Date().toISOString(),
        },
      ]);
      this.isLoadMessageAI.set(true);
      this.isSendingMessage.set(true);
      this.socketService.sendMessage(body, this.UUID);
      this.message = '';
      this.scrollToBottomOfNgScrollBar();
    }
  }

  reloadChat(onClick: boolean = false) {
    const prevConversationId = this.UUID;
    this.UUID = uuid();

    postMessage({ type: 'conversation_id', data: this.UUID });
    this.newChat = true;
    this.message = '';
    this.isLoadMessageAI.set(true);
    this.isSendingMessage.set(true);
    this.listChatMessage.set([]);
    this.isStreaming = [false];
    this.hasJoinedCsRoom = false;
    this.hasJoinedMessageRoom = false;

    this.subscription.unsubscribe();
    this.subscription = new Subscription();
    this.setupSocketSubscriptions();

    if (onClick) {
      this.isReloadChat.set(true);
      this.getInitSetting();
    }

    this.socketService.joinRoomConversationCs({
      prevConversationId,
      conversationId: this.UUID,
    });

    this.socketService.joinRoomConversationMessage({
      prevConversationId,
      conversationId: this.UUID,
    });

    this.socketService.callStartFlow({
      author: this.author,
      content: this.message,
      //       internal:
      //         !JSON.parse(this.triggerService.get(TRIGGER_KEYS.FLOW_UNMOUNTED)()) &&
      //         this.studioStore.status() === STUDIO_STATUS.DEV,
      internal:
        this.isStudio && this.studioStore.status() === STUDIO_STATUS.DEV,
      debug:
        !JSON.parse(this.triggerService.get(TRIGGER_KEYS.FLOW_UNMOUNTED)()) &&
        this.studioStore.status() === STUDIO_STATUS.DEV,
      preview: true,
      conversationId: this.UUID,
      partition_ordinal: this.partitionOrdinal,
    });

    this.socketStore.setHasCallStartFlow(true);
  }

  editMessage(msg: IMessage) {
    const isValidMessage = msg.id && msg.conversation_id && msg.role === 'user';
    const canEditMessage = !this.isEditingMessage() && !this.editingMessageId();

    if (isValidMessage && canEditMessage && msg.id) {
      this.isEditingMessage.set(true);
      this.editingMessageId.set(msg.id);
      this.editedMessage = msg.content;
      this.editFinalQuestion = msg.content;
    }
  }

  sendEditedMessage(msg: IMessage) {
    const isValidMessage = msg.id && msg.conversation_id && msg.role === 'user';
    const canSendEditMessage =
      this.isEditingMessage() && this.editingMessageId() && this.editedMessage;

    if (isValidMessage && canSendEditMessage && msg.conversation_id) {
      const content = this.editedMessage;
      const body = {
        author: {
          type: 'user',
          data_info: this.getUserInfo(),
        },
        id: msg.id,
        content,
        //         internal:
        //           !JSON.parse(this.triggerService.get(TRIGGER_KEYS.FLOW_UNMOUNTED)()) &&
        //           this.studioStore.status() === STUDIO_STATUS.DEV,
        internal:
          this.isStudio && this.studioStore.status() === STUDIO_STATUS.DEV,
        debug: this.studioStore.debugModeSnapshot(),
        preview: true,
        partition_ordinal: this.partitionOrdinal,
        edit_final_question: content,
      };

      this.socketService.sendEditedMessage(body, msg.conversation_id);
      this.isEditingMessage.set(false);
      this.editingMessageId.set('');
      this.editedMessage = '';
      this.editFinalQuestion = '';
      this.listChatMessage.update((messages) => {
        const updatedMessages = messages.map((item) =>
          item.id === msg.id ? { ...item, content } : item
        );
        return updatedMessages.slice(
          0,
          updatedMessages.findIndex((item) => item.id === msg.id) + 1
        );
      });
      this.isSendingMessage.set(true);
      this.isLoadMessageAI.set(true);
    }
  }

  cancelEditMessage(msg: IMessage) {
    this.isEditingMessage.set(false);
    this.isLoadMessageAI.set(false);
    this.editingMessageId.set('');
    this.editedMessage = msg.content;
    this.editedMessage = '';
  }

  private handleMEventCs(message: any) {
    if (
      message &&
      message.type === TYPE_MESSAGE_SOCKET.NEW_MESSAGE_HUMAN_REPLY
    ) {
      const data = message.content.data_reply;
      const typeMessage = ChatUtils.classifyMessage(data.content);
      const showSource = ChatUtils.isShowSource(data.content);
      const currentMessages = this.listChatMessage() || [];
      this.listChatMessage.set([
        ...currentMessages,
        {
          ...data,
          role: data.author ? JSON.parse(data.author)?.type : null,
          content: ChatUtils.extractUrls(
            data.content,
            data.author ? JSON.parse(data.author)?.type : null
          ),
          typeMessage: typeMessage,
          created_at: new Date().toISOString(),
          showSource: showSource,
          listTextSources: showSource
            ? ChatUtils.extractSources(data.content)
            : [],
        },
      ]);
      this.isLoadMessageAI.set(false);
      this.isSendingMessage.set(false);
    }
    if (message && message.type === TYPE_MESSAGE_SOCKET.ERROR_MESSAGE) {
      const data = message.content.content;
      this.snackBar.open(data.content, '', {
        panelClass: 'dx-snack-bar-error',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
      this.isLoadMessageAI.set(false);
      this.isSendingMessage.set(false);
    }
  }

  private handleMEventMessageMapping(messageMapping: any) {
    const currentMessages = this.listChatMessage() || [];
    const updatedMessages = currentMessages.map((msg: IMessage) => {
      if (msg.content === messageMapping.content) {
        return { ...msg, ...messageMapping };
      }
      return msg;
    });
    this.listChatMessage.set(updatedMessages);
  }

  private handleMEventPartitionOrdinal(message: any) {
    this.partitionOrdinal = message.partition_ordinal;
    if (!this.socketStore.hasCallStartFlow()) {
      this.socketService.callStartFlow({
        author: this.author,
        content: this.message,
        //         internal:
        //           !JSON.parse(this.triggerService.get(TRIGGER_KEYS.FLOW_UNMOUNTED)()) &&
        //           this.studioStore.status() === STUDIO_STATUS.DEV,
        internal:
          this.isStudio && this.studioStore.status() === STUDIO_STATUS.DEV,
        debug:
          !JSON.parse(this.triggerService.get(TRIGGER_KEYS.FLOW_UNMOUNTED)()) &&
          this.studioStore.status() === STUDIO_STATUS.DEV,
        preview: true,
        conversationId: this.UUID,
        partition_ordinal: this.partitionOrdinal,
      });

      this.socketStore.setHasCallStartFlow(true);
    }
  }

  private handleMEventMessage(message: any) {
    if (
      message &&
      message.type === TYPE_MESSAGE_SOCKET.NEW_MESSAGE_FLOW_START
    ) {
      const data = message.content.content;
      this.listChatMessage.update((messages) => [
        ...messages,
        {
          ...data,
          role: 'assistant',
          content: ChatUtils.extractUrls(
            data.message_response,
            data.author ? JSON.parse(data.author)?.type : null
          ),
          typeMessage: 'text',
          created_at: new Date().toISOString(),
          listImages: [],
          listTextSources: [],
        },
      ]);
    }

    if (message && message.type === TYPE_MESSAGE_SOCKET.NEW_MESSAGE_CS_CHAT) {
      const data = message.content.content;
      const messages = this.listChatMessage();
      const lastMessage = messages[messages.length - 1];

      if (data.content.includes('DONE')) {
        this.isLoadMessageAI.set(false);
        this.newChat = true;

        if (this.isSendingMessage()) {
          this.isSendingMessage.set(false);
        }
        const lastQueueItem = lastMessage.queue[lastMessage.queue.length - 1];
        this.listChatMessage.update((messages) => {
          const newMessages = [...messages];
          if (lastQueueItem) {
            newMessages[newMessages.length - 1].queue[
              lastMessage.queue.length - 1
            ] = lastQueueItem.replace(/\n+$/, '');
          }
          return newMessages;
        });
        return;
      } else {
        const typeMessage = ChatUtils.classifyMessage(data.content);
        const showSource = ChatUtils.isShowSource(data.content);
        const queue = ChatUtils.checkTheVoid(data.content);

        if (lastMessage?.typeMessage === 'logging' || this.newChat) {
          this.newChat = false;
          this.listChatMessage.update((messages) => [
            ...messages,
            {
              ...data,
              role: data.author ? JSON.parse(data.author)?.type : null,
              content: '',
              queue: queue ? [queue] : [],
              typeMessage: typeMessage,
              listImages:
                typeMessage === 'grid' || typeMessage === 'gallery'
                  ? ChatUtils.extractImages(data.content)
                  : '',
              created_at: new Date().toISOString(),
              showSource: showSource,
              listTextSources: showSource
                ? ChatUtils.extractSources(data.content)
                : [],
            },
          ]);

          this.streamingQueue.push(this.listChatMessage().length - 1);
          if (
            !this.isStreaming[this.listChatMessage().length - 1] &&
            this.streamingQueue[0] == this.listChatMessage().length - 1
          ) {
            this.isStreaming[this.listChatMessage().length - 1] = true;
            this.streamText(this.listChatMessage().length - 1);
          }
        } else {
          this.listChatMessage.update((messages) => {
            const newMessages = [...messages];
            newMessages[newMessages.length - 1].queue.push(data.content);
            newMessages[newMessages.length - 1].typeMessage = typeMessage;
            newMessages[newMessages.length - 1].listImages =
              typeMessage === 'grid' || typeMessage === 'gallery'
                ? ChatUtils.extractImages(data.content)
                : '';
            newMessages[newMessages.length - 1].showSource = showSource;
            newMessages[newMessages.length - 1].listTextSources = showSource
              ? ChatUtils.extractSources(data.content)
              : [];
            return newMessages;
          });

          if (
            !this.streamingQueue.includes(this.listChatMessage().length - 1)
          ) {
            this.streamingQueue.push(this.listChatMessage().length - 1);
          }
          if (
            !this.isStreaming[this.listChatMessage().length - 1] &&
            this.streamingQueue[0] == this.listChatMessage().length - 1
          ) {
            this.isStreaming[this.listChatMessage().length - 1] = true;
            this.streamText(this.listChatMessage().length - 1);
          }
        }
      }
    }
    if (
      message &&
      message.type === TYPE_MESSAGE_SOCKET.NEW_MESSAGE_CS_CHAT_PAUSE
    ) {
      const data = message.content.content;
      if (data) {
        this.isLoadMessageAI.set(false);
        this.isSendingMessage.set(false);
      }
    }
    if (
      message &&
      message.type === TYPE_MESSAGE_SOCKET.NEW_MESSAGE_FLOW_LOGGING
    ) {
      const logging = message.content.logging;
      this.listChatMessage.update((messages) => [
        ...messages,
        {
          ...logging,
          role: 'assistant',
          content: logging,
          typeMessage: 'logging',
          created_at: new Date().toISOString(),
        },
      ]);
    }
    if (
      message &&
      message.type === TYPE_MESSAGE_SOCKET.NEW_MESSAGE_FLOW_ERROR
    ) {
      const error = message.content.error;
      this.listChatMessage.update((messages) => [
        ...messages,
        {
          ...error,
          role: 'assistant',
          content: error.message,
          node: error?.node_id,
          typeMessage: 'error',
          created_at: new Date().toISOString(),
        },
      ]);
    }
    if (
      message &&
      message.type === TYPE_MESSAGE_SOCKET.NEW_MESSAGE_COMPLEX_CS_CHAT
    ) {
      const data = message.content.content;
      const content = JSON.parse(data.content);
      const complexMessage = {
        ...data,
        role: 'user',
        content,
        typeMessage: 'complex',
        created_at: new Date().toISOString(),
        listImages: [],
        listTextSources: [],
      };

      if (this.isStreaming.some((isStreaming) => isStreaming)) {
        this.complexMessageQueue.push(complexMessage);
      } else {
        this.listChatMessage.update((messages) => [
          ...messages,
          complexMessage,
        ]);
      }

      this.isLoadMessageAI.set(false);
      this.isSendingMessage.set(false);
    }
    if (message && message.type === TYPE_MESSAGE_SOCKET.SAVE_USER_INFO) {
      this.handleSaveUserInfo(message);
    }
    if (this.isUserAtBottom()) {
      // Delay to allow DOM update
      setTimeout(() => {
        this.scrollToBottomOfNgScrollBar();
      }, 50);
    }
  }

  private findTextAliasesInteractiveButton(message: string): any[] {
    const textAliases: any[] = [];
    const messages = this.listChatMessage();

    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (
        lastMessage.content &&
        lastMessage.content.buttons &&
        lastMessage.content.buttons.length > 0
      ) {
        lastMessage.content.buttons.forEach(
          (btn: {
            text_aliases?: string[];
            value?: string;
            link?: string;
            action_id?: string;
          }) => {
            if (btn.text_aliases && Array.isArray(btn.text_aliases)) {
              btn.text_aliases.forEach((alias: string) => {
                if (alias.toLowerCase().includes(message.toLowerCase())) {
                  textAliases.push({
                    value: btn.value,
                    link: btn.link,
                    actionId: btn.action_id,
                  });
                }
              });
            }
          }
        );
      }
    }

    return textAliases;
  }

  private streamText(index: number): void {
    const messages = this.listChatMessage();
    if (messages[index].queue.length === 0) {
      this.isStreaming[index] = false;
      this.streamingQueue = this.streamingQueue.filter(
        (number) => number !== index
      );
      if (
        this.streamingQueue.length > 0 &&
        !this.isStreaming[this.streamingQueue[0]]
      ) {
        this.isStreaming[this.streamingQueue[0]] = true;
        this.streamText(this.streamingQueue[0]);
      }
      if (
        this.streamingQueue.length == 0 &&
        !this.isStreaming[index] &&
        this.newChat
      ) {
        this.isSendingMessage.set(false);
        if (this.complexMessageQueue.length > 0) {
          const complexMessage = this.complexMessageQueue.shift();
          this.listChatMessage.update((messages) => [
            ...messages,
            complexMessage,
          ]);
        }
      }
      return;
    }
    this.isSendingMessage.set(true);
    const message = messages[index];
    const currentText = ChatUtils.extractUrls(
      message.queue.shift(),
      message.role
    );
    let charIndex = 0;
    let isInAnchorTag = false;

    const addCharacter = () => {
      if (charIndex < currentText.length) {
        const currentChar = currentText[charIndex];
        if (
          !this.isProcessingImage &&
          currentChar === '!' &&
          currentText[charIndex + 1] === '['
        ) {
          this.isProcessingImage = true;
          this.imageMarkdownBuffer = currentChar;
          this.isLoadingImage.set(true);
          charIndex++;
          setTimeout(addCharacter, 20);
        } else if (this.isProcessingImage) {
          this.imageMarkdownBuffer += currentChar;
          if (currentChar === ')') {
            const imageMatch =
              this.imageMarkdownBuffer.match(/!\[(.*?)\]\((.*?)\)/);
            if (imageMatch) {
              const altText = imageMatch[1];
              const src = imageMatch[2];
              const imgTag = `<img id="imgCheck" class="rounded-xl zoom" src="${src}" alt="${altText}">`;

              this.listChatMessage.update((messages) => {
                const newMessages = [...messages];
                newMessages[index].content += imgTag;
                return newMessages;
              });
            }
            this.isProcessingImage = false;
            this.imageMarkdownBuffer = '';
            this.isLoadingImage.set(false);
          }
          charIndex++;
          setTimeout(addCharacter, 10);
          return;
        }
        if (!this.isProcessingImage) {
          if (
            currentChar === '<' &&
            currentText.substring(charIndex, charIndex + 3) === '<a '
          ) {
            isInAnchorTag = true;
            this.listChatMessage.update((messages) => {
              const newMessages = [...messages];
              newMessages[index].content += currentText.substring(
                charIndex,
                charIndex + 3
              );
              return newMessages;
            });
            charIndex += 3;
          } else if (
            isInAnchorTag &&
            currentChar === '>' &&
            currentText.substring(charIndex - 1, charIndex + 1) === '</a>'
          ) {
            this.listChatMessage.update((messages) => {
              const newMessages = [...messages];
              newMessages[index].content += currentText[charIndex];
              return newMessages;
            });
            isInAnchorTag = false;
            charIndex++;
          } else if (
            currentChar === '<' &&
            currentText.substring(charIndex, charIndex + 4) === '<img'
          ) {
            this.isLoadingImage.set(true);
          }

          this.listChatMessage.update((messages) => {
            const newMessages = [...messages];
            newMessages[index].content += currentChar;
            return newMessages;
          });
          if (currentChar === '>') {
            setTimeout(() => {
              if (this.isLoadingImage()) this.isLoadingImage.set(false);
            }, 0);
          }

          charIndex++;
          setTimeout(addCharacter, 10);
          if (
            this.listChatMessage() &&
            this.listChatMessage().filter((msg) => msg.role === 'user')
              .length === 1
          ) {
            if (this.isUserAtBottom()) {
              // Delay to allow DOM update
              setTimeout(() => {
                this.scrollToBottomOfNgScrollBar();
              }, 50);
            }
          }
        }
      } else {
        this.streamText(index);
      }
    };

    addCharacter();
  }

  private getInitSetting() {
    this.settingsService.getDetailSetting().subscribe({
      next: (res) => {
        if (res && res.id) {
          this.ai = res.id;
          this.placeholder.set(res.settings.widget.input_placeholder);
          this.logoUrl.set(res.settings.widget.logo_url);
          this.header.set(res.settings.widget.header);
          this.isSendingMessage.set(false);
          this.isReloadChat.set(false);
        }
      },
      error: (err) => {
        this.listChatMessage.set([
          {
            role: 'assistant',
            content: "Hey, I'm here to answer any questions you might have!",
            typeMessage: 'text',
          },
        ]);
        this.placeholder.set('Write a question');
        this.logoUrl.set('');
        this.header.set('');
        this.isSendingMessage.set(false);
        this.isReloadChat.set(false);
      },
    });
  }

  private getListAPIKey() {
    this.messageService.getListAPIKey().subscribe({
      next: (res) => {
        this.API_KEY = res[0].api_key;
      },
    });
  }

  private handleFlowUnmounted(status: boolean) {
    if (status === this.isStudio) {
      this.isStudio = !status;
    }
  }

  private scrollToBottomOfNgScrollBar(): void {
    const maxRetries = 20;
    let attempt = 0;

    const checkAndScroll = () => {
      attempt++;
      if (this.scrollRef) {
        this.scrollRef.scrollTo({ bottom: 0, duration: 0 });
      } else if (attempt < maxRetries) {
        setTimeout(checkAndScroll, 100);
      }
    };
    checkAndScroll();
  }

  private updateUserScrollPosition(): void {
    try {
      const el = (this.scrollRef as any)?.viewport?.nativeElement || (this.scrollRef as any)?.elementRef?.nativeElement;
      if (!el) return;
      const threshold = 10; // px
      const atBottom = el.scrollHeight - el.scrollTop - el.clientHeight < threshold;
      this.isUserAtBottom.set(atBottom);
    } catch {}
  }

  // Expose for template binding if needed
  onScroll(): void {
    this.updateUserScrollPosition();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    if (this.scrollListener) {
      const { el, listener } = this.scrollListener;
      el.removeEventListener('scroll', listener);
    }
    this.socketStore.setHasCallStartFlow(false);
  }

  private getUserInfo(): any {
    const savedUserInfo = localStorage.getItem('userInfo');
    return savedUserInfo ? JSON.parse(savedUserInfo) : null;
  }

  private handleSaveUserInfo(message: any): void {
    if (message && message.content && message.content.user_info) {
      localStorage.setItem(
        'userInfo',
        JSON.stringify(message.content.user_info)
      );

      this.author.data_info = message.content.user_info;
    }
  }
}
