// @ts-nocheck
import GoTo<PERSON>lockHandle from "@flow-editor-v1/components/flow/GoToBlockHandle";
import InputField from "@flow-editor-v1/components/form/InputField";
import SegmentedField from "@flow-editor-v1/components/form/SegmentedFiled";
import Icon from "@flow-editor-v1/components/icon/icon";
import NodeHeader from "@flow-editor-v1/components/nodes/NodeHeader";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor-v1/components/styled";
import NodeTooltip from "@flow-editor-v1/components/tooltip/NodeTooltip";
import { STUDIO_STATUS, } from "@flow-editor-v1/constant";
import { useFlowInstance } from "@flow-editor-v1/hook";
import { FlowDebugState, VariableLocatedState } from "@flow-editor-v1/model";
import {
  useBuildFlowState,
  useFlowDebugState,
  useFlowInstanceState,
  useLayoutState,
  useStudioState,
  useVariableLocatedState
} from "@flow-editor-v1/store";
import { isValidConnection } from "@flow-editor-v1/utils/flow";
import { extractVariableNames, hexToRgb } from "@flow-editor-v1/utils/util";
import { yupResolver } from "@hookform/resolvers/yup";
import { Modal } from "antd";
import _ from 'lodash';
import React, { useCallback, useEffect, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { Handle, Position } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";
import { VARIABLE_POSITION_TYPE } from "../../../constant/variable";
import { ImageNodeData } from "../../../model/image-node.model";

const StyledHandleSourceAnchor = styled.div<{ $bgColor; $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => (props.$data ? "white" : `rgba(255, 255, 255, 0.5)`)};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => (props.$data ? "normal" : "italic")};
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const imageListFormSchema = yup.object().shape({
  url_image: yup.string(),
  label_image: yup.string(),
});

const imageNodeDataFormSchema = yup.object().shape({
  image: yup
    .array()
    .of(imageListFormSchema)
    .required("Images is required")
    .default([
      {
        url_image: "",
        label_image: ""
      },
    ]),
  view_type: yup.string().required("View type is required").default("dx_gr"),
});

const CarouselNode = ({data}: { data: ImageNodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const {flowInstance} = useFlowInstanceState((state) => state);
  const {flow, setDirty} = useBuildFlowState(state => state);
  const {status} = useStudioState((state) => state);
  const {debug} = useFlowDebugState<FlowDebugState>(state => state);
  const {setVariableLocations} = useVariableLocatedState<VariableLocatedState>(state => state);
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const {theme} = useLayoutState(state => state);

  const {control, setValue, handleSubmit, reset} = useForm({
    resolver: yupResolver(imageNodeDataFormSchema),
  });

  const {
    fields: imageFields,
    append: imageAppend,
    remove: imageRemove,
    replace: imageReplace,
  } = useFieldArray({
    control,
    name: "image",
  });

  const handleAddImage = () => {
    imageAppend({
      url_image: "",
      label_image: ""
    });
  };

  const handleDeleteImage = (index: number) => {
    imageRemove(index);
  };

  const resetForm = () => {
    reset();
  };

  const buildGallery = (images, type) => {
    // Determine the tag based on the type
    const tag = type === "dx_gll" ? "dx_gll" : "dx_gr";
    let result = `<${tag}>\n`;

    images.forEach(item => {
      result += `"${item.url_image}", "${item.label_image}"\n`;
    });

    result += `</${tag}>`;

    return result;
  };

  const onSubmit = (formValue) => {
    data.image = buildGallery(_.cloneDeep(formValue.image), _.cloneDeep(formValue.view_type));
    data.imageSchema = _.cloneDeep(formValue.image);
    data.viewType = _.cloneDeep(formValue.view_type);

    const variableLocations = extractVariableNames(_.cloneDeep(data.image))?.map(name => ({
      position_type: VARIABLE_POSITION_TYPE.FLOW,
      flow_dev_id: flow.id,
      node_id: data.id,
      var_def_dev_name: name,
    }))

    setVariableLocations(variableLocations, VARIABLE_POSITION_TYPE.FLOW);

    resetForm();
    setModalOpen(false);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  };

  useEffect(() => {
    resetForm();
    if (
      data.image &&
      Array.isArray(data.imageSchema) &&
      data.imageSchema.length > 0 &&
      data.imageSchema.every(item =>
        item &&
        typeof item === 'object' &&
        item.hasOwnProperty('url_image') && item.url_image !== undefined && item.url_image !== null &&
        item.hasOwnProperty('label_image') && item.label_image !== undefined && item.label_image !== null
      )
    ) {
      imageReplace(data.imageSchema);
      data.image = buildGallery(_.cloneDeep(data.imageSchema), _.cloneDeep(data.viewType));
      setValue("view_type", _.cloneDeep(data.viewType))
    } else {
      imageReplace([
        {
          url_image: "",
          label_image: "",
        },
      ]);
    }
  }, [JSON.stringify(data), modalOpen]);

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    if (!data.debug) setModalOpen(true)
  }, [data.debug]);

  const isImageUrl = useCallback((url) => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  }, []);

  const isValidImageUrl = (url: string) => {
    const isImageUrl = /\.(jpeg|jpg|gif|png|svg|webp|bmp|tiff)$/i.test(url);
    const isVariablePlaceholder = /{{{(.*?)}}}/.test(url);
    if (isVariablePlaceholder || isImageUrl) {
      return null;
    }
    return "URL must point to an image or be a valid variable placeholder";
  };

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $debug={data.debug} $theme={theme}>
      {
        debug && (
          <div className="absolute left-0 text-[10px]" style={{top: -20, color: '#D4D9E2'}}>{data.id}</div>
        )
      }
      {status && status === STUDIO_STATUS.DEV && (
        <NodeTooltip data={data} selected={data.selected}/>
      )}
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
        {data.image ? (
          <div>
            {data.imageSchema.map((image) => {
              return (
                <StyledHandleSourceAnchor
                  key={`${image.url_image}`}
                  $bgColor={data.node_color}
                  $data={data.image}
                  onDoubleClick={handleOpenModal}
                >
                  <div className="w-full flex items-center justify-between space-x-3">
                    <img
                      className={"zoom rounded-md"}
                      src={isImageUrl(image.url_image) ? image.url_image : '/assets/img/default-img.png'}
                      alt={`${image.label_image}`} // Provide an alt text for accessibility
                      style={{width: "auto", height: 32, objectFit: 'cover'}} // Adjust the size and fit as needed
                    />
                    <div style={{fontSize: 8}} className="font-normal col-span-1">
                      {`${
                        image.label_image
                      } `}
                    </div>
                  </div>
                </StyledHandleSourceAnchor>
              );
            })}
          </div>
        ) : (
          <div>
            <StyledHandleSourceAnchor
              $bgColor={data.node_color}
              $data={data.image}
              onDoubleClick={handleOpenModal}
            >
              <div className="flex items-center justify-between space-x-3">
                <div style={{fontSize: 8}} className="font-normal">
                  URL
                </div>
                <div style={{fontSize: 8}}>Configure</div>
              </div>
            </StyledHandleSourceAnchor>
            <StyledHandleSourceAnchor
              $bgColor={data.node_color}
              $data={data.image}
              onDoubleClick={handleOpenModal}
            >
              <div className="flex items-center justify-between space-x-3">
                <div style={{fontSize: 8}} className="font-normal">
                  Label
                </div>
              </div>
            </StyledHandleSourceAnchor>
          </div>
        )}
        {
          !data.goToBlockSource ? (
            <Handle
              type="source"
              position={Position.Right}
              id={`${data.id}_source#1`}
              style={{
                height: 10,
                width: 10,
                backgroundColor: theme === 'dark' ? 'white' : 'black',
              }}
            />
          ) : (() => {
              const key = `${data.id}_source#1`
              const goToBlock = data.goToBlockSource.find(v => v.sourceHandle === key)
              if (goToBlock) {
                return <GoToBlockHandle
                  data={data}
                  sourceHandle={key}
                  style={{
                    height: 14,
                    width: 84,
                    right: -80,
                    backgroundColor: theme === 'dark' ? 'white' : 'black',
                    fontSize: 10,
                    color: theme === 'dark' ? 'black' : 'white',
                    border: 'none',
                    borderRadius: 10,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                />
              }

              return <Handle
                type="source"
                position={Position.Right}
                id={`${data.id}_source#1`}
                style={{
                  height: 10,
                  width: 10,
                  backgroundColor: theme === 'dark' ? 'white' : 'black',
                }}
              />
            }
          )()
        }
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-col space-y-4 mt-6"
        >
          <div className="flex items-center space-x-4">
            <label className="!text-neutral-content dark:!text-dark-neutral-content">View</label>
            <div className="flex items-center">
              <SegmentedField
                control={control}
                name={`view_type`}
                disabled={status && status === STUDIO_STATUS.LIVE}
                options={[
                  {
                    label: 'Grid',
                    value: 'dx_gr',
                  },
                  {
                    label: 'Gallery',
                    value: 'dx_gll',
                  },
                ]}
              />
            </div>
          </div>
          <div className="grid grid-cols-10 gap-4">
            <>
              <div className="col-span-6">
                <div className="!text-neutral-content dark:!text-dark-neutral-content">
                  URL <span style={{color: 'red'}}>*</span>
                </div>
              </div>
              <div className="col-span-3">
                <label className="!text-neutral-content dark:!text-dark-neutral-content">Label</label>
              </div>
              <div className="col-span-1">

              </div>
            </>
          </div>
          <div className="flex flex-col space-y-4">
            {imageFields.map((field, index) => (
              <div key={field.id} className="w-full flex flex-col space-y-3">
                <div className="w-full grid grid-cols-10 gap-4">
                  <div className="col-span-6">
                    <InputField
                      className="w-full"
                      style={{flexGrow: 1}}
                      type="input"
                      name={`image[${index}].url_image`}
                      control={control}
                      disabled={status && status === STUDIO_STATUS.LIVE}
                      setValue={setValue}
                      validate={isValidImageUrl}
                    />
                  </div>
                  <div className="col-span-3">
                    <InputField
                      className="w-full"
                      style={{flexGrow: 1, minWidth: 0}}
                      type="input"
                      name={`image[${index}].label_image`}
                      control={control}
                      disabled={status && status === STUDIO_STATUS.LIVE}
                      setValue={setValue}
                    />
                  </div>
                  <div className="col-span-1 flex items-center">
                    {status && status === STUDIO_STATUS.DEV && (
                      <div
                        className="hover:cursor-pointer flex items-end"
                        onClick={() => handleDeleteImage(index)}
                      >
                        <Icon
                          iconName={"RiDeleteBinLine"}
                          size={24}
                          style={{color: "#6F767E"}}
                        />
                      </div>
                    )}
                    </div>
                  </div>
              </div>
            ))}
          </div>
          <div
            className="flex gap-3 items-center hover:cursor-pointer"
            onClick={handleAddImage}
          >
            <Icon
              iconName={"RiAddCircleFill"}
              size={18}
              style={{color: data.node_color}}
            />
            <div style={{color: data.node_color}}>Add another image</div>
          </div>

          <div
            className="w-full flex justify-end items-center space-x-4"
            style={{marginTop: 24}}
          >
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() => setModalOpen(false)}
            >
              Cancel
            </StyledButtonModal>
            <StyledButtonModal
              type="submit"
              key="save"
              $bgColor={"#7F75CF"}
              $color={"white"}
              disabled={status && status === STUDIO_STATUS.LIVE}
            >
              Save
            </StyledButtonModal>
          </div>
        </form>
      </Modal>
    </StyledNodeFlow>
  );
};

export default CarouselNode;
