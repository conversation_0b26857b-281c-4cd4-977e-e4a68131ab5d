<div class="relative h-full flex flex-col">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex text-base-content dark:text-dark-base-content items-center text-2xl font-bold"
    >
      LLM api keys
    </div>
    <ng-icon
      name="heroXMark"
      class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
      (click)="dialogRef.close()"
    ></ng-icon>
  </div>

  <div class="mt-18 mb-20 px-6 pb-6 pt-[8px] space-y-3 max-h-[60vh] flex flex-col overflow-y-auto">

    <div class="flex items-center justify-end">
      <div class="flex items-center">
        <button
          dxButton="filled"
          class="bg-light-primary text-white h-[40px] mt-4 px-6 md:px-8 xl:px-6 2xl:px-8 rounded-lg"
          (click)="dropdownOpen = !dropdownOpen"
          cdkOverlayOrigin
          #trigger="cdkOverlayOrigin"
        >
          Add new
        </button>
        <ng-template
          cdkConnectedOverlay
          [cdkConnectedOverlayOrigin]="trigger"
          [cdkConnectedOverlayOpen]="dropdownOpen"
          [cdkConnectedOverlayPush]="true"
          [cdkConnectedOverlayPositions]="[
              {
                originX: 'start',
                originY: 'center',
                overlayX: 'end',
                overlayY: 'top',
                offsetY: 10,
              },
              {
                originX: 'start',
                originY: 'center',
                overlayX: 'end',
                overlayY: 'bottom',
                offsetY: 10,
              },
            ]"
        >
          <ul
            class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
            (clickOutside)="dropdownOpen = false"
          >
            <li>
              <button
                (click)="addOrEditLLM({ LLM_name: 'Open AI' }, data)"
                [disabled]="checkDuplicate('Open AI')"
                class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover w-full">
                Open AI
              </button>
            </li>
            <li>
              <button
                (click)="addOrEditLLM({ LLM_name: 'Gemini' }, data)"
                [disabled]="checkDuplicate('Gemini')"
                class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover w-full">
                Gemini
              </button>
            </li>

          </ul>
        </ng-template>

        <!--
                <div
                  class="absolute z-10 mt-2 flex flex-col p-2 gap-2 bg-light-secondary-background dark:bg-dark-secondary-background shadow-md text-light-text dark:text-dark-text"
                  [ngClass]="{ hidden: !dropdownOpen, block: dropdownOpen }"
                  (click)="dropdownOpen = false"
                >
                  <button
                    dxButton="elevated"
                    (click)="addOrEditLLM({ LLM_name: 'Open AI' }, data)"
                    [disabled]="checkDuplicate('Open AI')"
                  >
                    Open AI
                  </button>
                  <button
                    dxButton="elevated"
                    (click)="addOrEditLLM({ LLM_name: 'Gemini' }, data)"
                    [disabled]="checkDuplicate('Gemini')"
                  >
                    Gemini
                  </button>
                </div>
        -->
      </div>
    </div>

    <div class="flex items-center flex-col">
      <app-data-table
        class="w-full mt-4 min-h-[20vh]"
        [pageIndex]="0"
        [hiddenPaginator]="true"
        [rows]="listLLM()"
        [columns]="columnsLLM"
        [rowTemplate]="rowTemplate"
      >
        <ng-template #rowTemplate let-row="row" let-column="column">
          <ng-container [ngSwitch]="column.columnDef">
            <ng-container *ngSwitchCase="'created_at'">
              <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
                {{ row[column.columnDef] | date : "dd/MM/yyyy HH:mm:ss" }}
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="'updated_at'">
              <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
                {{ row[column.columnDef] | date : "dd/MM/yyyy HH:mm:ss" }}
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="'action_edit'">
              <div
                class="flex cursor-pointer"
                [ngStyle]="{ 'justify-content': column.align }"
              >
                <div class="text-red flex gap-2">
                  <ng-icon
                    name="heroPencilSquare"
                    size="24"
                    class="flex items-center justify-center"
                    (click)="addOrEditLLM(row, data)"
                  ></ng-icon>
                  <ng-icon
                    (click)="deleteLLM(row)"
                    name="heroTrash"
                    size="24"
                    class="flex items-center justify-center text-light-red"
                  ></ng-icon>
                </div>
              </div>
            </ng-container>
            <ng-container *ngSwitchDefault>
              <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
                {{ row[column.columnDef] }}
              </div>
            </ng-container>
          </ng-container>
        </ng-template>
      </app-data-table>
    </div>
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Close</button>
    <!--      <button-->
    <!--        class="bg-light-primary px-4 py-1 rounded-full text-light-white button-disabled"-->
    <!--        [disabled]="!newSubscription || newSubscription === ''"-->
    <!--        (click)="upgradePlan(data)"-->
    <!--      >-->
    <!--        Save-->
    <!--      </button>-->
  </div>
</div>
