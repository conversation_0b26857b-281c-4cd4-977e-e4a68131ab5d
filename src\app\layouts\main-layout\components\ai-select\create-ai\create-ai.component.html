<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100">
    <div class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content">
      Create AI
    </div>
    <div class="flex items-center justify-end space-x-4">
      <ng-icon name="heroXMark" class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
               (click)="closeDialogCreateAi()"></ng-icon>
    </div>
  </div>

  <div class="flex-1 overflow-y-auto mt-18 mb-20">
    <div [formGroup]="formGroupCreateAi"
         class="px-6 pt-6 pb-[3px] flex flex-col space-x-4 bg-base-200 dark:bg-dark-base-200">
      <dx-form-field class="w-full" id="name">
        <dx-label class="text-sm">Name</dx-label>
        <input dxInput formControlName="name" [type]="'text'" placeholder="Enter name"/>
        @if (formGroupCreateAi.get('name')?.errors && formGroupCreateAi.get('name')?.errors?.['required'] && (formGroupCreateAi.get('name')?.touched || formGroupCreateAi.get('name')?.dirty)) {
          <dx-error>Name is required.</dx-error>
        }
        @if (formGroupCreateAi.get('name')?.errors && formGroupCreateAi.get('name')?.errors?.['maxLength'] && (formGroupCreateAi.get('name')?.touched || formGroupCreateAi.get('name')?.dirty)) {
          <dx-error>Name must be less than 255 characters.</dx-error>
        }
      </dx-form-field>
      <dx-form-field class="w-full" id="description">
        <dx-label class="text-sm">Description</dx-label>
        <input dxInput formControlName="description" [type]="'text'" placeholder="Enter description"/>
        @if (formGroupCreateAi.get('description')?.errors && formGroupCreateAi.get('description')?.errors?.['required'] && (formGroupCreateAi.get('description')?.touched || formGroupCreateAi.get('description')?.dirty)) {
          <dx-error>Description is required.</dx-error>
        }
        @if (formGroupCreateAi.get('description')?.errors && formGroupCreateAi.get('description')?.errors?.['maxLength'] && (formGroupCreateAi.get('description')?.touched || formGroupCreateAi.get('description')?.dirty)) {
          <dx-error>Description must be less than 255 characters.</dx-error>
        }
      </dx-form-field>
    </div>
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200">
    <button dxButton="elevated" (click)="closeDialogCreateAi()">
      Cancel
    </button>
    <button dxLoadingButton="filled"
            [loading]="isCreating()"
            [disabled]="formGroupCreateAi.invalid"
            (click)="onCreateNewAi(formGroupCreateAi.value)">
      Save
    </button>
  </div>
</div>
