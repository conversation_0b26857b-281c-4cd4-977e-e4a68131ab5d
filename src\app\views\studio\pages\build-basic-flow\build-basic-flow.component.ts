import {
  AfterViewInit,
  Component,
  effect,
  ElementRef,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core';
import { Router } from '@angular/router';
import {
  APP_ROUTES,
  STUDIO_PATH,
  STUDIO_STATUS,
  TRIGGER_KEYS,
} from '@core/constants';
import { TriggerService } from '@core/services';
import { StudioStore, UIStore } from '@core/stores';
import { DxDialog } from '@dx-ui/ui';
import { IFlowDev } from '@shared/models';
import EditorBasicFlowView from '@views/flow-editor-v1/views/EditorBasicFlowView';
import { PublishFlowDialogComponent } from '@views/studio/components/publish-flow-dialog/publish-flow-dialog.component';
import * as React from 'react';
import { createRoot, Root } from 'react-dom/client';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-build-basic-flow',
  standalone: true,
  imports: [],
  templateUrl: './build-basic-flow.component.html',
  styleUrl: './build-basic-flow.component.css',
})
export class BuildBasicFlowComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  flowId = input.required<number>();

  rootId: string = 'flow-canvas';
  root: Root | undefined;
  protected destroy$ = new Subject<void>();

  private messageListener!: (event: MessageEvent) => void;

  private router: Router = inject(Router);
  private el: ElementRef = inject(ElementRef);
  private dialog = inject(DxDialog);
  private studioStore = inject(StudioStore);
  private uiStore = inject(UIStore);
  private triggerService = inject(TriggerService);

  private previousStatus: string | null = this.studioStore.status();

  private studioStoreEffect = effect(() => {
    const currentStatus = this.studioStore.status();
    if (currentStatus !== this.previousStatus) {
      this.previousStatus = currentStatus;
      void this.router.navigate(['/studio/build']);
    }
  });

  private themeEffect = effect(() => {
    const currentTheme = this.uiStore.theme();
    postMessage({
      type: 'theme',
      data: { theme: currentTheme },
    });
  });

  ngOnInit(): void {
    this.messageListener = (event) => {
      if (event && event?.data && event?.data.type === 'publish_flow') {
        this.publishFlow(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'revert_flow') {
        this.revertFlow(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'debug_mode') {
        this.studioStore.setFlowDebugMode(event.data.data);
      }
      if (
        event &&
        event?.data &&
        event?.data.type === 'back_to_list_basic_flow'
      ) {
        void this.router.navigateByUrl(
          `${APP_ROUTES.STUDIO}/${STUDIO_PATH.BASIC_FLOW}`
        );
      }
    };
    window.addEventListener('message', this.messageListener);

    this.triggerService.trigger(TRIGGER_KEYS.FLOW_UNMOUNTED, 'false');
  }

  ngAfterViewInit(): void {
    this.render();
    postMessage({
      type: 'studio_status',
      data: { status: this.studioStore.status() },
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    if (this.root) {
      this.root.unmount();
      this.root = undefined;
    }

    // Reset debug mode when component is destroyed
    this.studioStore.setFlowDebugMode(false);

    // Remove message listener
    window.removeEventListener('message', this.messageListener);

    this.triggerService.trigger(TRIGGER_KEYS.FLOW_UNMOUNTED, 'true');
  }

  private publishFlow(flow: IFlowDev) {
    if (!flow) return;
    if (this.studioStore.status() === STUDIO_STATUS.LIVE) return;
    if (this.studioStore.status() === STUDIO_STATUS.DEV) {
      this.dialog
        .open(PublishFlowDialogComponent, {
          width: '40dvw',
          data: {
            flow_id: flow.id,
            trigger_type: flow.trigger_type,
            isMultiple: false,
          },
        })
        .afterClosed()
        .subscribe((_) => {
          void this.router.navigateByUrl(
            `${APP_ROUTES.STUDIO}/${STUDIO_PATH.BASIC_FLOW}`
          );
          setTimeout(() => {
            this.studioStore.setStudioStatus(STUDIO_STATUS.LIVE);
          }, 200);
        });
    }
  }

  private revertFlow(flow: IFlowDev) {
    // Implementation for revert flow functionality
    console.log('Revert flow:', flow);
  }

  private render() {
    if (this.flowId()) {
      const reactRoot = this.el.nativeElement.querySelector(`#${this.rootId}`);
      if (reactRoot) {
        if (this.root) {
          this.root.unmount();
        }

        this.root = createRoot(reactRoot);
        this.root.render(
          React.createElement(EditorBasicFlowView, { flowId: this.flowId() })
        );

        postMessage({
          type: 'theme',
          data: { theme: this.uiStore.theme() },
        });
      }
    }
  }
}
