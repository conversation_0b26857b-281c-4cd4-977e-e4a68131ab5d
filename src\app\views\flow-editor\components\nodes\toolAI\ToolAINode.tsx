// @ts-nocheck
import GoTo<PERSON>lockHandle from "@flow-editor/components/flow/GoToBlockHandle";
import CheckboxField from "@flow-editor/components/form/CheckboxField";
import InputField from "@flow-editor/components/form/InputField";
import SelectField from "@flow-editor/components/form/SelectField";
import Slide<PERSON><PERSON>ield from "@flow-editor/components/form/SliderField";
import Icon from "@flow-editor/components/icon/icon";
import NodeHeader from "@flow-editor/components/nodes/NodeHeader";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor/components/styled";
import NodeTooltip from "@flow-editor/components/tooltip/NodeTooltip";
import { STUDIO_STATUS, TOOL_MODEL_LIST } from "@flow-editor/constant";
import { useFlowInstance } from "@flow-editor/hook";
import {
  useBuildFlowState,
  useFlowInstanceState,
  useLayoutState,
  useStudioState,
} from "@flow-editor/store";
import { isValidConnection } from "@flow-editor/utils/flow";
import { hexToRgb } from "@flow-editor/utils/util";
import { yupResolver } from "@hookform/resolvers/yup";
import { LayoutState, ToolAINodeData } from "@views/flow-editor/model";
import { Divider, Modal, Typography } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { Handle, Position } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";

const StyledHandleSourceAnchor = styled.div<{ $bgColor; $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => (props.$data ? "white" : `rgba(255, 255, 255, 0.5)`)};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => (props.$data ? "normal" : "italic")};
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const parametersFormControlSchema = yup.object().shape({
  key: yup.string().required("Param key is required"),
  description: yup.string().required("Param description is required")
});

const toolNodeDataFormSchema = yup.object().shape({
  system_prompt: yup.string().required("System prompt is required"),
  user_prompt: yup.string(),
  parameters: yup
    .array()
    .of(parametersFormControlSchema)
    .default([{key: "", description: ""}])
    .required("Parameters is required"),
  output_key: yup.string().required("Output key is required"),
  model: yup.string().required("Model is required").default('gpt-4o-mini'),
  temperature: yup.number().required("Temperature is required").default(0.1),
  use_history: yup.boolean().default(false)
});

const ToolAINode = ({data}: { data: ToolAINodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [isHoverGoToBlock, setIsHoverGoToBlock] = useState<boolean>(false);
  const {flowInstance} = useFlowInstanceState((state) => state);
  const {setDirty} = useBuildFlowState((state) => state);
  const {status} = useStudioState((state) => state);
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const {theme} = useLayoutState<LayoutState>(state => state);

  const {control, setValue, handleSubmit, reset} = useForm({
    resolver: yupResolver(toolNodeDataFormSchema),
  });

  const {
    fields: paramFields,
    append: paramAppend,
    remove: paramRemove,
    replace: paramReplace,
  } = useFieldArray({
    control,
    name: "parameters",
  });

  const handleAddParam = () => {
    paramAppend({key: "", description: ""});
  };

  const handleDeleteParam = (index: number) => {
    paramRemove(index);
  };

  const resetForm = () => {
    reset();
  };

  const onSubmit = (formValue) => {
    data.system_prompt = formValue?.system_prompt;
    data.user_prompt = formValue?.user_prompt;
    data.parameters = formValue?.parameters;
    data.output_key = formValue?.output_key;
    data.model = formValue?.model;
    data.temperature = formValue?.temperature;
    data.use_history = Boolean(formValue?.use_history);

    resetForm();
    setModalOpen(false);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  };

  useEffect(() => {
    resetForm();
    data.parameters && data.parameters.length
      ? paramReplace(data.parameters)
      : paramReplace([{key: "", description: ""}]);
    data.system_prompt && setValue("system_prompt", data.system_prompt);
    data.user_prompt && setValue("user_prompt", data.user_prompt);
    data.output_key && setValue("output_key", data.output_key);
    data.model && setValue("model", data.model);
    data.temperature && setValue("temperature", Number(data.temperature));
    data.use_history && setValue("use_history", Boolean(data.use_history));
  }, [JSON.stringify(data)]);

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    setModalOpen(true);
  }, []);

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $theme={theme}>
      <div className="absolute left-0 text-[10px]" style={{top: -20, color: '#D4D9E2'}}>{data.id}</div>
      {status && status === STUDIO_STATUS.DEV && !isHoverGoToBlock && (
        <NodeTooltip data={data} selected={data.selected}/>
      )}
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: "white",
          }}
        />
        <StyledHandleSourceAnchor
          $bgColor={data.node_color}
          $data={data.parameters || data.system_prompt}
          onDoubleClick={handleOpenModal}
        >
          {data.parameters.length > 0 || data.system_prompt
            ? data.system_prompt
            : "Configure"}
        </StyledHandleSourceAnchor>
        {
          !data.goToBlockSource ? (
            <Handle
              type="source"
              position={Position.Right}
              id={`${data.id}_source#1`}
              style={{
                height: 10,
                width: 10,
                backgroundColor: "white"
              }}
            />
          ) : (() => {
              const key = `${data.id}_source#1`
              const goToBlock = data.goToBlockSource.find(v => v.sourceHandle === key)
              if (goToBlock) {
                return <GoToBlockHandle
                  data={data}
                  sourceHandle={key}
                  style={{
                    height: 14,
                    width: 84,
                    right: -80,
                    backgroundColor: "white",
                    fontSize: 10,
                    color: 'black',
                    border: 'none',
                    borderRadius: 10,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onMouseEnter={() => setIsHoverGoToBlock(true)}
                  onMouseLeave={() => setIsHoverGoToBlock(false)}
                />
              }

              return <Handle
                type="source"
                position={Position.Right}
                id={`${data.id}_source#1`}
                style={{
                  height: 10,
                  width: 10,
                  backgroundColor: "white"
                }}
              />
            }
          )()
        }
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-col space-y-4 mt-6"
        >
          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
              System Prompt <span style={{color: 'red'}}>*</span>
            </Typography.Text>
            <InputField
              type={"textarea"}
              name={`system_prompt`}
              control={control}
              setValue={setValue}
            />
          </div>

          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
              User Prompt
            </Typography.Text>
            <InputField
              type={"textarea"}
              name={`user_prompt`}
              control={control}
              setValue={setValue}
            />
          </div>

          <div className="w-full grid grid-cols-2 items-center gap-5">
            <div className="col-span-1 w-full flex flex-col space-y-2">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Model <span style={{color: 'red'}}>*</span></Typography.Text>
              <SelectField
                name={"model"}
                disabled={status && status === STUDIO_STATUS.LIVE}
                control={control}
                options={TOOL_MODEL_LIST}
              />
            </div>

            <div className="col-span-1 w-full flex flex-col space-y-2">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Temperature <span style={{color: 'red'}}>*</span>
              </Typography.Text>
              <SliderField
                name={"temperature"}
                disabled={status && status === STUDIO_STATUS.LIVE}
                control={control}
                min={0}
                max={2}
                step={0.1}
                defaultValue={0}
              />
            </div>

            <div className="col-span-1 w-full flex space-x-2">
              <CheckboxField
                name={`use_history`}
                control={control}
                disabled={status && status === STUDIO_STATUS.LIVE}
              />
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Use history
              </Typography.Text>
            </div>
          </div>

          <Divider className="!border-primary-border dark:!border-dark-primary-border"/>

          <div className="flex flex-col">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Params</Typography.Text>
            {paramFields.map((field, index) => (
              <div
                key={field.id}
                className="flex items-center justify-between gap-4 mt-3"
              >
                <div className="w-full flex flex-col space-y-2">
                  <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                    Key <span style={{color: 'red'}}>*</span>
                  </Typography.Text>
                  <InputField
                    type={"input"}
                    name={`parameters[${index}].key`}
                    control={control}
                    disabled={status && status === STUDIO_STATUS.LIVE}
                    setValue={setValue}
                  />
                  <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                    Description <span style={{color: 'red'}}>*</span>
                  </Typography.Text>
                  <InputField
                    type={"textarea"}
                    name={`parameters[${index}].description`}
                    control={control}
                    disabled={status && status === STUDIO_STATUS.LIVE}
                    setValue={setValue}
                  />
                </div>

                {status && status === STUDIO_STATUS.DEV && (
                  <div
                    className="hover:cursor-pointer"
                    onClick={() => handleDeleteParam(index)}
                  >
                    <Icon
                      iconName={"RiDeleteBinLine"}
                      size={24}
                      style={{color: "#6F767E"}}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>

          {status && status === STUDIO_STATUS.DEV && (
            <div
              className="flex gap-3 items-center hover:cursor-pointer"
              onClick={handleAddParam}
            >
              <Icon
                iconName={"RiAddCircleFill"}
                size={18}
                style={{color: data.node_color}}
              />
              <div style={{color: data.node_color}}>Add another param</div>
            </div>
          )}

          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
              Output key <span style={{color: 'red'}}>*</span>
            </Typography.Text>
            <InputField
              type={"input"}
              name={`output_key`}
              control={control}
              setValue={setValue}
            />
          </div>

          <Divider className="!border-primary-border dark:!border-dark-primary-border"/>

          <div
            className="w-full flex justify-end items-center space-x-4"
            style={{marginTop: 24}}
          >
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() => setModalOpen(false)}
            >
              Cancel
            </StyledButtonModal>
            <StyledButtonModal
              type="submit"
              key="save"
              $bgColor={"#7F75CF"}
              $color={"white"}
              disabled={status && status === STUDIO_STATUS.LIVE}
            >
              Save
            </StyledButtonModal>
          </div>
        </form>
      </Modal>
    </StyledNodeFlow>
  );
};

export default ToolAINode;
