@import url('https://fonts.googleapis.com/icon?family=Material+Icons');
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');

@import 'reactflow/dist/style.css';
@import './_image.css';

@import "tailwindcss";

@config "../../tailwind.config.js";

@theme {
  --color-primary: #7241FF;
  --color-secondary: #1A1D1F;
  --color-secondary2: #008080;
  --color-red: #FF0000;
  --color-orange: #FFA500;
  --color-green: #008000;
  --color-turquoise: #40CECCFF;
  --color-gray-450: #80808080;


  /* Light mode colors */
  --color-light-text: var(--color-gray-700);
  --color-light-white: #FFFFFF;
  --color-light-black: #000000;
  --color-light-primary: #7241FF;
  --color-light-primary2: #5200FF;
  --color-light-secondary: #1A1D1F;
  --color-light-secondary2: #008080;
  --color-light-third: #40CECC4C;

  --color-light-red: #FF0000;
  --color-light-orange: #FFA500;
  --color-light-green: #008000;
  --color-light-gray: #BEC6CE;
  --color-light-background: #FFFFFFFF;
  --color-light-secondary-background: #F9F9F9FF;
  --color-light-inflate-background: #eaf5fb;
  --color-light-user-chat-background: #2A85FF;
  --color-light-hover: var(--color-gray-300);
  --color-light-border-line: var(--color-gray-200);
  --color-light-title: #717171;
  --color-light-info: #40cecc;

  --color-light-text-gray: #212529BF;
  --color-light-text-nav: #94A2B8;
  --color-light-plan-free: #A9A9A9;
  --color-light-plan-starter: #00BFFF;
  --color-light-plan-essential: #7241FF;
  --color-light-plan-business: #FFD700;

  /* Dark mode colors */
  --color-dark-text: #E0E0E0FF;
  --color-dark-white: #FFFFFF;
  --color-dark-black: #000000;
  --color-dark-primary: #7241FFCC;
  --color-dark-secondary: #1A1D1F;
  --color-dark-secondary2: #008080;
  --color-dark-third: #40CECC4C;
  --color-dark-red: #FF0000;
  --color-dark-green: #008000;
  --color-dark-gray: #2B313F;
  --color-dark-background: #121212;
  --color-dark-secondary-background: #1E1E1EFF;
  --color-dark-inflate-background: #272B30;
  --color-dark-user-chat-background: #2A85FF;
  --color-dark-hover: var(--color-gray-600);
  --color-dark-border-line: var(--color-gray-700);
  --color-dark-title: #94A2B8;
  --color-dark-primary-hover: #7A5CFF;
  --color-dark-text-gray: #94A2B8;
  --color-dark-text-nav: #94A2B8;
  --color-dark-plan-free: #A9A9A9;
  --color-dark-plan-starter: #00BFFF;
  --color-dark-plan-essential: #7241FF;
  --color-dark-plan-business: #FFD700;

  /*New colors scheme*/                                   /*New colors scheme(dark-mode)*/
  --color-base-100: #E2E3E8;                              --color-dark-base-100: #0D121E;
  --color-base-200: #EBEBEF;                              --color-dark-base-200: #1E232E;
  --color-base-300: #E3E3E8;                              --color-dark-base-300: #0D121E;
  --color-base-400: #F5F5F7;                              --color-dark-base-400: #343942;
  --color-base-content: #282826;                          --color-dark-base-content: #F8FAFF;
  --color-neutral-content: #6F767E;                       --color-dark-neutral-content: #ADAFB9;
  --color-primary-border: #D2D6DD;                        --color-dark-primary-border: #292E39;
  --color-primary: #7F75CF;                               --color-dark-primary: #7F75CF;
  --color-primary-hover: #675BC6;                         --color-dark-primary-hover: #9891CD;
  --color-primary-content: #FFFFFF;                       --color-dark-primary-content: #FDFDFD;
  --color-warning: #DAB674;                               --color-dark-warning: #DAB674;
  --color-warning-content: #614004;                       --color-dark-warning-content: #614004;
  --color-success: #9DC390;                               --color-dark-success: #9DC390;
  --color-success-content: #195305;                       --color-dark-success-content: #195305;
  --color-error: #E07272;                                 --color-dark-error: #E07272;
  --color-error-content: #5C0909;                         --color-dark-error-content: #5C0909;
  --color-info: #669EDD;                                  --color-dark-info: #669EDD;
  --color-info-content: #09305B;                          --color-dark-info-content: #09305B;
  --color-accent-200: #C0BBE8;                            --color-dark-accent-200: #C0BBE880;
  --color-accent-300: #E8BBBB;                            --color-dark-accent-300: #E8BBBB7F;
  --color-decoration-100: #FFFFFF7F;                      --color-dark-decoration-100: #FFFFFF14;
  --color-decoration-100-alt: #FFFFFF3F;                  --color-dark-decoration-100-alt: #FFFFFF07;
  --color-base-400-hover: #E5E5EB ;                       --color-dark-base-400-hover: #414854;
  --color-button-primary-bg-disabled: #B5B0DF;            --color-dark-button-primary-bg-disabled: #4F4C7F;
  --color-button-primary-bg-disabled-content: #F5F5F7;    --color-dark-button-primary-bg-disabled-content: #8E9096;
  --color-button-secondary-bg-disabled: #F0F0F3;          --color-dark-button-secondary-bg-disabled: #292E38;
  --color-button-secondary-bg-disabled-content: #89898A;  --color-dark-button-secondary-bg-disabled-content: #8B8F97;
  --color-gray: #BEC6CE;                                  --color-dark-gray: #2B313F;
  --color-gray-content: #676D73;                          --color-dark-gray-content: #909399;



  --font-sans: 'Plus Jakarta Sans', 'ui-sans-serif', 'system-ui', '-apple-system', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-body: 'Plus Jakarta Sans', 'ui-sans-serif', 'system-ui', '-apple-system', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-mono: 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace';

  --color-container-background: linear-gradient(122.32deg, #E2E3E8 70.48%, #D4D3E8 76.3%, #CAC7E8 80.41%, #C0BBE8 84.65%, #E8BBBB 99.98%);
  --color-dark-container-background: linear-gradient(122.32deg, #0D121E 70.48%, rgba(192, 187, 232, 0.5) 84.65%, rgba(232, 187, 187, 0.5) 99.98%);
}

@layer utilities {
  .h-screen-table {
    height: 90.9vh;
  }

  .error-message {
    @apply block text-red-500 font-medium pt-0.5 text-sm;
  }

  .bg-container-background {
    background: var(--color-container-background);
  }

  .bg-dark-container-background {
    background: var(--color-dark-container-background);
  }
}

:root {
  --dx-sidenav-container-background-light: linear-gradient(122.32deg, #E2E3E8 70.48%, #D4D3E8 76.3%, #CAC7E8 80.41%, #C0BBE8 84.65%, #E8BBBB 99.98%);
  --dx-sidenav-container-background-dark: linear-gradient(122.32deg, #0D121E 70.48%, rgba(192, 187, 232, 0.5) 84.65%, rgba(232, 187, 187, 0.5) 99.98%);
}

body, html {
  font-family: 'Plus Jakarta Sans', sans-serif !important;
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: none;
}

.dx-sidenav-content::-webkit-scrollbar {
  width: 0px;
  height: 4px;
}

.dx-sidenav-content::-webkit-scrollbar-track {
  background: transparent;
}

.dx-sidenav-content::-webkit-scrollbar-thumb {
  /* background: #D2D6DD; */
  border-radius: 10px;
  transition: all 0.3s ease;
}

.dx-sidenav-content::-webkit-scrollbar-thumb:hover {
  background: #ADAFB9;
}

.dx-sidenav-content::-webkit-scrollbar-thumb:active {
  background: rgba(75, 85, 99, 0.8);
}

.dx-sidenav-content::-webkit-scrollbar-track:hover::-webkit-scrollbar-thumb {
  background: #ADAFB9;
}

.dx-drawer-inner-container::-webkit-scrollbar {
  display: none;
}

.m-dx-drawer {
  --dx-sidenav-width: 100dvw;
}

.card-dashboard .dx-card.dx-card--outlined {
  height: 100% !important;
}

.card-integration:hover .dx-card.dx-card--outlined {
  background: var(--color-base-400);
  border-color: var(--color-primary-hover);
}

.dark .card-integration:hover .dx-card.dx-card--outlined {
  background: var(--color-dark-base-400);
  border-color: var(--color-dark-primary-hover);
}

.settings-tab-content {
  overflow-y: scroll;
  height: calc(100dvh - 180px);
}

.settings-tab-content::-webkit-scrollbar {
  display: none;
}

.tab-export-settings .dx-mdc-tab-body {
  border: none !important;
}

.tab-export-settings .dx-mdc-tab-body.dx-mdc-tab-body-active {
  border-radius: 0 !important;
}

.tab-api-detail .dx-mdc-tab-body {
  border: none !important;
}

.tab-api-detail .dx-mdc-tab-body.dx-mdc-tab-body-active {
  border-radius: 0 !important;
}

.ant-modal-title {
  color: var(--color-base-content);
}

.dark .ant-modal-title {
  color: var(--color-dark-base-content);
}

.ant-select-selector:after {
  @apply !bg-light-background;
  @apply dark:!bg-dark-background;
}

.ant-select-single {
  height: unset !important;
}

.ant-select-selector {
  @apply !bg-base-400 dark:!bg-dark-base-400 !border-primary-border dark:!border-dark-primary-border focus:!border-primary dark:focus:!border-dark-primary focus:!outline-4 focus:!outline-[#d2ceee] dark:focus:!outline-[#343942] placeholder-neutral-content dark:!placeholder-dark-neutral-content !text-base-content dark:!text-dark-base-content;
}

.ant-select-item-option-content {
  @apply !text-base-content dark:!text-dark-base-content;
}

.ant-select-single.ant-select-open .ant-select-selection-item {
  @apply !text-base-content dark:!text-dark-base-content;
}

.ant-select-single.ant-select-open .ant-select-selection-item:hover {
  @apply !bg-base-400-hover dark:!bg-dark-base-400-hover;
}

.ant-select-item.ant-select-item-option.ant-select-item-option-selected {
  @apply !bg-base-100 dark:!bg-dark-base-100;
}

.ant-select-dropdown {
  @apply !bg-base-400 dark:!bg-dark-base-400 !text-base-content dark:!text-dark-base-content;
}

.image-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000000;
}

.image-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000000;
}

img.zoom {
  margin-top: 1.2em;
  cursor: pointer;
  transition: transform 0.3s;
}

img.zoom.n-margin {
  cursor: pointer;
  transition: transform 0.3s;
}

.image-loader {
  width: 160px; /* Chiều rộng của khung ảnh */
  height: 160px; /* Chiều cao của khung ảnh */
  background: linear-gradient(
      90deg,
      #f0f0f0 25%,
      #e0e0e0 50%,
      #f0f0f0 75%
  );
  background-size: 200% 100%;
  border-radius: 8px; /* Góc bo tròn, thay đổi theo ý thích */
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.overlay {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}

.zoomed-image-container {
  position: relative;
  max-width: 90%;
  max-height: 90%;
}

.zoomed-image {
  max-width: 100%;
  max-height: 100%;
  transition: transform 0.3s;
  display: block;
}
