import { Component, inject, OnInit, signal } from '@angular/core';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import { UserAiStore } from '@core/stores';
import {
  DIALOG_DATA,
  DxButton,
  DxDialogRef,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxOption,
  DxSelect,
  DxSnackBar,
} from '@dx-ui/ui';
import { NgIcon, provideIcons } from '@ng-icons/core';
import { heroXMark } from '@ng-icons/heroicons/outline';
import { SelectOption } from '@shared/components';
import { TrimStringDirective } from '@shared/directives';
import {ICustomer, LeadsService} from '@shared/services';
import { ILead } from '@views/leads/leads.component';

@Component({
  selector: 'app-add-or-edit-lead',
  imports: [
    FormsModule,
    DxLabel,
    TrimStringDirective,
    DxInput,
    DxFormField,
    DxSelect,
    DxOption,
    DxButton,
    DxLoadingButton,
    NgIcon,
    ReactiveFormsModule,
  ],
  providers: [provideIcons({ heroXMark })],
  templateUrl: './add-or-edit-lead.component.html',
  styleUrl: './add-or-edit-lead.component.css',
  host: {
    class: 'h-full',
  },
})
export class AddOrEditLeadComponent implements OnInit {
  isLoadingSaveLead = signal<boolean>(false);

  leadStatusOptions: SelectOption[] = [
    {
      value: 'new',
      label: 'New',
    },
    {
      value: 'contacted',
      label: 'Contacted',
    },
    {
      value: 'qualified',
      label: 'Qualified',
    },
    {
      value: 'proposal',
      label: 'Proposal',
    },
    {
      value: 'negotiation',
      label: 'Negotiation',
    },
    {
      value: 'closed_won',
      label: 'Closed Won',
    },
    {
      value: 'closed_lost',
      label: 'Closed Lost',
    },
  ];

  dialogRef = inject(DxDialogRef<AddOrEditLeadComponent>);
  data: {
    lead?: ILead;
    isEdit: boolean;
  } = inject(DIALOG_DATA);
  private snackBar = inject(DxSnackBar);
  private leadsService: LeadsService = inject(LeadsService);
  private userAiStore = inject(UserAiStore);

  leadData: ILead = {
    user_name: '',
    phone_number: '',
    email: '',
    user_id: '',
    status: 'new',
    data_info: '{}',
    ai_id: this.userAiStore.currentAi()?.id,
  };

  formGroup: FormGroup = inject(FormBuilder).group({
    user_name: [this.leadData.user_name,[Validators.required]],
    phone_number: [this.leadData.phone_number],
    email: [this.leadData.email],
    user_id: [this.leadData.user_id,[Validators.required]],
    status: [this.leadData.status],
    data_info: [this.leadData.data_info],
    ai_id: [this.leadData.ai_id],
  });

  ngOnInit() {
    if (this.data.isEdit && this.data.lead) {
      this.leadData = this.data.lead;
    }
    this.formGroup.patchValue(this.leadData);
  }

  saveLead(data: any) {
    if (!this.canSave()) return;

    // Validate JSON format before saving
    if (!this.validateAndFormatDataInfo()) {
      this.showSnackBar(
        'Invalid JSON format in Additional Information',
        'error'
      );
      return;
    }

    this.isLoadingSaveLead.set(true);

    const body: Partial<ICustomer> = {
      ...this.formGroup.value
    };
    if (data.isEdit && data.lead?.user_id) {
      // Update existing lead
      this.leadsService
        .updateCustomer(data.lead.user_id, body)
        .subscribe({
          next: (response) => {
            this.showSnackBar('Lead updated successfully', 'success');
            this.closeLeadDialog();
          },
          error: (error) => {
            this.showSnackBar(error.error.detail, 'error');
            this.isLoadingSaveLead.set(false);
          },
        });
    } else {
      // Create new lead
      this.leadsService.createCustomer(body).subscribe({
        next: (response) => {
          this.showSnackBar('Lead created successfully', 'success');
          this.closeLeadDialog();
        },
        error: (error) => {
          this.showSnackBar(error.error.detail, 'error');
          this.isLoadingSaveLead.set(false);
        },
      });
    }
  }

  closeLeadDialog() {
    this.dialogRef.close();
  }

  canSave() {
    return this.formGroup.valid && !this.isLoadingSaveLead() && this.validateAndFormatDataInfo();
  }

  validateAndFormatDataInfo(): boolean {
    if (!this.formGroup.value.data_info || this.formGroup.value.data_info.trim() === '') {
      this.leadData.data_info = '{}';
      return true;
    }

    try {
      // Try to parse and re-stringify to ensure valid JSON
      const parsed = JSON.parse(this.formGroup.value.data_info);
      this.formGroup.value.data_info = JSON.stringify(parsed);
      return true;
    } catch (error) {
      return false;
    }
  }

  private showSnackBar(message: string, type: 'success' | 'error') {
    if (type === 'success') {
      this.snackBar.open(message, '', {
        panelClass: 'dx-snack-bar-success',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }

    if (type === 'error') {
      this.snackBar.open(message, '', {
        panelClass: 'dx-snack-bar-error',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }
  }
}
