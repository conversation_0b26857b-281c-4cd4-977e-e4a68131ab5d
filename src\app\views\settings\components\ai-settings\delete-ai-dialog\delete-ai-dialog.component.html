  <div class="h-full relative flex flex-col rounded-3xl">
    <div
      class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
    >
      <div
        class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
      >
        Delete AI Assistant
      </div>
      <div class="flex items-center justify-end space-x-4">
        <app-svg-icon type="icClose" class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          (click)="this.dialogRef.close()"
        ></app-svg-icon>
      </div>
    </div>

    <div
      class="flex-1 mt-18 mb-20 overflow-y-auto flex flex-col space-y-4 px-6 py-5"
    >
      <div class="" [formGroup]="formGroupConfirmDeleteAI">
        <p class="mb-4 text-error">{{ data.content }}</p>

        @if (data.canDelete) {
          <dx-form-field>
            <dx-label>Confirm by typing the name of this AI Assistant:</dx-label>
            <input
              dxInput
              placeholder="Confirm AI's name"
              formControlName="nameConfirm"
            />
            @if (this.formGroupConfirmDeleteAI.errors && this.formGroupConfirmDeleteAI.errors['mustMatch']
            && (this.formGroupConfirmDeleteAI.dirty || this.formGroupConfirmDeleteAI.touched)) {
              <dx-error>Name do not match</dx-error>
            }
          </dx-form-field>
        }
      </div>
    </div>

    <div
      class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
    >
      <button dxButton="elevated" (click)="this.dialogRef.close()">Close</button>
      <button
        dxButton="filled"
        class="text-white"
        [style.--dx-button-filled-container-color]="'#E07272'"
        [style.--dx-button-filled-hover-container-color]="'#E38080'"
        [style.--dx-button-filled-disabled-container-color]="'#E8BBBB'"
        [disabled]="
          !data.canDelete ||
          (data.canDelete && formGroupConfirmDeleteAI.invalid)
        "
        (click)="deleteAi()"
      >
        Delete
      </button>
    </div>
  </div>
